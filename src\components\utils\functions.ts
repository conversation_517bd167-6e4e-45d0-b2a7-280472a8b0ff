import { $isAtNodeEnd } from "@lexical/selection";
import ED_SETTING from "../../types";
import { businessUserLimits, premiumUserLimits } from "./limits";
import toast from "@minieditor/react-hot-toast";
import { CanvasSnipperPlugin, RendererUiPlugin, ViewerApp, downloadBlob, timeout } from "webgi";
import { IjewelEditorPlugin } from "../viewer/IjewelEditorPlugin";

function randomIntFromInterval(min: number = 0, max: number) {
  // min and max included
  return Math.floor(Math.random() * (max - min + 1) + min);
}

const getRandomPlaceholder = () => {
  const placeholders = [
    {
      title: "Elegant Sapphire Cascade: A Necklace of Flowing Blue Brilliance",
      description:
        "3D-designed sapphire necklace featuring cascading blue gems. Timeless elegance meets modern artistry.",
    },
    {
      title: "Whimsical Butterfly Ring: Rose Gold and Diamond Fantasy",
      description:
        "Delicate butterfly ring in rose gold, adorned with diamonds. 3D-crafted to capture nature's beauty.",
    },
    {
      title: "Geometric Harmony: Minimalist Platinum Earrings",
      description:
        "Clean lines meet subtle curves in these 3D-designed platinum earrings. Contemporary chic for any occasion.",
    },
    {
      title: "Emerald Infinity: A Timeless Symbol of Endless Love",
      description:
        "3D-modeled emerald and white gold bracelet. The infinity symbol reimagined with vibrant green stones.",
    },
    {
      title: "Art Deco Revival: Pearl and Onyx Cocktail Ring",
      description:
        "Bold 3D-designed cocktail ring inspired by the Roaring Twenties. Freshwater pearl meets sleek black onyx.",
    },
  ];

  return placeholders[randomIntFromInterval(0, placeholders.length - 1)];
};

function getSelectedNode(selection: any) {
  const anchor = selection.anchor;
  const focus = selection.focus;
  const anchorNode = selection.anchor.getNode();
  const focusNode = selection.focus.getNode();
  if (anchorNode === focusNode) {
    return anchorNode;
  }
  const isBackward = selection.isBackward();
  if (isBackward) {
    return $isAtNodeEnd(focus) ? anchorNode : focusNode;
  } else {
    return $isAtNodeEnd(anchor) ? focusNode : anchorNode;
  }
}
function positionEditorElement(editor: HTMLDivElement, rect: any) {
  if (rect === null) {
    editor.style.opacity = "0";
    editor.style.top = "-1000px";
    editor.style.left = "-1000px";
  } else {
    editor.style.opacity = "1";
    editor.style.left = `0px`;

    const parentRect =
      editor.parentElement?.parentElement?.getBoundingClientRect();

    editor.style.top = `${
      rect.top + rect.height - (parentRect?.top ?? rect.top) + 10
    }px`;

    // origiinal positioning, not usefull for our use case
    // editor.style.top = `${rect.top + rect.height + window.pageYOffset + 10}px`;
    // editor.style.left = `${
    //   rect.left + window.pageXOffset - editor.offsetWidth / 2 + rect.width / 2
    // }px`;
  }
}

export function isVersionLess(version1 : string, version2 :string) {
  const v1Parts = version1.split('.').map(Number);
  const v2Parts = version2.split('.').map(Number);

  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
      const part1 = v1Parts[i] || 0; // Default to 0 if undefined
      const part2 = v2Parts[i] || 0; // Default to 0 if undefined

      if (part1 < part2) {
          return true;
      }
      if (part1 > part2) {
          return false;
      }
  }

  return false; // Versions are equal
}

function moveCustomInFront(array: ED_SETTING.CUSTOM_ASSET_ITEM[]) {
  return array.sort((a, b) => {
    // Check if either `a` or `b` has `isCustom: true`
    const aIsCustom = a.isCustom === true ? 0 : 1;
    const bIsCustom = b.isCustom === true ? 0 : 1;

    // Sort based on `isCustom`
    return aIsCustom - bIsCustom;
  });
}

async function isCustomScenePerset(preset: Record<string, any> | string) {
  let isJson = false;
  if (typeof preset === "string") {
    isJson = preset.endsWith(".json");
  } else if (typeof preset === "object" && preset !== null) {
    isJson = preset.path ? preset.path.endsWith(".json") : false;
  }

  if (!isJson) return { isValid: false, file: null };
  else {
    const json = await fetch((preset as any).path).then((res) => res.json());
    return { isValid: json.type === "IjewelEditorPlugin", file: json };
  }
}

async function generateCustomSceneSettingFile(viewer: ViewerApp) {
  if (!viewer) throw new Error("Viewer not found");

  const editorPlugin = viewer.getPluginByType<IjewelEditorPlugin>("IjewelEditorPlugin")!;
  if (!editorPlugin) throw new Error("Editor plugin not found");

  let pjConfig = editorPlugin.getConfig();

  let customSceneSetting: any = {
    type: "IjewelEditorPlugin",
    version: pjConfig?.version,
    shouldApplyModelStageConfig: true,
    cameraConfig: await viewer.scene.activeCamera.toJSON(),
  };
  if (pjConfig?.plugins) customSceneSetting.plugins = pjConfig.plugins;
  if (pjConfig?.sceneConfig) customSceneSetting.sceneConfig = pjConfig.sceneConfig;
  if (pjConfig?.materialConfig) customSceneSetting.materialConfig = pjConfig.materialConfig;
  if (pjConfig?.embedSettings) customSceneSetting.embedSettings = pjConfig.embedSettings;

  // remove custom scene from sceneConfig
  if (customSceneSetting.sceneConfig?.VJSON && (await isCustomScenePerset(customSceneSetting.sceneConfig.VJSON)).isValid) {
    customSceneSetting.sceneConfig.VJSON = null;
  }
  const json = JSON.stringify(customSceneSetting, null, 2);
  const blob = new Blob([json], { type: "application/json" });
  const file = new File([blob], `sp_custom_${Math.floor(Math.random() * 1000)}.json`, {
    type: "application/json"
  });

  return { file, customSceneSetting };
}

const canUplaodPresetOrMaterial = (params: {
  customAssetsStore?: Record<string, ED_SETTING.CUSTOM_ASSET_ITEM[]> | null;
  isBusiness?: boolean;
  isPremium?: boolean;
  isLogin?: boolean;
  setShowSubscribeModal?: any;
}) => {
  const totalMaterialsAndPresets = Object.values(params.customAssetsStore ?? {}).flat().length;
  const premiumUserLmt = premiumUserLimits.customAsset.uploadPresetLimit;
  const businessUserLmt = businessUserLimits.customAsset.uploadPresetLimit;

  const isPremiumUserLimitReached = !params.isBusiness && params.isPremium && totalMaterialsAndPresets >= premiumUserLmt;
  const isBusinessUserLimitReached = params.isBusiness && totalMaterialsAndPresets >= businessUserLmt;

  let isPassed = true,
    msg = "";

  if (!params.isLogin) {
    isPassed = false;
    msg = "Please login to upload assets";
  } else if (!params.isBusiness && !params.isPremium) {
    isPassed = false;
    msg = "Upgrade to paid plan to upload assets";
    params.setShowSubscribeModal?.("business");
  } else if (isPremiumUserLimitReached) {
    isPassed = false;
    msg = "Maximum number of custom assets upload limit reached, upgrade to higher plan to upload more";
    params.setShowSubscribeModal?.("business");
  } else if (isBusinessUserLimitReached) {
    isPassed = false;
    msg = "Maximum number of custom assets upload limit reached, contact us for more";
  }

  if (!isPassed) {
    toast.error(msg, {
      id: "asset-upload-error",
      duration: 5000,
    });
  }
  return isPassed;
};

export { isCustomScenePerset, canUplaodPresetOrMaterial, getRandomPlaceholder, getSelectedNode, positionEditorElement, moveCustomInFront, generateCustomSceneSettingFile };
