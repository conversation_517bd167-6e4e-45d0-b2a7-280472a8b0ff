# @ijewel3d/mini-editor
This is a webgi react wrapper to edit 3d files on ijewel design and other products.


## Deployment
This project uses a custom deployment script alongside GitHub Actions to publish new versions.
### steps
1- Bump Version : Update package.json version.
2- Add Changelog : Add an entry in CHANGELOG.md
3- Create Tag : Push a new Git tag starting with `v` and the version number (e.g., v1.2.3)

After pushing the tag it will create a new release in the following links:
- `https://releases.ijewel3d.com/libs/web/ijewel3d-mini-editor-{version-number}.tgz`
- `https://releases.ijewel3d.com/libs/mini-editor/{version-number}/bundle.iife.js`