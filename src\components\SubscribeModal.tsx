import { FC, CSSProperties, useState } from "react";
import { But<PERSON> } from "@nextui-org/react";
import Carousel from "./Carousel";
import { Link } from "@nextui-org/react";

interface SubscribeModalProps {
  className?: string;
  children?: any;
}

const SubscribeModal: FC<SubscribeModalProps> = (props) => {
  const [active, setActive] = useState(0);

  return (
    <div className="w-full flex flex-col">
      <Carousel
        setActive={setActive}
        images={sections.map((section) => ({ imageSrc: section.image }))}
      />
      <div className="p-unit-2xl flex flex-col bg-white">
        <p className="text-xl font-mainSemiBold">{sections[active]?.title}</p>
        <p className="text-small mt-unit-md shrink-0 h-[calc(1.4rem*3)]">
          {sections[active]?.description}
        </p>
        <div className="flex w-full mt-unit-2xl gap-unit-md">
          <Button
            href="https://ijewel.design/pricing"
            as={Link}
            className="text-small py-4"
            size="lg"
            isExternal
            color="default"
            fullWidth
          >
            Compare Plans
          </Button>
          <Button
            href="https://ijewel.design/pricing"
            as={Link}
            className="text-small py-4"
            size="lg"
            isExternal
            color="primary"
            fullWidth
          >
            Upgrade to Premium
          </Button>
        </div>
      </div>
    </div>
  );
};

const sections = [
  {
    image: "https://assets.ijewel.design/images/sub1.webp",
    title: "Upload up to 100 models",
    description:
      "You can upload more files to expand and enhance your portfolio, presenting a diverse range of ideas and showcasing your creativity and versatility.",
  },
  {
    image: "https://assets.ijewel.design/images/sub2.webp",
    title: "Have full access to all Scene Presets",
    description:
      "Choose from a variety of scene presets to give your project a unique look. Quickly find the perfect atmosphere for your project with our selection of scenes.",
  },
  {
    image: "https://assets.ijewel.design/images/sub3.webp",
    title: "Access multiple material variations",
    description:
      "Explore various material options, each offering distinct aesthetic and functional qualities, to create personalized and innovative designs that stand out.",
  },
  {
    image: "https://assets.ijewel.design/images/sub4.webp",
    title: "Annotations",
    description:
      "Easily revisit specific angles and ensure you highlight the most important aspects of your work with our annotation feature.",
  },
  {
    image: "https://assets.ijewel.design/images/sub5.webp",
    title: "Export images and videos",
    description:
      "Export your designs effortlessly in a variety of formats, making it simple to share your work with others.",
  },
];


export { SubscribeModal };
