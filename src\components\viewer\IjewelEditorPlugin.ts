import {
  AViewerPlugin,
  AWSClientPlugin,
  AssetExporterPlugin,
  FileTransferPlugin,
  GLTFDracoExportPlugin,
  MaterialConfiguratorPlugin,
  ViewerApp,
  downloadBlob,
  MaterialPresetPlugin,
  PresetLibraryPlugin,
  IAsset,
  Euler,
  Vector3,
  ModelStagePlugin,
  IModel
} from "webgi";
import {AssetsLibrary, DriveFile, EditorSettings, Preset, Project} from "../../main";
// import { MaterialConfiguratorPlugin2 } from "./app";

export class IjewelEditorPlugin extends AViewerPlugin<"updatedAssets"> {
  public static readonly PluginType = "IjewelEditorPlugin";
  public project: Project | undefined;
  enabled = true;
  hasEdited = false;
  basePath: string | undefined;
  static fileCache = new Map()
  assetFiles: DriveFile[] = [];
  defaults : {sceneConfig: any, materials: any, ConfiguratorConfig: any} = {sceneConfig: {}, materials: [], ConfiguratorConfig: {}}

  editorSettings: EditorSettings | undefined;

  serializeWithViewer =false

  private presetPlugin: PresetLibraryPlugin | undefined;
  private materialPlugin: MaterialPresetPlugin | undefined;

  async onAdded(viewer: ViewerApp): Promise<void> {
    await super.onAdded(viewer);

    this.presetPlugin = await this._viewer!.addPlugin(PresetLibraryPlugin);
    this.materialPlugin = await this._viewer?.getOrAddPlugin(MaterialPresetPlugin);
  }

  // Imports data from exportPresets()
  async fromJSON(data: any, meta?: any): Promise<this | null> {
    if (!super.fromJSON({ data, type: "IjewelEditorPlugin" }, meta)) return null;
    this.project = { ...this.project, ...data };
    return this;
  }

  // Exports data to be imported by fromJSON()
  toJSON(meta?: any): any {
    return {
      ...this.project,
      type: "IjewelEditorPlugin",
    };
  }

  async init(basePath : string , editorSettings?: EditorSettings,project?: Project) {
    

    if (!this._viewer || !this.presetPlugin || !this.materialPlugin) return;
    this.editorSettings = editorSettings;
    this.basePath = basePath;
    this.project = project ?? this.project;
    let library: AssetsLibrary | undefined;
    let versionNumber = parseInt(this.project?.version?.split("v")[1] ?? "0");
    if(editorSettings?.assetFiles || versionNumber > 3 ){
      this.assetFiles = editorSettings?.assetFiles ?? [];
      library = this.processFiles(this.assetFiles);
    }else {
      library = await this.loadLib(basePath, editorSettings?.assetLib, "ijewel-assets");
    }

    if(library){
      //v2
      const presets = library?.presets ? this.preprocessData(library.presets, basePath) : {};
      const materials = library?.materials ? this.preprocessData(library.materials, basePath) : {};
      this.presetPlugin.loadPresetGroups(presets);
      this.materialPlugin.loadPresets(materials, basePath);
      this.setDefaults();
    }else {
      if(versionNumber>1) return;
      //v1
      let pLib = await this.loadLib(basePath, editorSettings?.presetLib, "presetLib");
      if(pLib){
        pLib = this.preprocessData(pLib, basePath);
        this.presetPlugin.clear()
        this.presetPlugin.loadPresetGroups(pLib);
      }else{
        console.error("No preset library found");
      }

      //material library
      let mLib = await this.loadLib(basePath, editorSettings?.materialLib, "materialLib");
      if(mLib){
        mLib = this.preprocessData(mLib, basePath);
        this.materialPlugin.loadPresets(mLib, basePath);
      }else{
        console.error("No material library found");
      }
    }
    
    this.dispatchEvent({type : "updatedAssets"})
  }

  isMaterialConfigurator = () : boolean => {
    if (!this._viewer) return false;
    const configuratorPlugin = this._viewer.getPluginByType("MaterialConfiguratorPlugin") as MaterialConfiguratorPlugin;
    const flag =  configuratorPlugin && configuratorPlugin.variations.length > 0;
    return Boolean(flag) || Boolean(this.project?.plugins?.["materialConfiguratorPlugin"]);
  }

  isVjson = () => {
    if (!this._viewer) return false;
    const object = this._viewer.scene.modelRoot.children[0] as any;
    return (object && (object.__importedViewerConfig || (object as IModel).modelObject?.__importedViewerConfig))
            || this.project?.sceneConfig?.VJSON
  }

  isMaterialMapping = () : boolean => {
    if (!this._viewer) return false;
    return (this.project?.materialConfig || this.isVjson() || this.isMaterialConfigurator()) && this.isModelLoaded()
  }

  isModelLoaded = () : boolean => {
    if (!this._viewer) return false;
    return this._viewer.scene.modelRoot.children.length > 0 
  }

  async applyConfig() {
    let viewer = this._viewer;
    if (!viewer || !this.materialPlugin || !this.presetPlugin || !this.project) return;
    viewer.scene.fixedEnvMapDirection = true;

    if(this.isModelLoaded() && !this.isVjson() && !this.isMaterialMapping() 
      && !this.isMaterialConfigurator() && this.editorSettings?.useDefaultConfig){
      await this.applyDefaultConfig();
    }
    if (this.project.sceneConfig){
      const sceneConfig = this.makeAbsolute(this.project.sceneConfig, this.basePath ?? "");
      sceneConfig.type = "PresetLibraryPlugin";
      //remove preImported from presets
      Object.keys(sceneConfig).forEach((key) => {
        if(sceneConfig[key] && sceneConfig[key]["preImported"]){
          delete sceneConfig[key]["preImported"];
        }
      });
      await this.presetPlugin.fromJSON(sceneConfig);
    }

    if (this.project.plugins) await this.loadPlugins(this.project.plugins);

    this.materialPlugin.applyOnLoad = true;
    this.materialPlugin.basePath = this.basePath //just in case it's not set earlier 
    if (this.project.materialConfig){
      let configuratorMaterials : any[] | undefined = []
      if(this.project.materialConfig.mapping){
        //extract the selected materials from the materialConfig.mapping in the form MaterialConfiguratorPlugin:index
        //todo: this should be added to MaterialPresetPlugin
        configuratorMaterials = this.project.materialConfig.mapping?.filter((material: { name: string; path: string; }) => 
          material.path.startsWith("MaterialConfiguratorPlugin")
        );      
  
        //rmeove the configurator materials from the materialConfig
        this.project.materialConfig.mapping = this.project.materialConfig.mapping?.filter((material: { name: string; path: string; }) =>
          !material.path.startsWith("MaterialConfiguratorPlugin")
        );

      }

      await this.materialPlugin.fromJSON({
        ...this.project.materialConfig,
        type: "MaterialPresetPlugin",
      });

      if(configuratorMaterials){
        //add the configurator materials back
        this.project.materialConfig.mapping = this.project.materialConfig.mapping?.concat(configuratorMaterials);
        configuratorMaterials?.forEach((material: { name: string; path: string; }) => {
          this.materialPlugin?.addMapping(material.name, material.path , false)
        })
        
        //apply the configurator materials
        if(configuratorMaterials?.length){
          const configuratorPlugin = viewer.getPluginByType("MaterialConfiguratorPlugin") as MaterialConfiguratorPlugin;
          configuratorMaterials.forEach((material: { name: string; path: string; }) => {
            const index = parseInt(material.path.split(":")[1]);
            const variation = configuratorPlugin.variations.find((v) => v.uuid === material.name);
            if (variation && typeof index === "number") {
              configuratorPlugin.applyVariation(variation, index);
            }
          }); 
        }
      }
    }
    if (this.project.cameraConfig) viewer.scene.activeCamera.fromJSON(this.project.cameraConfig);
    
    if(this.project.currentPose) {
      const position = this.project.currentPose.position;
      const rotation = this.project.currentPose.rotation;
      viewer.scene.modelRoot.children[0].position.copy(new Vector3(...position));
      viewer.scene.modelRoot.children[0].rotation.copy(new Euler(...rotation));
    }
    //model stage config
    //todo: there should be a flag in webgi for this 
    if(this.project.sceneConfig?.ModelStage){
      if(this.project.shouldApplyModelStageConfig){
        const modelStagePlugin = viewer.getPlugin(ModelStagePlugin)  as ModelStagePlugin;
        if(modelStagePlugin){
          await modelStagePlugin.importStageViewerConfig()
        }
      }

      const modelStageGroup = this.presetPlugin.presetGroups.find((group) => group.name === "ModelStage");
      if(modelStageGroup){
        modelStageGroup.selected = this.project.sceneConfig.ModelStage
      }
    }
  }

  async setDefaults() {
    let viewer = this._viewer;
    if (!viewer || !this.materialPlugin || !this.presetPlugin || !this.assetFiles?.length) return;
    let library = this.processFiles(this.assetFiles);
    let presets = this.preprocessData(library?.presets, "");
    let sceneConfig = {};
    //presets
    Object.keys(presets).forEach((key) => {
      const p = presets[key].find((preset) => preset.isDefault);
      if(p) sceneConfig = { ... sceneConfig , [key]: p.path };
    });
    if(Object.keys(sceneConfig).length > 0){
      sceneConfig = this.makeAbsolute(sceneConfig, this.basePath ?? "");
    }

    //materails
    const defaultMaterials = this.assetFiles.filter((file) => file.tags.includes("_layer:"));

    const materialConfig : {name:string, path:string}[] = [];
    defaultMaterials.map(file => {
      const tags = file.tags.split(',');
      const layerTags = tags.filter(tag => tag.startsWith('_layer:'));
      const layerNames = layerTags.map(tag => tag.split(':')[1]);
      layerNames.forEach((name) => {
        materialConfig.push({ name, path: this.basePath + file.file });
      });
    });

    this.defaults = {sceneConfig: sceneConfig, materials : materialConfig, ConfiguratorConfig: {}}
  }

  async applyDefaultConfig(presets =true, materails = true) {
    let viewer = this._viewer;
    if (!viewer || !this.materialPlugin || !this.presetPlugin || !this.assetFiles?.length) return;
    if(!presets && !materails) return;

    const sceneConfig = this.defaults.sceneConfig;
    const materialConfig = this.defaults.materials;

    if(Object.keys(sceneConfig).length > 0 && presets){
      await this.presetPlugin.fromJSON({...sceneConfig , type: "PresetLibraryPlugin"});
    }

    if(materialConfig.length > 0 && materails){
      await this.materialPlugin.fromJSON({materials :materialConfig , type: "MaterialPresetPlugin"});
    }

    if (!this.project) this.project = {}
    this.project.sceneConfig = sceneConfig;
    this.project.materialConfig = { materials : materialConfig };

  }


  async fetcher(url: RequestInfo | URL , init?: RequestInit){
    const dynamic = new URLSearchParams(window.location.search).get("cache") === "false";
    const prevUrl = url

    if (dynamic) {
      url += "?t" + Date.now();
    }
    if(IjewelEditorPlugin.fileCache.has(prevUrl)){
      return IjewelEditorPlugin.fileCache.get(prevUrl);
    }
    const json = await((await fetch(url, init)).json());
    IjewelEditorPlugin.fileCache.set(prevUrl,json);
    return json;
  }


  async loadLib(basePath: string, lib: object | string | undefined, name: string) : Promise<any> {
    if (typeof lib === "string" && lib) {
      try {
        lib = await this.fetcher(lib);
      } catch (e) {
        lib = undefined;
      }
    }
    if (typeof lib === "undefined") {
      try {
        lib = (await this.fetcher(basePath + name + ".json"))
      } catch (e) {
        lib = undefined;
      }
    }
    return lib;
  }

  getConfig = (saveView?: boolean): Project | undefined => {
    let viewer = this._viewer;
    if (!viewer) return;
    let sceneConfig = (viewer.plugins["PresetLibraryPlugin"] as PresetLibraryPlugin).exportPresets()
    // delete sceneConfig["type"];
    let materialConfig = (
      viewer.plugins["MaterialPresetPlugin"] as MaterialPresetPlugin
    ).toJSON();

    if (!this.project) {
      console.log("Project not found");
      return;
    }

    if (saveView) {
      this.project.cameraConfig = viewer.scene.activeCamera.toJSON();
    }

    const materialConfiguratorPlugin = (viewer.getPluginByType("MaterialConfiguratorPlugin") as MaterialConfiguratorPlugin).exportState();

    let plugins = {
      SimpleBackgroundEnvUiPlugin2: viewer
        .getPluginByType("SimpleBackgroundEnvUiPlugin2")!
        .toJSON?.(),
      materialConfiguratorPlugin : materialConfiguratorPlugin ? materialConfiguratorPlugin : undefined
    };

    let project_data = { ...this.project, sceneConfig, materialConfig, plugins };
    delete (project_data as any)["presetLib"];
    delete (project_data as any)["materialLib"];
    if(project_data.model_url){
      project_data.modelUrl = project_data.model_url;
      delete project_data.model_url;
    }

    //model pose
    if(this.project.currentPose) {
      const position = viewer.scene.modelRoot.children[0].position.toArray();
      const rotation = viewer.scene.modelRoot.children[0].rotation.toArray();
      project_data.currentPose = {position, rotation : rotation as any};
    }

    if (this.project.embedSettings) {
      project_data = { ...project_data, embedSettings: this.project.embedSettings };
    }
    
    return project_data;
  };

  export = async (saveView?: boolean, export3D?: boolean, download = false, encrypt = false, password?: string) => {
    let viewer = this._viewer;
    if (!viewer) return;
    let project_data = this.getConfig(saveView);
    if (!project_data) return;

    await viewer.getOrAddPlugin(GLTFDracoExportPlugin);
    const assetExporter = (await viewer.getOrAddPlugin(AssetExporterPlugin)) as AssetExporterPlugin;
    if ((this.editorSettings?.allow3dExport && export3D) || export3D) {
      const fileName = (this.project?.modelUrl
        ? this.project.modelUrl.split("/").pop()!
        : "scene.glb") || "scene.glb";
      viewer.getPluginByType<IjewelEditorPlugin>("IjewelEditorPlugin")?.fromJSON(project_data);
      const aws = viewer.getPlugin(AWSClientPlugin);
      aws?.disconnect();
      if(encrypt && password){
        assetExporter.exportOptions.encrypt = true
        assetExporter.exportOptions.encryptKey = password
      }
      const glb = await assetExporter.exportScene({
        ...assetExporter.exportOptions,
        name: fileName,
        viewerConfig: true,
        preserveUUIDs: true,
        compress: true,
      });
      aws?.connect();

      if (glb) {
        if (download) downloadBlob(glb, fileName);
        return glb;
      }
    } else {
      await this.saveConfig(project_data);
    }
  };

  saveConfig = async (config: any) => {
    let viewer = this._viewer;
    if (!viewer) return;
    const tr = (await viewer.getOrAddPlugin(FileTransferPlugin)) as FileTransferPlugin;

    if (!tr) {
      viewer.console.error("FileTransferPlugin required to export/download file");
      return;
    }

    if (this.editorSettings?.beforeExportCallback) this.editorSettings?.beforeExportCallback();

    let blob = new Blob([JSON.stringify(config)], { type: "application/json" });

    if (this.project?.modelUrl) {
      const file = new File([blob], ".meta/" + this.project.modelUrl.split("/").pop() + ".json", {
        type: "application/json",
      });
      await tr.exportFile(blob, ".meta/" + this.project?.modelUrl.split("/").pop() + ".json");
    } else {
      await tr.exportFile(blob, this.project?.name ? this.project?.name + ".json" : "config.json");
    }
  };

  preprocessData(data?: Record<string, Preset|undefined>, basePath?: string): { [key: string]: { path: string; icon: string, isPremium:boolean , isDefault?:boolean }[] } {
    let result: { [key: string]: { path: string; icon: string, isPremium:boolean }[] } = {};
    if(!data) return result
    for (let key in data) {
      const val = data[key]
      if(!val) continue
      result[key] = val.assets.map((asset) => {
        let dataBasePath = val.basePath || "";
        const isString = typeof asset === 'string'

        let icon, path
        if(isString) {
          const parts = asset.split(";");
          icon = parts[1]
          path = parts[0];
        }else {
          icon = asset.icon
          path = asset.path
        }

        // Check if there's a semicolon and derive the icon path based on it
        icon = icon || path;
        if (icon.startsWith(".")) {
          // For cases like 1k.hdr;.png
          icon = `${path}${icon}`;
        }

        // Remove leading slash if dataBasePath is empty
        const mainPath = dataBasePath ? dataBasePath  + '/' + path : path;
        const iconFullPath = dataBasePath ? dataBasePath + '/'  + icon : icon.replace(/^\//, '');

        const res = {
          id: isString ? undefined : asset.id,
          path: (basePath||"") + mainPath,
          icon: (basePath||"") + iconFullPath,
          name: isString ? undefined : asset.name,
          isPremium: isString ?asset.includes(";premium") : asset.isPremium,
          isDefault: isString ? asset.includes(";default") : asset.isDefault,
        };
        if(res.isPremium === false) delete res.isPremium;
        if(res.isDefault === false) delete res.isDefault;
        return res
      });
    }

    return result;
  }

  //used to process list of files from new drive api to asset library structure
  processFiles(files: DriveFile[]): AssetsLibrary | undefined {
    const assets: AssetsLibrary = {
      presets: {} as any,
      materials: {} as any,
    };
    if (!files || files.length === 0) return undefined;

    files = files.filter((file) => !file.deleted); // in case there are any deleted files
    const mainFolderNames = ["presets", "hdrmaps", "materials", "maps", "configs", "images", "stages"];

    // Helper function to parse tags into an array
    const getTagsArray = (tags: string | undefined): string[] => {
      return tags ? tags.split(",").map((tag) => tag.trim()) : [];
    };

    // Helper function to extract category tags
    const getCategoryTags = (tagsArray: string[]): string[] => {
      return tagsArray
        .filter((tag) => tag.startsWith("_category:"))
        .map((tag) => tag.replace("_category:", ""));
    };

    const getAssets = (files: DriveFile[]): IAsset[] => {
        return files.map((file) => {
        const tags = getTagsArray(file.tags);
        const isPremium = tags.includes("_premium");
        const isDefault = tags.includes("_default");
        // return (
        //   `${file.file ? file.file : file.thumb};${file.thumb}` +
        //   `${isPremium ? ";premium" : ""}` +
        //   `${isDefault ? ";default" : ""}`
        // );
          return {
            id: file.id,
            path: file.file || `//${file.id}`,
            icon: file.thumb,
            name: file.name,
            isPremium,
            isDefault,
            tags,
          }
      });
    };

    mainFolderNames.forEach((folderName) => {
      const folder = files.find((file) => file.name === folderName);
      if (folder) {
        const children = files.filter((file) => file.path?.includes?.(folder.id));
        switch (folderName) {
          case "presets":
            assets.presets.VJSON = { basePath: "", assets: getAssets(children) };
            break;
          case "hdrmaps": {
            assets.presets.Environment = {
              basePath: "",
              assets: getAssets(
                children.filter((file) => {
                  const tagsArray = getTagsArray(file.tags);
                  return tagsArray.includes("_category:metal");
                })
              ),
            };
            assets.presets.GemEnvironment = {
              basePath: "",
              assets: getAssets(
                children.filter((file) => {
                  const tagsArray = getTagsArray(file.tags);
                  return tagsArray.includes("_category:gem");
                })
              ),
            };
            const remaining = children.filter((file) => {
              const tagsArray = getTagsArray(file.tags);
              return (
                !tagsArray.includes("_category:metal") &&
                !tagsArray.includes("_category:gem")
              );
            });
            if (remaining.length > 0) {
              if (!assets.presets.Environment) {
                assets.presets.Environment = { basePath: "", assets: [] };
              }
              assets.presets.Environment.assets.push(...getAssets(remaining));
            }
            break;
          }
          case "materials": {
            // Collect all unique categories for materials dynamically
            const categories = new Set<string>();
            children.forEach((file) => {
              const tagsArray = getTagsArray(file.tags);
              getCategoryTags(tagsArray).forEach((category) => categories.add(category));
            });

            // Initialize materials for each category
            categories.forEach((category) => {
              const categoryAssets = children.filter((file) => {
                const tagsArray = getTagsArray(file.tags);
                const categories = getCategoryTags(tagsArray);
                return categories.includes(category);
              });
              const categoryKey = category as keyof AssetsLibrary["materials"]; // Use the category name as the key
              // todo what if the category doesnt exist
              assets.materials[categoryKey] = {
                basePath: "",
                assets: getAssets(categoryAssets),
              };
            });

            // Assets without categories go to 'other'
            const uncategorizedAssets = children.filter((file) => {
              const tagsArray = getTagsArray(file.tags);
              const categories = getCategoryTags(tagsArray);
              return categories.length === 0;
            });

            if (uncategorizedAssets.length > 0) {
              if (!assets.materials.other) {
                assets.materials.other = { basePath: "", assets: [] };
              }
              assets.materials.other.assets.push(...getAssets(uncategorizedAssets));
            }
            break;
          }
          case "configs":
            assets.presets.Ground = { basePath: "", assets: getAssets(children) };
            break;
          case "stages":
            assets.presets.ModelStage = { basePath: "", assets: getAssets(children) };
            break;
          case "images":
            assets.presets.Background = { basePath: "", assets: getAssets(children) };
            break;
          default:
            break;
        }
      }
    });
    return assets;
  }


  async loadPlugins(plugins: any) {
    for (const [key, value] of Object.entries(plugins)) {
      let file = new File([new Blob([JSON.stringify(value)])], key + ".json");
      if (value) {
        await this._viewer!.load({ file: file, path: file.name });
      }
    }
  }

  makeAbsolute(config: any, basePath: string) {
    const obj = { ...config };

    const getPath = (p: string) => {
      let path = (p.startsWith("https://") ? "" : basePath) + p;
      path = path.replace(/(https?:\/\/)|(\/)+/g, (m, p1) => p1 ? p1 : '/')
      return path;
    }

    for (let key in obj) {
      if (typeof obj[key] === "string") {
        if(key === "type") continue;
        if (obj[key].split("/").pop()?.includes("None")) continue;
        obj[key] = getPath(obj[key]);
      } else {
        if(obj[key]?.path){
          obj[key].path = getPath(obj[key].path);
        }
      }
    }
    return obj;
  }


  makeRelative(config: any, basePath: string) {
    const obj = { ...config };
    for (let key in obj) {
      if (typeof obj[key] === "string") {
        if(obj[key] === "type") continue;
        obj[key] = obj[key].replace(basePath, "");
      } else {
        this.makeRelative(obj[key], basePath);
      }
    }

    return obj;
  }

  mergeLibs(lib1: { [key: string]: { path: string; icon: string, isPremium:boolean }[] }, lib2: { [key: string]: { path: string; icon: string, isPremium:boolean }[] }) {
    for (let key in lib2) {
      if (lib1[key]) {
        lib1[key] = [...lib1[key], ...lib2[key]];
      } else {
        lib1[key] = lib2[key];
      }
    }
    return lib1;
  }
}
