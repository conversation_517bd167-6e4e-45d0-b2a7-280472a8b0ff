import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="19"
    viewBox="0 0 16 19"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8 5.09166C4.63923 5.09166 1.91476 7.81612 1.91476 11.1761C1.91476 14.5368 4.63923 17.2613 8 17.2613C11.3608 17.2613 14.0852 14.5368 14.0852 11.1761C14.0852 7.81612 11.3608 5.09166 8 5.09166ZM0.176476 11.1761C0.176476 6.85519 3.67912 3.35254 8 3.35254C12.3209 3.35254 15.8235 6.85519 15.8235 11.1761C15.8235 15.497 12.3209 18.9996 8 18.9996C3.67912 18.9996 0.176476 15.497 0.176476 11.1761Z"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.88579 0.217949C6.06542 0.0782439 6.3088 0 6.56298 0H9.43702C9.69121 0 9.9346 0.078235 10.1142 0.217949L11.0725 0.963283C11.4228 1.2357 11.448 1.67088 11.1309 1.96707L8.73558 4.20235C8.55415 4.3721 8.28472 4.47059 8.00001 4.47059C7.71529 4.47059 7.44584 4.3721 7.26444 4.20235L4.86909 1.96707C4.55205 1.6709 4.57721 1.23572 4.92747 0.963283L5.88579 0.217949ZM6.95997 1.48997L6.90069 1.53537L8 2.56151L9.09931 1.53537L9.04003 1.48997H6.95997Z"
    />
  </svg>
);
export default SvgComponent;
