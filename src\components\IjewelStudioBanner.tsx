import { FC, useCallback, useEffect, useState } from "react";
import { ViewerApp } from "webgi";
import Logo from "./icons/studio-logo";
import { Button } from "@nextui-org/react";

const IjewelStudioBanner: FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const isClosed = localStorage.getItem("ijewel-studio-banner");
    if (isClosed) setIsOpen(false);
    else setIsOpen(true);
  }, []);

  const close = useCallback(() => {
    setIsOpen(false);
    //save it to local storage
    localStorage.setItem("ijewel-studio-banner", "closed");
  }, []);

  if (!isOpen) return null;

  return (
    <div className="flex overflow-hidden bg-white flex-col gap-unit-lg w-full rounded-medium justify-center items-center pb-unit-lg">
      <img
        src={"https://playground.ijewel3d.com/images/ijewelstudiobanner.webp"}
        alt="iJewel Studio"
        className="w-full h-full object-cover"
      />
      <Logo />
      <p className="text-small text-center font-normal">
        Online Batch Rendering
      </p>
      <a
        href="https://ijewel.studio"
        target="_blank"
        rel="noreferrer"
        className="text-default-500 text-sm font-mainSemiBold"
      >
        <Button
          className="py-4 px-unit-lg text-sm rounded-large"
          color="primary"
          size="sm"
          fullWidth={false}
        >
          Get Started
        </Button>
      </a>
      <p
        className="text-default-500 text-sm font-mainSemiBold cursor-pointer"
        onClick={close}
      >
        <Button
          className="px-unit-lg text-sm rounded-large text-default-500 font-mainSemiBold"
          color="default"
          variant="light"
          size="lg"
          fullWidth={false}
          onClick={close}
        >
          Close
        </Button>
      </p>
    </div>
  );
};

export { IjewelStudioBanner };
