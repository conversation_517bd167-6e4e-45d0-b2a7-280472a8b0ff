import { Button, ButtonProps } from "@nextui-org/react";
import { FC } from "react";
import { Icon } from "./Icon";

interface CustomButtonProps extends ButtonProps{
  name?: string;
  endIcon?: any;
  startIcon?: string;
  className?: string;
  color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger";
  size?: "sm" | "md" | "lg";
  varient?: "solid" | "bordered" | "light" | "flat" | "faded" | "shadow" | "ghost";
  heiglighted?: boolean;
  onClick?: any;
  fullWidth?: boolean;
  isLoading?: boolean;
  disabled?: boolean;
  ref?: any;
}

const MyButton: FC<CustomButtonProps> = (props) => {
  return (
    <Button
      ref={props.ref}
      disabled={props.disabled}
      isDisabled={props.disabled}
      isLoading={props.isLoading}
      onClick={props.onClick}
      color={props.color}
      endContent={props.endIcon && typeof props.endIcon === 'string' ? <Icon src={props.endIcon} /> : props.endIcon}
      startContent={props.startIcon ? <Icon src={props.startIcon} /> : null}
      className={
        props.className +
        (props.heiglighted ? " bg-white bg-gradient-to-r from-[#B150FF] to-[#81C3FF] text-white" + (props.disabled ? " opacity-40" : "") : "")
      }
      fullWidth={props.fullWidth !== undefined ? props.fullWidth : true}
      size={props.size ? props.size : "sm"}
      style={{ fontWeight: "inherit", fontSize: "inherit" }}
      variant={props.varient}
      disableRipple
      {...props}
    >
      {props.name ?? props.children}
    </Button>
  );
};

export { MyButton as Button };
