import { Mesh, MeshPhysicalMaterial, Object3D } from "webgi"

export const getAllMaterials = (root : Object3D) : any[] => {
    let materials : any[] = []
    root.traverse((o) => {
        const material = (o as any).material
        if (material) {
            materials.push(material)
        }
    })
    return materials;
}

export const applyMaterials = (materials : any[], root : Object3D) => {
    root.traverse((o) => {
        const material = (o as any).material
        if (material) {
            const newMaterial = materials.find((m) => m.name === material.name)
            if (newMaterial) {
                console.log("Applying material", newMaterial.name, "to", o)
                if((o as any).setMaterial) (o as any).setMaterial(newMaterial)
                else (o as any).material = newMaterial
            }
        }
    })
}   