import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { EditorOption } from "./EditorOption";
import {
  DiamondPlugin,
  type IAsset,
  ModelStagePresetGroup,
  PresetGroup,
  TPreset,
  VJSONPresetGroup,
} from "webgi";
import { PresetLibraryPlugin } from "webgi";
import { ViewerApp } from "webgi";
import toast from "@minieditor/react-hot-toast";
import { ConfirmToast } from "./ConfirmToast";
import { Popover, PopoverContent, PopoverTrigger, Switch, Tooltip } from "@nextui-org/react";
import { PresetGroupTab, PresetTab } from "./EditorTabs";
import { useUi } from "./provider/UiProvider";
import { EDSETTING, EditorSettings } from "../main";
import { Button } from "./Button";
import PlusSolid from "./icons/PlusSolid";
import LockIcon from "./icons/Lock";
import Reset from "./icons/Reset";
import Close from "./icons/Close";
import { useProject } from "./provider/ProjectProvider";
import { HideMaterialOrPresetItemWrapper, UploadMaterialOrPresetButton } from "./utils/shared";
import { generateCustomSceneSettingFile, isCustomScenePerset, moveCustomInFront } from "./utils/functions";
import ED_SETTING from "../types";
import { IjewelEditorPlugin } from "./viewer/IjewelEditorPlugin";

interface PresetOption {
  path: string;
  icon?: string;
}

interface PresetsOptionsProps {
  selectedPresetTab: PresetTab;
  viewer?: ViewerApp;
  setHasEdited: any;
  groups?: PresetGroup[];
  isPremium?: boolean;
  isBusiness?: boolean;
  setShowSubscribeModal?: any;
  isLogin?: boolean;
  customAssets: EditorSettings['customAssets']
  customAssetsStore:  Record<string, ED_SETTING.CUSTOM_ASSET_ITEM[]> | null;
  setCustomAssetsStore: React.Dispatch<React.SetStateAction<Record<string, ED_SETTING.CUSTOM_ASSET_ITEM[]> | null>>;
  fullEditor?: boolean;
}

const PresetsOptions: React.FC<PresetsOptionsProps> = ({
  viewer,
  setHasEdited,
  selectedPresetTab,
  groups,
  isPremium,
  isBusiness,
  setShowSubscribeModal,
  isLogin,
  customAssets,
  customAssetsStore,
  setCustomAssetsStore, 
  fullEditor,
}) => {
  const [presets, setPresets] = useState<any[]>([]);
  const [group, setGroup] = useState<PresetGroup>();
  const [loading, setLoading] = useState(false); 
  const [savedConfig, setSavedConfig] = useState<any>();
  const { autoApplyModelStageConfig, setAutoApplyModelStageConfig } = useUi();
  const [selectedHideAsset, setSelectedHideAsset] = useState(null)
  const { setProject } = useProject()
  const { embed: { setSettings } } = useUi();


  const handleAddCustomAsset = async ({ file, assetType }: { file: File; assetType: string }) => {
    const newPreset = await customAssets?.handleUploadAsset?.({
      assetFile: file,
      assetType,
    });
    if (!newPreset || !newPreset.path) {
      throw new Error("Failed to upload asset");
    }
    const presetGroup = groups?.find((g) => g.name === assetType);
    presetGroup?.addPresets([newPreset]);

    const newPresets = [...presetGroup!.presets];
    setPresets(newPresets);
    setCustomAssetsStore?.((p) => {
      const prev = p ?? {};
      return {
        ...prev,
        [assetType]: [...(prev[assetType] ?? []), newPreset],
      };
    });
    
  };
  const handleHideAsset =  async (asset: any, assetType: string, isSelected: boolean) => {
    if (asset.isCustom !== true) return;

    if(isSelected){
      setSelectedHideAsset(null)
      toast.error('Before deleting this asset, please either deselect it or replace it with another option.', { id: "asset-unselect-error" });
      return
    }

    let id = toast.loading("Deleting Asset...", {
      id: "asset-delete-start",
    });
    return customAssets
      ?.handleArchiveAsset?.({ asset, isArchive: true })
      .then(() => {
        const presetGroup = groups?.find((g) => g.name === assetType);

        // override preset reference
        if (!presetGroup?.presets) return;
        presetGroup.presets = presetGroup?.presets.filter((item: any) => item.path !== asset.path);
        setPresets(presetGroup.presets);
        
        setCustomAssetsStore?.((p) => {
          const prev = p ?? {};
          let newPresets = prev[assetType] ?? [];
        
          if (selectedPresetTab === "VJSON" && asset.path.endsWith(".json") ) {
            newPresets = prev["ScenePreset"] ?? []; // custom scene preset
          }
          newPresets = newPresets.filter((item: any) => item.path !== asset.path);
          return {
            ...p,
            [assetType]: newPresets,
          };
        });
    
        setSelectedHideAsset(null)
        toast.dismiss(id);
        toast.success("Asset deleted successfully", {
          id: "asset-delete-success",
        });
      })
      .catch((err: any) => {
        toast.dismiss(id);
        toast.error(err.message, { id: "asset-delete-error" });
      });
  };

  const LoadMaterialConfiguratorPrompt = useCallback(async (group :VJSONPresetGroup, preset: TPreset) => {
    if (!viewer) return;
    ConfirmToast("Do you want to load the material configurator from this preset? This will override current configurator", "LOAD_VJSON_CONFIGURATOR", () => {
      group.usePresetMaterialConfiguratorPlugin = true;
      if(!group.selected) return
      group.apply(viewer, group.selected as any).then(() => {
        group.usePresetMaterialConfiguratorPlugin = false;
      })
    })
  }, [viewer]);

  const handleClick = useCallback(
    async (path: string | null) => {
      if (!viewer) return;
      if (group) {
        let preset = group.presets.find((p) =>
          typeof p === "string" ? p === path : p.path === path
        );
        let previousConfig: any;
        if (selectedPresetTab === "VJSON") {
          previousConfig = viewer
            .getPlugin(PresetLibraryPlugin)
            ?.exportPresets();
          // delete previousConfig.Scene;
          clearSettings(viewer);
        }
        setLoading(true);
        setHasEdited(true);
        //temporary till fixed in webgi
        if (selectedPresetTab === "ModelStage") {
          group.selected = preset
        }

        let isValidCustomSPreset = false;
        let customSPresetSetting = null;
        if (selectedPresetTab === "VJSON" && preset) {
          const result = await isCustomScenePerset(preset);
          isValidCustomSPreset = result.isValid;
          customSPresetSetting = result.file;
        }
        if (isValidCustomSPreset) {
          toast.loading("Loading Scene peset", { id: "loading-scene-preset" });
          const editorPlugin = viewer.getPluginByType<IjewelEditorPlugin>("IjewelEditorPlugin")!;
          const { customSceneSetting } = await generateCustomSceneSettingFile(viewer);
          previousConfig = customSceneSetting;

          // fetch custom scence preset apply.
          if (customSPresetSetting.embedSettings && Object.keys(customSPresetSetting.embedSettings).length > 0) {
            setSettings(customSPresetSetting.embedSettings);
          }
          editorPlugin.fromJSON(customSPresetSetting);
          await editorPlugin.applyConfig();
          group.selected = preset;
          toast.dismiss("loading-scene-preset");
          setLoading(false);
        } else {
          //todo: fix in webgi VJSONPresetGroup is not returning the asset after apply
          group.apply(viewer, preset).then(async (asset: any) => {
            setLoading(false);
            //after loading model stages
            if (selectedPresetTab === "ModelStage") {
              group.selected = preset;
              viewer.fitToView();
              const plugin = viewer.getPlugin("ModelStagePlugin") as any as { importStageViewerConfig: () => void; resetDefaults: () => void };

              if (autoApplyModelStageConfig) {
                plugin.importStageViewerConfig();
                setProject((p) => ({ ...p, shouldApplyModelStageConfig: true }));
              } else {
                setProject((p) => ({ ...p, shouldApplyModelStageConfig: false })); // reset the flag

                ConfirmToast("Do you want to apply scene settings from the model stage? this will override other settings", "APPLY", () => {
                  setProject((p) => ({ ...p, shouldApplyModelStageConfig: true }));
                  plugin.importStageViewerConfig();
                });
              }
            }

            if (selectedPresetTab === "VJSON") {
              LoadMaterialConfiguratorPrompt(group as any, preset!);
            }
          });
        }

        // show Undo-prompt 
        toast.dismiss("undo-changes")
        if (
          previousConfig &&
          (previousConfig.Background ||
            previousConfig.Environment ||
            previousConfig.Ground ||
            previousConfig.CameraViews ||
            previousConfig.GemEnvironment || 
            previousConfig.type == 'IjewelEditorPlugin')
        ) {
          toast(
            (t) => (
              <UndoToast
                id={t.id}
                resetSetting={resetSetting}
                previousConfig={previousConfig}
                setSavedConfig={setSavedConfig}
              />
            ),
            { duration: 8000 , className: "!bg-warning" , id:'undo-changes' }
          );
        }
      }
    },
    [viewer, group, selectedPresetTab , autoApplyModelStageConfig]
  );

  useEffect(() => {
    if (!viewer || selectedPresetTab == "hidden" || !groups) return;
    
    if (groups?.length && groups.find((g) => g.name === selectedPresetTab)) {
      setPresets([...groups.find((g) => g.name === selectedPresetTab)!.presets]);
      setGroup(groups.find((g) => g.name === selectedPresetTab));
    }
  }, [selectedPresetTab, group, viewer, groups]);

  const clearSettings = (viewer: ViewerApp) => {
    viewer.getPlugin(PresetLibraryPlugin)?.clear();
  };

  const resetSetting = useCallback(
    async (previous: any) => {
      if (!viewer) return;
      if (previous.type === "IjewelEditorPlugin") {
        // custom scene preset
        const editorPlugin = viewer.getPluginByType<IjewelEditorPlugin>("IjewelEditorPlugin")!;
        editorPlugin.fromJSON(previous);
        await editorPlugin.applyConfig();
      } else viewer.getPlugin(PresetLibraryPlugin)?.fromJSON(previous);
    },
    [viewer, savedConfig]
  );

  const handleNone = useCallback(() => {
    if (!viewer || selectedPresetTab == "hidden" || !group) return;
    switch (selectedPresetTab) {
      case "Environment":
        viewer.scene.environment = null;
        break;
      case "GemEnvironment":
        viewer.getPlugin(DiamondPlugin)!.envMap = null;
        break;
      case "Background":
        viewer.scene.background = null;
        break;
      case "ModelStage":
        //hardcoded for now, just to test the functionality
        group.apply(viewer, {path:"https://packs.ijewel3d.com/files/preset._model_stage_plugin_2_b753e9dd0b.json" , name:"none"})
        break;
    }
    group?.clear();
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, [viewer , group , selectedPresetTab ]);

  const isRect = selectedPresetTab === "Ground" || selectedPresetTab === "VJSON" || selectedPresetTab === "ModelStage"
  const isCustomAssetOver2MbWarning= group?.selected && (group?.selected as any).isCustom && (group?.selected as any).size > 2 * 1024 * 1024


  const sortedPresets = presets.sort((a, b) => {
    const aIsBasic = !a.isPremium && !a.isBusiness;
    const bIsBasic = !b.isPremium && !b.isBusiness;
  
    // If A is basic and B is not, A should come first
    if (aIsBasic && !bIsBasic) return -1;
  
    // If B is basic and A is not, B should come first
    if (!aIsBasic && bIsBasic) return 1;
  
    // Otherwise keep their order the same
    return 0;
  });
  return (
    <div className="flex flex-col items-center">

      {selectedPresetTab === "ModelStage" &&
        <Tooltip content="Automatically apply the scene settings from the model stage" size="sm" showArrow color="secondary" placement="right">
          <div className="w-full h-full flex items-center justify-center mb-unit-lg">
            <Switch
              // label="Apply by default"
              checked={autoApplyModelStageConfig}
              isSelected={autoApplyModelStageConfig}
              onChange={() => {
                setAutoApplyModelStageConfig(!autoApplyModelStageConfig);
              }}
              color="default"
              size="sm"
              className="w-full !text-tiny"
              classNames={{label: "text-default-500 text-sm"}}
            >
              Apply config
            </Switch>
          </div>
        </Tooltip>
      }
      <div className={"flex flex-wrap gap-1 justify-center " +
        (selectedPresetTab === "Ground" ||
        selectedPresetTab === "ModelStage" ||
        selectedPresetTab === "VJSON" ||
        selectedPresetTab === "export" ||
        selectedPresetTab === "advanced"
          ? "gap-1"
          : "gap-y-unit-xl")
      }>
        {(selectedPresetTab === "ModelStage" ||
          selectedPresetTab === "Environment" ||
          selectedPresetTab === "Background" ||
          selectedPresetTab === "GemEnvironment") && <EditorOption
          onClick={() => {
            handleNone();
          }}
          selected={!group?.selected}
          key={"none" + group?.name}
          src="none"
          loading={group?.selected === null && loading}
          name={"None"}
          rect={selectedPresetTab === "ModelStage"}
          className=""
          index={presets.length}
          isLogin={isLogin}
          isPremium={false}
          isLocked={false}
          setShowSubscribeModal={setShowSubscribeModal}
        />}
       {isCustomAssetOver2MbWarning && (
          <div className="bg-[#fdf8e3] text-default-foreground border-[#f8eab1] border-[1px] p-2 rounded-small text-xs w-full">This custom asset is larger than 2MB.</div>
        )}
       <UploadMaterialOrPresetButton
          handleAddCustomAsset={handleAddCustomAsset}
          assetType={selectedPresetTab as Exclude<PresetGroupTab, "ModelStage">}
          rect={isRect}
          isLogin={isLogin}
          isBusiness={isBusiness}
          isPremium={isPremium}
          setShowSubscribeModal={setShowSubscribeModal}
          customAssetsStore={customAssetsStore}
          fullEditor={fullEditor}
        />
        {moveCustomInFront(sortedPresets).map((option: any, i) => {
          const isSelected = (group?.selected as any)?.path === option.path;
          return (
            <HideMaterialOrPresetItemWrapper            
              key={option.path}
              asset={option}
              isSelected={isSelected}
              selectedPresetTab={selectedPresetTab}
              onConfirm={handleHideAsset}
              selectedHideAsset={selectedHideAsset}
              setSelectedHideAsset={setSelectedHideAsset}
            >
              <EditorOption
                onClick={() => {
                  handleClick(option.path || option);
                }}
                selected={isSelected}
                key={option.path + selectedPresetTab + i + "preset" + (option.isPremium ? "premium" : "")}
                src={option.icon ? option.icon + "?preset" : ""}
                loading={isSelected && loading}
                name={option.name || option.path}
                rect={isRect}
                className=""
                index={i}
                isLogin={isLogin}
                isPremium={option.isPremium}
                isBusiness={option.isBusiness}
                isLocked={(option.isPremium && !isPremium) || (!isLogin && i > 3) || (!isLogin && selectedPresetTab === "ModelStage")}
                setShowSubscribeModal={setShowSubscribeModal}
              />
            </HideMaterialOrPresetItemWrapper>
          );
        })}
      </div>
    </div>
  );
};

const UndoToast = (props: any) => {
  return (
    <div className="flex gap-unit-md items-center justify-center text-white bg-warning">
      <p className="font-mainSemiBold h-unit-3xl">
        All other settings have been cleared
      </p>
      <div className="flex-1"></div>
      <div className="flex border-l border-gray-200">
        <button
          onClick={() => {
            toast.dismiss(props.id);
            props.resetSetting(props.previousConfig);
            props.setSavedConfig(props.previousConfig);
          }}
          className="w-full border border-transparent rounded-none rounded-r-lg p-4
                     flex items-center justify-center text-sm font-medium hover:text-danger
                      focus:outline-none focus:ring-2 focus:ring-danger text-white font-mainSemiBold"
        >
          UNDO
        </button>
      </div>
    </div>
  );
};

export default PresetsOptions;