import { Button } from "./Button";

export const UpgradeToBusinessButton = (props: {
  className?: string;
  onClick?: () => void;
}) => {
  return (
    <Button
      name="Upgrade to"
      variant="bordered"
      onClick={props.onClick}
      className={`h-fit px-4 py-0.5 text-[11px] font-semibold leading-1 rounded-full border-1 border-[#CFCFCF] ${props.className}`}
      endContent={
        <span className="ml-0.5 text-[#FFFFFF] px-2 py-1 font-bold text-[10px] leading-none rounded-full bg-[linear-gradient(229.19deg,_#FCC253_-33.33%,_#FFE0A4_18.24%,_#B5821F_69.81%,_#FFD788_121.38%)]">
          Business
        </span>
      }
      fullWidth
      style={{}}
    />
  );
};
