import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { useEffect } from "react";
import { ADD_CHILD_NODE_COMMAND } from "./Commands";
import { $getRoot } from "lexical";
import { type EditorState, type LexicalEditor } from "lexical";
import { type TextEditorProjectData } from "./TextEditor";

export default function MyOnChangePlugin({
  onChange,
  initialState,
}: {
  onChange?: (
    editorNewState: EditorState,
    projectData: TextEditorProjectData,
    editor: LexicalEditor
  ) => void;
  initialState: any;
}) {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    if (!editor || !initialState) return;
    let json = initialState;
    if (initialState.name) {
      json = {
        root: {
          children: [
            {
              children: [{ text: initialState.name, type: "text" }],
              type: "paragraph",
            },
          ],
          type: "root",
        },
      };
      if (initialState.description) {
        json.root.children.push({
          children: initialState.description
            ?.split("\n")
            .map((text: string) => ({
              children: [{ text, type: "text" }],
              type: "paragraph",
            })),
          type: "paragraph",
        });
      }
    }

    if (!json.root) return;
    const state = editor.parseEditorState(json);
    editor.setEditorState(state);
  }, [editor]);

  useEffect(() => {
    if (!editor) return;
    return editor.registerUpdateListener(({ editorState }) => {
      const editorStateJson = editorState.toJSON();
      //get all the text from the editor
      editor.update(() => {
        // if first child and not second, add a child
        const ctxRoot = $getRoot();
        const nameNode = ctxRoot.getFirstChild();
        const nameNodeText = nameNode?.getTextContent() ?? "";
        const desctiptionNode = ctxRoot.getChildAtIndex(1);
        const desctiptionNodeText = desctiptionNode?.getTextContent() ?? "";

        if (nameNodeText.length > 0 && !desctiptionNode) {
          editor.dispatchCommand(ADD_CHILD_NODE_COMMAND, "");
        }
        if (
          nameNodeText === "" &&
          desctiptionNode &&
          desctiptionNodeText === ""
        ) {
          desctiptionNode?.remove();
          nameNode?.remove();
        }
      });

      editor.update(() => {
        const ctxRoot = $getRoot();

        const name = ctxRoot.getFirstChild()?.getTextContent() ?? "";

        let description = "";
        let currentChild = ctxRoot.getFirstChild()?.getNextSibling();
        while (currentChild) {
          description += currentChild.getTextContent();
          currentChild = currentChild.getNextSibling();
        }

        const changes = {
          name: name.trim(),
          description: description.trim(),
        };
        onChange?.(editor.getEditorState(), changes, editor);
      });
    });
  }, [editor, onChange]);
  return null;
}
