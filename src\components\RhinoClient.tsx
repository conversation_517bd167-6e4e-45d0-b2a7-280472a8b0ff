import { Button, Input, Textarea } from "@nextui-org/react";
import { FC, useCallback, useEffect, useRef } from "react";
import { Icon } from "./Icon";
import { DiamondPlugin, Mesh, Object3D, ViewerApp } from "webgi";
import { applyMaterials, getAllMaterials } from "./utils/materials";
import toast from "@minieditor/react-hot-toast";
import { useUi } from "./provider/UiProvider";
import { DefaultEnvMapUrl } from "./utils/urls";



const RhinoClient = ({viewer} : {viewer?: ViewerApp}) => {
  const {showDefaultMaterialsPrompt} = useUi();
  const loaindgRef = useRef(false);

  const changeCallback = useCallback(async (res: Response) => {

    if(!viewer) {
      loaindgRef.current = false;
      return;
    }
    if (res.status === 200) {
      const ischanged = await res.json();
      if (ischanged) {
        const modelUrl = "http://localhost:8469/model.3dm" + "?t=" + Date.now();
        const currentScene = viewer?.scene.modelRoot.children[0] as Mesh;
        //hide the div with the role = status in classname
        toast.loading("", { id: "model-sync" , className: `[&_[role='status']]:hidden`  });

        const model = await viewer?.load(modelUrl, {
          autoCenter: true,
          autoScale: true,
        });
        if (!model || !model.modelObject) {
          toast.error("" , { id: "model-sync" });
          loaindgRef.current = false;
          return;
        }

        if (currentScene) {
          currentScene.removeFromParent();
          const mats = getAllMaterials(currentScene);
          viewer.getPlugin(DiamondPlugin)?.disposeAllCacheMaps();
          applyMaterials(mats, model.modelObject as Object3D);
        }else{

          if (!viewer.scene.environment) {
            viewer.load(DefaultEnvMapUrl); // from asset packs v5
          }
          showDefaultMaterialsPrompt(viewer);

        }

        toast.dismiss("model-sync");
      }
    }

    loaindgRef.current = false;
  }, [viewer]);

  useEffect(() => {
      if (!viewer) return;
      //run it once on load
      (async () => {
        try{
          await fetch("http://localhost:8469/api/has-changed?force").then(changeCallback);
        }catch(e){
          console.error(e);
        }
      })();

      const interval = setInterval(async () => {
        try {
          if(loaindgRef.current) return;
          loaindgRef.current = true
          await fetch("http://localhost:8469/api/has-changed").then(changeCallback);
        } catch (error) {
          console.error(error);
          loaindgRef.current = false;
        }
      }, 3000);

      return () => clearInterval(interval);
    }, [viewer]);

  return (
    <></>
  );
};

export { RhinoClient  };
