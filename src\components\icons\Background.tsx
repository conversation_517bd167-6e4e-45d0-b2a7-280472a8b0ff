import * as React from "react"
import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <mask
      id="a"
      width={24}
      height={24}
      x={0}
      y={0}
      maskUnits="userSpaceOnUse"
      style={{
        maskType: "alpha",
      }}
    >
      <path fill="#D9D9D9" d="M0 0h24v24H0z" />
    </mask>
    <g >
      <path
        fill="currentColor"
        d="M4.554 20.5c-.24-.06-.46-.191-.662-.392a1.425 1.425 0 0 1-.392-.662L19.446 3.5c.26.07.483.201.67.392.185.191.319.412.4.662L4.553 20.5ZM3.5 14.114v-2.108L12.006 3.5h2.107L3.5 14.114Zm0-7.441V5.308c0-.505.175-.933.525-1.283.35-.35.778-.525 1.283-.525h1.365L3.5 6.673ZM17.327 20.5l3.173-3.173v1.365c0 .505-.175.933-.525 1.283-.35.35-.778.525-1.283.525h-1.365Zm-7.44 0L20.5 9.886v2.108L11.994 20.5H9.887Z"
      />
    </g>
  </svg>
)
export default SvgComponent
