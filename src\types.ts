import { IAsset } from "webgi";

declare namespace ED_SETTING {
  export type ASSET_ITEM = {
    isCustom?: boolean;
    icon: string;
    meta: {
      asset_data?: Record<string, any>;
      name: string;
    };
  };

  export type CUSTOM_ASSET_ITEM = ASSET_ITEM & IAsset;
  export type CUSTOM_ASSET_KEYS = "Environment" | "GemEnvironment" | "Background" | "VJSON" | "ScenePreset" | "Ground" | "Material";

  export type EMBED_SETTING = {
    isTitle?: boolean;
    isResponsive?: boolean;
    width?: string;
    height?: string;
    posterWidth?: string;
    posterHeight?: string;
    isRemoveHologram?: boolean;
    isRemoveLogo?: boolean;
    isRemoveLogoLink?: boolean;
    isRemoveLoadingBgImg?: boolean;
    isShowPrompt?: boolean;
    isTurntableAnimation?: boolean;
    isAutoplay?: boolean;
    isTransparentBackground?: boolean;
    isConfigurator?: boolean;
    isEnabledZoom?: boolean;
    isShare?: boolean;
    isShowModelInfo?: boolean;
    isQuality?: boolean;
    isResetView?: boolean;
    isRotateCamera?: boolean;
    isPlayCameraViews?: boolean;
    isPlayAnimations?: boolean;
    isFitObject?: boolean;
    isFullScreen?: boolean;
    [x: string]: any;
  };

  export type BRANDING_SETTING = {
    enable?: boolean;
    positionX?: number;
    positionY?: number;
    maxWidth?: number;
    maxHeight?: number;
    objectFit?: "contain" | "cover" | "fill" | "none" | "scale-down";
    showLoadingScreenLogo?: boolean;
    [x: string]: any;
  };

  export type EMBED = {
    baseurl?: string;
    previewBaseurl?: string;
    handleUploadLogo?: (data: { file: File; [x: string]: any }) => Promise<null | CUSTOM_ASSET_ITEM>;
    handleUploadEmbedPoster?: ((data: { file: File; [x: string]: any }) => Promise<CUSTOM_ASSET_ITEM | null>) | undefined;
    getProjectCountByLogoUrl?: (params: { field: string; logoUrl: string }) => Promise<number>;
    handleProjectLogoUpdate?: (data: { isRemove?: boolean; isRemoveAsset?: boolean; item: CUSTOM_ASSET_ITEM; [x: string]: any }) => Promise<any>;
    getlogoList?: () => Promise<CUSTOM_ASSET_ITEM[] | null>;
    enabled?: boolean;
    warnningMessage?: string;
    canRemoveHologram?: boolean;
  };

  export type CUSTOM_ASSET = {
    getCustomAssets?: () => Promise<null | Record<CUSTOM_ASSET_KEYS, CUSTOM_ASSET_ITEM[]>>;
    handleUploadAsset?: (data: {
      assetFile: File;
      assetType: string;
      materialCategory?: string;
      thumbnailFile?: File;
      [x: string]: any;
    }) => Promise<null | CUSTOM_ASSET_ITEM>;
    handleArchiveAsset?: (data: { asset: any; isArchive: boolean; [x: string]: any }) => Promise<null | CUSTOM_ASSET_ITEM>;
  };
}
export default ED_SETTING;
