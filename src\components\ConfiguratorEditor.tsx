import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
  <PERSON><PERSON><PERSON><PERSON>ow,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Button as NextButton,
  Popover,
  Autocomplete,
  AutocompleteItem,
  Input,
  Tooltip,
} from "@nextui-org/react";

import { ConfiguratorOption } from "./ConfiguratorOption";
import { FC, useCallback, useEffect, useRef, useState } from "react";
import { Button } from "./Button";
import { OverlayTabs } from "./OverlayTabs";
import { getUrlQueryParam, IMaterial, PickingPlugin, ViewerApp } from "webgi";
import { MaterialPresetPlugin } from "webgi";
import { useProject } from "./provider/ProjectProvider";
import splashIcon from "../assets/splash.webp?inline";

import metalIcon from "../assets/metal-icon.webp?inline";
import gemIcon from "../assets/gem-icon.webp?inline";
import ceramicIcon from "../assets/ceramic-icon.webp?inline";
import mattIcon from "../assets/matt-icon.webp?inline";
import { useMaterialConfiguratorConfigurators, useLegacyMaterialConfigurators, useSwitchNodeConfigurators } from "./hooks/ConfiguratorHooks";
// import { MaterialConfiguratorPlugin2 } from "./viewer/app";
import { EditorOption } from "./EditorOption";

interface ConfiguratorEditorProps {
  // configuratorConfig: any;
  // setConfiguratorConfig: any;
  viewer?: ViewerApp;
  setHasEdited: any;
  labels?: { metal: string[]; gem: string[], ceramic: string[], matt: string[] };
  isPremium?: boolean;
  setShowSubscribeModal: any;
  isLogin?: boolean;
  fullEditor?:boolean
}

const AutoCompletelabels = {
  metal: [
    "Main Trim",
    "Shank Trim",
    "Side Trim",
    "Accent Trim",
    "Inlay Trim",
    "Lace Trim",
    "Halo",
    "Chain",
  ],
  gem: [
    "Center Gem",
    "Side Gem",
    "Accent Gem",
    "Inlay Gem",
    "Shank Gem",
    "Halo Gem",
    "Crest Gem",
    "Color Gem",
    "Fancy Gem",
  ],
  ceramic: [
    "Main Trim",
    "Shank Trim",
    "Side Trim",
    "Accent Trim",
    "Inlay Trim",
    "Lace Trim",
    "Halo",
    "Chain",
  ],
  matt: [
    "Main Trim",
    "Shank Trim",
    "Side Trim",
    "Accent Trim",
    "Inlay Trim",
    "Lace Trim",
    "Halo",
    "Chain",
  ],
};

const ConfiguratorEditor: FC<ConfiguratorEditorProps> = (props) => {
  const [selectedTab, setSelectedTab] = useState<
    "metal-config" | "gem-config" | "ceramic-config" | "matt-config" | "scene-materials"
  >("metal-config");
  const [selectedOverlayTab, setSelectedOverlayTab] = useState("");
  const [selectedMesh, setSelectedMesh] = useState<any>();
  const [materialPresets, setMaterialPresets] = useState<any>({});
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const { project, setProject } = useProject();
  const [optionLoading, setOptionLoading] = useState("");
  const [labels, setLabels] =
    useState<typeof AutoCompletelabels>(props.labels ?? AutoCompletelabels);

  // const [configuratorConfig, setConfiguratorConfig] = useState<ConfiguratorConfig>([]);
  const [isConfig, setIsConfig] = useState(false);
  const [title, setTitle] = useState("");
  // const viewer = useSelector((state: PixolState) => state.user.viewer);
  const configurator = useRef(null);
  const configUi = useRef(null);

  const materialConfiguratorPlugin = useRef<any | null>();

  const materaialPresetConfigurators = useLegacyMaterialConfigurators({
    viewer: props.viewer,
    project: project,
    setProject: setProject,
    setHasEdited: props.setHasEdited,
  })

  const materialConfiguratorConfigurators = useMaterialConfiguratorConfigurators({
    viewer: props.viewer,
    setHasEdited: props.setHasEdited,
  })

  const switchNideConfigurators = useSwitchNodeConfigurators({viewer: props.viewer})

  useEffect(() => {
    if (props.viewer) {
      materialConfiguratorPlugin.current = props.viewer.getPluginByType("MaterialConfiguratorPlugin");
    }
  }, [props.viewer]);


  const exitConfig = () => {
    if (!configurator.current || !configUi.current) return;
    (configurator.current as HTMLDivElement).classList.remove("right-unit-4xl");
    (configurator.current as HTMLDivElement).classList.add("right-[-500px]");
    (configUi.current as HTMLDivElement).classList.remove("top-0");
    (configUi.current as HTMLDivElement).classList.add("top-[-500px]");
    setIsConfig(false);
  };

  const enterConfig = () => {
  if (!configurator.current || !configUi.current || !props.viewer ) return;
  let p = props.viewer.getPluginByType("Picking") as PickingPlugin;
  let selectedMesh = p.getSelectedObject() as any;
  if (!selectedMesh) return;
  
  props.setHasEdited(true);
  (configurator.current as HTMLDivElement).classList.remove("right-[-500px]");
  (configurator.current as HTMLDivElement).classList.add("right-unit-4xl");
  (configUi.current as HTMLDivElement).classList.remove("top-[-500px]");
  (configUi.current as HTMLDivElement).classList.add("top-0");


  if (materialConfiguratorPlugin.current) {
    let variation = materialConfiguratorPlugin.current.findVariation(selectedMesh?.material?.name || "");

    if (variation) {
      setTitle(variation.title);
    } else {
      setTitle(selectedMesh.material.name);
      materialConfiguratorPlugin.current.createVariation(selectedMesh.material , selectedMesh.material.name);
    }
      setIsConfig(true);
      setSelectedOverlayTab(selectedMesh.material.name + "-materialConfiguratorPlugin");
    }
  };



  const changeTitle = (v: string) => {
    setTitle(v);
    if (materialConfiguratorPlugin.current && selectedMesh) {
      let variation = materialConfiguratorPlugin.current.findVariation(selectedMesh.material.name);
      if (variation) {
        variation.title = v;
        materialConfiguratorPlugin.current.refreshUi();
      }
    }
  };

  const getConfigForSelectedObject = () => {
    return materialConfiguratorPlugin.current?.findVariation(selectedMesh?.material?.name);
  };

  const isOptionSelected = (option: any) => {
    // const optionName = option.path.split("/").pop();
  
    if (materialConfiguratorPlugin.current && selectedMesh) {
      let variation = materialConfiguratorPlugin.current.findVariation(selectedMesh.material.name);
      if (variation) {
        return variation.materials.some((m : any) => m.userData?.rootPath === option.path);
      }
    }
    return false;
  };
  

  useEffect(() => {
    if (!props.viewer) return;
    let p = props.viewer.getPluginByType("Picking") as PickingPlugin;
    p?.addEventListener("selectedObjectChanged", async () => {
      let obj = p?.getSelectedObject();
      if (!obj) {
        setSelectedMesh(undefined);
      } else {
        setSelectedMesh(obj);
        if ((obj?.material as any)?.isDiamondMaterial) {
          setSelectedTab("gem-config");
        } else if (obj?.material) {
          setSelectedTab("metal-config");
        }
      }
    });
  }, [props.viewer]);

  useEffect(() => {
    if (isConfig && !selectedMesh) {
      exitConfig();
      console.log("exit config");
    }
    if(!selectedMesh) return;
    let c = getConfigForSelectedObject();
    if(c) {
      setSelectedOverlayTab(selectedMesh?.material?.name + "-materialConfiguratorPlugin");
    }
  }, [selectedMesh , materialConfiguratorConfigurators]);

  useEffect(() => {
    if (!props.viewer || !isConfig) return;
    let mc = props.viewer.getPluginByType(
      "MaterialPresetPlugin"
    ) as MaterialPresetPlugin;
    setMaterialPresets(mc.presets);
  }, [isConfig , props.viewer]);

  const handleOptionClick = useCallback(async (option: any) => {
    if (!selectedMesh || !materialConfiguratorPlugin.current || !props.viewer) return;
    const name = selectedMesh.material.name;
    setOptionLoading(option.path);
    let variation = materialConfiguratorPlugin.current.findVariation(name);

    if(!variation) enterConfig();
    variation = materialConfiguratorPlugin.current.findVariation(name);

    if (variation) {
      let materialIndex = variation.materials.findIndex((m: IMaterial) => m.userData.rootPath && m.userData.rootPath === option.path);
      if (materialIndex >= 0) {
        variation.materials.splice(materialIndex, 1);
        materialConfiguratorPlugin.current.refreshUi();
        props.viewer.setDirty();
      } else {
        const material = selectedTab === "scene-materials" ? option.material.clone?.()
            : (await props.viewer.getManager()!.importer?.importSinglePath<IMaterial>(option.path))?.clone?.()
            
        if (material) {
          material.name = option.path?.split("/")?.pop() || material.name;
          if(!material.userData) material.userData = {};  
          material.userData.rootPath = option.path;
          material.userData.icon = option.icon;
          // material.userData.ijID = option.ijID; //todo: the id of the material file in ij drive
          materialConfiguratorPlugin.current.addVariation(material , name , false);
        }
      }
      
      setSelectedOverlayTab(" ");
      setSelectedOverlayTab(name + "-materialConfiguratorPlugin");
      props.setHasEdited(true);
      // materialConfiguratorPlugin.current.refreshUi();
    }else {
      //create new variation
      enterConfig();
    }
    setOptionLoading("");

  }, [selectedMesh, materialConfiguratorPlugin, props.viewer]);
  

  const deleteConfigurator = () => {
    if (!selectedMesh || !materialConfiguratorPlugin.current) return;
  
    let variation = materialConfiguratorPlugin.current.findVariation(selectedMesh.material.name);
    if (variation) {
      materialConfiguratorPlugin.current.removeVariation(variation);
      materialConfiguratorPlugin.current.refreshUi();
    }
    setIsDeleteOpen(false);
    exitConfig();
  };
  

  const materialGroupExist = useCallback(
    (type: string) => {
      if (!props.viewer) return false;
      const m = props.viewer.getPlugin(MaterialPresetPlugin) 
      return m?.presets[type]?.length;
    },
    [props.viewer]
  );

  useEffect(() => {
    if (!props.viewer || !materialConfiguratorPlugin.current) return;
    if(selectedTab === "scene-materials") {
      materialConfiguratorPlugin.current.updateSceneMaterials = true;
      materialConfiguratorPlugin.current.refreshUi();
      return;
    }
  }, [selectedTab]);

  const getSceneMaterials = () => {
    if (!props.viewer || !materialConfiguratorPlugin) return [];
    const materials = materialConfiguratorPlugin.current?.sceneMaterials
    return materials;
  };

  const showOverlay = materaialPresetConfigurators.length !== 0 || materialConfiguratorConfigurators.length !== 0 || switchNideConfigurators.length !== 0

  return (
    <div className="w-full h-full flex-col absolute inline-block bottom-0 left-1/2 -translate-x-1/2 pointer-events-none p-4">
      <div className="relative w-full h-full overflow-hidden">
        {!isConfig && selectedMesh && (
          <Button
            className={"absolute top-unit-xs p-unit-xl pointer-events-auto text-tiny shadow-md right-0 "  + (props.fullEditor ? "right-unit-4xl" : " ")}
            size="sm"
            name={
              getConfigForSelectedObject()
                ? "Edit Configurator"
                : "Add Configurator"
            }
            fullWidth={false}
            endIcon={splashIcon}
            onClick={() => enterConfig()}
          />
        )}
        <div
          ref={configUi}
          className="absolute top-[-500px] left-20 flex gap-unit-lg transition-all duration-250 pointer-events-auto"
        >
          <Button
            className="p-unit-xl pointer-events-auto text-tiny"
            size="sm"
            name="Hide"
            fullWidth={false}
            // endIcon="/icons/splash.png"
            onClick={() => exitConfig()}
          />
          <Popover
            isOpen={isDeleteOpen}
            onOpenChange={setIsDeleteOpen}
            placement="bottom"
            showArrow
          >
            <PopoverTrigger>
              <NextButton
                fullWidth={false}
                color="danger"
                name="Delete"
                size="lg"
                className="p-unit-xl rounded-small text-tiny"
              >
                Delete
              </NextButton>
            </PopoverTrigger>
            <PopoverContent className="rounded-small">
              <div className="flex flex-col gap-unit-xl py-unit-md text-tiny">
                <p>Do you want to delete the configurator for this object?</p>
                <div className="flex gap-unit-lg w-full justify-center items-center">
                  <Button
                    onClick={deleteConfigurator}
                    fullWidth={false}
                    color="danger"
                    name="Delete"
                    size="lg"
                    className="py-unit-8 px-unit-2xl"
                  ></Button>
                  <Button
                    onClick={() => setIsDeleteOpen(false)}
                    fullWidth={false}
                    color="default"
                    name="Cancel"
                    size="lg"
                    className="py-unit-8 px-unit-2xl"
                  ></Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
          {/* <Button
          className="p-unit-xl pointer-events-auto text-tiny"
          size="sm"
          name="Delete"
          color="danger"
          fullWidth={false}
          endIcon="/icons/splash.png"
          onClick={() => setIsConfig(false)}
        /> */}
        </div>

        <div
          ref={configurator}
          style={{visibility : isConfig ? "visible" : "hidden"}}
          className="w-unit-9xl absolute top-0 transition-all pointer-events-auto duration-250 ease-in-out right-[-500px] overflow-hidden h-[70%]"
        >
          <div className="flex flex-col h-full relative p-4  w-full bg-white/50 rounded-small justify-start overflow-hidden">
            <div className="flex gap-unit-lg items-center">
              {/* <p className="text-small text-default-400  font-normal">
                label
              </p> */}
              <Tooltip delay={500} content={"Label"}>
                <Autocomplete
                  allowsCustomValue
                  onValueChange={changeTitle}
                  onInputChange={changeTitle}
                  value={title}
                  inputValue={title}
                  // label="Select a label"
                  // className="max-w-xs"
                  inputProps={{
                    tabIndex:-1,
                    classNames: {
                      input:
                        "h-unit-2xl placeholder:text-md !placeholder:font-normal !text-sm placeholder:text-default-400",
                      innerWrapper: "h-unit-2xl",
                      base: "shadow-none ",
                      mainWrapper:
                        "bg-[#f7f7f7]  !rounded-small  border-[#e7e9e9] border-1 shadow-none",
                      inputWrapper:
                        "bg-[#f7f7f7] shadow-none rounded-small h-unit-3xl",
                    },
                  }}
                  classNames={{
                    clearButton: "hidden",
                    selectorButton: "hidden",
                    listbox: "p-0  [&>*]:px-unit-",
                    listboxWrapper: "rounded-small",
                    popoverContent: "rounded-small !text-tiny ",
                  }}
                  placeholder="Enter a label"
                >
                  {labels[selectedTab.split("-")[0] as keyof typeof labels]?.map(
                    (label: string) => (
                      <AutocompleteItem key={label} value={label}>
                        {label}
                      </AutocompleteItem>
                    )
                  )}
                </Autocomplete>

              </Tooltip>
            </div>
            {/* <Input
                value={title}
                // onKeyDown={addTag as any}
                onValueChange={(v: string) => changeTitle(v)}
                size="sm"
                // label="Title*"
                // className="placeholder:text-s,a"
                classNames={{
                  // innerWrapper: "h-unit-2xl",
                  input:
                    "h-unit-2xl placeholder:text-md !placeholder:font-normal !text-sm placeholder:text-default-400",
                  innerWrapper: "h-unit-2xl",
                  base: "shadow-none ",
                  mainWrapper:
                    "bg-[#f7f7f7]  !rounded-small  border-[#e7e9e9] border-1 shadow-none",
                  inputWrapper:
                    "bg-[#f7f7f7] shadow-none rounded-small h-unit-3xl",
                }}
                type="text"
                placeholder="Configurator Name"
              /> */}
            <Tabs
              selectedKey={selectedTab}
              onSelectionChange={setSelectedTab as any}
              color="primary"
              variant="underlined"
              fullWidth
              className=" mt-unit-lg "
              classNames={{
                // tabList: [" mb-unit-md"],
                tabContent: "h-full h-unit-2xl mb-unit-md",
                cursor: ["rounded-large "],
                tab: ["text-tiny p-unit-md"],
              }}
            >
              {materialGroupExist("gem") && (
                <Tab
                  key="metal-config"
                  title={
                    <Tooltip delay={300} closeDelay={500} placement="top" content='Metal' className="text-tiny" color="secondary">
                      <img className="h-full" src={metalIcon} />
                    </Tooltip>
                  }
                />
              )}
              {materialGroupExist("metal") && (
                <Tab
                  key="gem-config"
                  title={
                    <Tooltip delay={300} closeDelay={500} placement="top" content='Gem' className="text-tiny" color="secondary">
                      <img className="h-full" src={gemIcon} />
                    </Tooltip>
                  }
                />
              )}
              {materialGroupExist("ceramic") && (
                <Tab
                  key="ceramic-config"
                  title={
                    <Tooltip delay={300} closeDelay={500} placement="top" content="Ceramic" className="text-tiny" color="secondary">
                      <img className="h-full" src={ceramicIcon} />
                    </Tooltip>
                  }
                />
              )}
              {materialGroupExist("pearl") && (
                <Tab
                  key="pearl-config"
                  title={
                    <Tooltip delay={300} closeDelay={500} placement="top" content="Pearl" className="text-tiny" color="secondary">
                      <img className="h-full" src={mattIcon} />
                    </Tooltip>
                  }
                />
              )}
              {/* todo: enable this tab */}
                {/* <Tab
                  key="scene-materials"
                  title={<img className="h-full" src={mattIcon} />}
                /> */}
            </Tabs>
            <ScrollShadow
              hideScrollBar
              className="w-full h-fit flex flex-wrap gap-unit-xl justify-center items-start pt-unit-md mt-4 pb-8"
            >
              {selectedTab === "scene-materials" ? getSceneMaterials()?.map((option : any, i : any) => {
                return (
                  <EditorOption
                    // selected={isOptionSelected(option)}
                    onClick={() => handleOptionClick(option)}
                    key={i + selectedTab}
                    src={option?.icon}
                    // isLocked={option.isPremium && !props.isPremium || (!props.isLogin && i > 3)}
                    isLogin={props.isLogin}
                    setShowSubscribeModal={props.setShowSubscribeModal}
                    // loading={optionLoading === option.path}
                    name={option?.material?.name}
                  />
                );
              }) : materialPresets[selectedTab.split("-")[0]] &&
                materialPresets[selectedTab.split("-")[0] as any].map(
                  (option: any, i: number) => {
                    return (
                      <EditorOption
                        selected={isOptionSelected(option)}
                        onClick={() => handleOptionClick(option)}
                        key={option?.name + "" + i + selectedTab}
                        src={option?.icon}
                        isLocked={option.isPremium && !props.isPremium || (!props.isLogin && i > 3)}
                        isPremium={option.isPremium}
                        isLogin={props.isLogin}
                        setShowSubscribeModal={props.setShowSubscribeModal}
                        loading={optionLoading === option.path}
                        name={option.path?.split("/")?.pop()}
                        isCustomAsset={option.isCustom}
                      />
                    );
                  }
                )}
            </ScrollShadow>
          </div>
        </div>
       {props.viewer && getUrlQueryParam("theme") === null && showOverlay && <OverlayTabs
          // viewer={props.viewer}
          configurators={[...materaialPresetConfigurators, ...materialConfiguratorConfigurators, ...switchNideConfigurators]}
          isConfig={isConfig}
          selectedTab={selectedOverlayTab}
          setSelectedTab={setSelectedOverlayTab}
          selectedMesh={selectedMesh}
          viewer={props.viewer}
          setHasEdited={props.setHasEdited}
          onEdit={enterConfig}
          enableEdit
        />}
      </div>
    </div>
  );
};

export { ConfiguratorEditor };
