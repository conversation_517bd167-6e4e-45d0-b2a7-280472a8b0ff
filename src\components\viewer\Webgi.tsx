import { useEffect, useRef } from "react";

export default function Webgi() {
  const canvas = useRef<HTMLCanvasElement>(null);
  const canvasContainer = useRef<HTMLDivElement>(null);


  return (
    <>
      <div ref={canvasContainer} id="webgi-mini-editor-canvas-container" className="w-full h-full text-center">
        <canvas ref={canvas} id="webgi-mini-editor-canvas" className="w-full h-full top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2 relative"></canvas>
      </div>
    </>
  );
}
