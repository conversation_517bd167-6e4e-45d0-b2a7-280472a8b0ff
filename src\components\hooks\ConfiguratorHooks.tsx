// useMaterialPresetConfigurators.ts
import { useState, useEffect, useCallback } from "react";
import { ViewerApp, SwitchNodePlugin, Mesh, PickingPlugin, MaterialConfiguratorBasePlugin, MaterialVariations, SwitchNodeBasePlugin } from "webgi";
import { MaterialPresetPlugin } from "webgi";
import { Configurator, ConfigOption } from "../OverlayTabs";
import { MaterialConfiguratorPlugin2 } from "../viewer/app";
// import { MaterialConfiguratorPlugin2 } from "../viewer/app";

interface UseMaterialPresetConfiguratorsProps {
  viewer?: ViewerApp;
  project: any;
  setProject: (project: any) => void;
  setHasEdited?: (hasEdited: boolean) => void;
}

export function useLegacyMaterialConfigurators({ viewer, project,setProject, setHasEdited }: UseMaterialPresetConfiguratorsProps): Configurator[] {
  const [configurators, setConfigurators] = useState<Configurator[]>([]);

  useEffect(() => {
    viewer?.scene.addEventListener("addSceneObject", () => {
      setConfigurators(getConfigurators());
    });

    const configuratorsArray = getConfigurators();
    setConfigurators(configuratorsArray);
  }, [viewer, project, setHasEdited]);

  const materialExists = useCallback((c: any) => {
    if (!viewer || !project.configuratorConfig) return;
    let found = false;
    viewer.scene.traverse((o) => {
      //@ts-ignore
      if (o.isMesh) {
        //@ts-ignore
        if (o.material.name === c.name) {
          found = true;
        }
      }
    });

    return found;
  } , [viewer, project]);

  const getConfigurators = useCallback(() => {
    if (!viewer || !project) return [];

    const materialPresetsPlugin = viewer.getPluginByType("MaterialPresetPlugin") as MaterialPresetPlugin;
    if (!materialPresetsPlugin) return []
    const configuratorsArray: Configurator[] = [];

    project.configuratorConfig?.forEach((config: any) => {
      if (!config.options || !config.name) return;

      const name = config.name;
      const title = config.title || config.name;
      if(!materialExists(config)) return;
      const getOption = () => config.options.map((option: any) => {
        let iconPath = option.icon;
        let materialPath = option.name;
        if (project.version && parseInt(project.version?.split("v")[1]) >= 4) {
          if(!option.name.startsWith("http")){
            materialPath = project.basePath + option.name;
            iconPath = project.basePath + option.icon;
          }
        } else {
          const path = option.name.startsWith("http") ? option.name :
            (project.basePath + "materials/" + option.type + "/" + option.name);
          materialPath = path;
          iconPath = path + ".png";
        }

        if(!iconPath) iconPath = materialPath + ".png";

        return {
          name: option.name,
          icon: iconPath,
          path: materialPath,
        };
      });

      const isSelected = (option: ConfigOption) => {
        const material = materialPresetsPlugin.mapping.find((m: any) => m.name === name);
        return material?.path.split("/").pop() === option.name.split("/").pop();
      };

      const applyOption = async (option: ConfigOption) => {
        const presetPath = option.path;
        if (!presetPath) {
          console.error("Material preset not found");
          return;
        }
        await materialPresetsPlugin.addMapping(name, presetPath , true);
        setHasEdited?.(true);
      };

      const handleDelete = (option: ConfigOption) => {
        setProject({
          ...project,
          configuratorConfig: project.configuratorConfig.filter((c: any) => c.name !== name),
        });
      };

      const configurator: Configurator = {
        name,
        title,
        getOptions: getOption,
        isSelected,
        applyOption,
        handleDelete,
        type: "legacyConfigurator",
      };

      configuratorsArray.push(configurator);
    });

    return configuratorsArray;
  } , [viewer, project, setHasEdited]);

  return configurators;
}
interface UseSwitchNodeConfiguratorsProps {
  viewer?: ViewerApp;
}

export function useSwitchNodeConfigurators({ viewer }: UseSwitchNodeConfiguratorsProps): Configurator[] {
  const [configurators, setConfigurators] = useState<Configurator[]>([]);

  useEffect(() => {
    if (!viewer) return;

    const switchNodePlugin = viewer.getPlugin(SwitchNodePlugin);
    if (!switchNodePlugin) return;

    switchNodePlugin.addEventListener("deserialize" as any, () => {
      setConfigurators(getConfigurators());
    });

    setConfigurators(getConfigurators());
  }, [viewer]);

  const getConfigurators = useCallback(() => {
    if (!viewer) return [];
    const configuratorsArray: Configurator[] = [];
    const switchNodePlugin = viewer.getPluginByType("SwitchNodePlugin") as SwitchNodePlugin;
    switchNodePlugin.variations.forEach((variation) => {
      const name = variation.name;
      const title = variation.name;

      const nodeChildren = viewer.scene.getObjectByName(name)?.children || [];

      const getOptions = () => {
        return nodeChildren.map((child) => ({
          name: child.name,
          icon: child.userData.__icon,
        }));
      };

      const isSelected = (option: ConfigOption) => {
        return variation.selected === option.name;
      };

      const applyOption = async (option: ConfigOption) => {
        variation.selected = option.name;
        viewer.setDirty({ sceneUpdate: true, frameFade: true });
      };

      const handleDelete = (option: ConfigOption) => {
        // Implement
      };

      const configurator: Configurator = {
        name,
        title,
        getOptions: getOptions,
        isSelected,
        applyOption,
        // handleDelete,
        type: "switchNode",
      };

      configuratorsArray.push(configurator);
    });

    return configuratorsArray
  }, [viewer]);

  return configurators;
}

interface UseMaterialConfiguratorConfiguratorsProps {
  viewer?: ViewerApp;
  setHasEdited?: (hasEdited: boolean) => void;
}

export function useMaterialConfiguratorConfigurators({ viewer, setHasEdited }: UseMaterialConfiguratorConfiguratorsProps): Configurator[] {
  const [configurators, setConfigurators] = useState<Configurator[]>([]);

  useEffect(() => {
    if (!viewer) return;
  
    const configurators = getConfigurators();
    setConfigurators(configurators);
    const materialConfiguratorPlugin = viewer.getPluginByType("MaterialConfiguratorPlugin")
    if (!materialConfiguratorPlugin) return;
    materialConfiguratorPlugin.addEventListener("deserialize" as any, () => {
      setConfigurators(getConfigurators());
    });
    materialConfiguratorPlugin.addEventListener("refreshUi" as any, () => {
      setConfigurators(getConfigurators());
    });
    viewer.scene.addEventListener("addSceneObject", () => {
      setConfigurators(getConfigurators());
    });


  }, [viewer, setHasEdited]);

  const materialExists = useCallback((variation: any) => {
    if (!viewer) return false;
    let found = false;
    viewer.scene.traverse((object) => {
      //@ts-ignore
      if (object.isMesh && (object).material?.name === variation.uuid) {
        found = true;
      }
    });
    return found;
  }, [viewer]);

  const getConfigurators = useCallback(() => {
    if (!viewer) return [];
    const materialConfiguratorPlugin = viewer.getPluginByType("MaterialConfiguratorPlugin") as MaterialConfiguratorPlugin2;
    if (!materialConfiguratorPlugin) return [];
    const configuratorsArray: Configurator[] = [];
    materialConfiguratorPlugin.variations.forEach((variation : MaterialVariations , i : any) => {
      const name = variation.uuid || variation.title;
      const title = variation.title || variation.uuid;

      if(!materialExists(variation)) return;

      const getOptions = () => {
        return (materialConfiguratorPlugin)?.options[i] || [];
      }

      const isSelected = (option: ConfigOption) => {
        return variation.selectedIndex === option.index;
      };

      const applyOption = async (option: ConfigOption) => {
        await materialConfiguratorPlugin.applyVariation(variation, option.uuid);
        let mat = variation.materials[option.index]
        if(mat){
          const mpp = viewer.getPluginByType("MaterialPresetPlugin") as MaterialPresetPlugin;
          mpp?.addMapping?.(name,"MaterialConfiguratorPlugin:" + option.index , false);
        }
        setHasEdited?.(true);
      };

      const handleDelete = (option: ConfigOption) => {
        let variation = materialConfiguratorPlugin.variations.find((v : any) => v.uuid === option.name);
        if (!variation) return;
        materialConfiguratorPlugin.removeVariation(variation);
      };

      const addCurrentMaterial = async () => {
        const picking = viewer.getPlugin(PickingPlugin)
        const mesh = picking?.getSelectedObject() as Mesh;
        if(!mesh || !mesh.material) return;
        const mat = (mesh.material as any).clone?.()
        if(mat.userData){
          mat.userData.icon = undefined;
        }
        // mat.uuid = variation.uuid;
        materialConfiguratorPlugin.addVariation(mat , name , false);
      }

      const handleOptionLabelChanged = (option: ConfigOption , newName : string) => {
        const material = variation.materials[option.index];
        if(material){
          material.userData.label = newName;
          material.name = newName;
        }
        materialConfiguratorPlugin.refreshUi();

      }

      const configurator: Configurator = {
        name,
        title,
        getOptions: getOptions,
        isSelected,
        applyOption,
        handleDelete,
        addCurrentMaterial,
        handleOptionLabelChanged,
        type: "materialConfiguratorPlugin",
      };

      configuratorsArray.push(configurator);
    });

    return configuratorsArray;
  } , [viewer, setHasEdited]);


  return configurators;
}