import { FC, CSSProperties, useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@nextui-org/react";
import { BoxGeometry, DropzonePlugin, Mesh, MeshPhysicalMaterial, PresetLibraryPlugin, SphereGeometry, ViewerApp } from "webgi";
import CloseIcon from "./icons/Close";
import RingIcon from "./icons/Ring";
import TwoRingIcon from "./icons/TwoRing";
import FlowerIcon from "./icons/Flower"
import WeddingSetIcon from "./icons/WeddingSet"

import SphereIcon from "./icons/Sphere";
import CubeIcon from "./icons/Cube";
import { DefaultEnvMapUrl, FlowerSampleUrl, SingleSampleUrl, TwoRingSampleUrl, WeddingSetSampleUrl } from "./utils/urls";
import { useProject } from "./provider/ProjectProvider";
import { useUi } from "./provider/UiProvider";
import { EditorSettings } from "../main";

interface DropZoneBackground {
  viewer?: ViewerApp;
  editorSettings?: EditorSettings;
  isModelLoadedRef?: any;
}

const DropZoneBackground: FC<DropZoneBackground> = (props) => {
  const [show, setShow] = useState(true);
  const {project , setProject} = useProject();
  const {showDefaultMaterialsPrompt} = useUi();

  useEffect(() => {
    if (!props.viewer) return;
    if (props.viewer!.scene.modelRoot.children.length > 0) setShow(false);
    props.viewer.scene.addEventListener("sceneUpdate", () => {
      if (props.viewer!.scene.modelRoot.children.length > 0) setShow(false);
    });
  }, [props.viewer]);

  const onClick = useCallback(() => {
    if (!props.viewer) return;
    props.viewer.getPlugin(DropzonePlugin)?.promptForFile();
  }, [props.viewer]);

  const close = useCallback(() => {
    if(!props.viewer) return;
    props.viewer.renderEnabled = true;
    setShow(false);
  }, [props.viewer]);

  const forwardDragEvent = useCallback(
    (buttonEvent: React.DragEvent<HTMLElement>) => {
      if (!props.viewer) return;
      buttonEvent.stopPropagation();
      buttonEvent.preventDefault();

      const DropzoneElem = props.viewer?.canvas;

      const { dataTransfer } = buttonEvent;
      const dragEventInit: DragEventInit = {
        bubbles: true,
        cancelable: true,
        composed: true,
        dataTransfer,
      };

      const newEvent = new DragEvent(buttonEvent.type, dragEventInit);
      DropzoneElem.dispatchEvent(newEvent);
    },
    [props.viewer]
  );

  const loadSample = useCallback((sample: string) => {
    if (!props.viewer) return;
    const presetsPlugin = props.viewer.getPlugin(PresetLibraryPlugin);
    const presetGroup = presetsPlugin?.presetGroups.find((group) => group.name === "Environment")
    const material = new MeshPhysicalMaterial({ color: 0xe5b377 , roughness: 0, metalness: 1 , name :  "SampleMaterial" });
    const mat = props.viewer.getManager()!.materials?.processMaterial(material as any , {}) as any;
    props.viewer.renderEnabled = true;
    switch (sample) {
      case "ring":
        props.viewer.load(SingleSampleUrl).then(() => {
          showDefaultMaterialsPrompt(props.viewer!);
          setProject({ ...project});
          if(presetGroup && props.viewer) presetGroup.apply(props.viewer, presetGroup.presets[0])
        })
        break;
      case "2ring":
        props.viewer.load(TwoRingSampleUrl).then(() => {
          showDefaultMaterialsPrompt(props.viewer!);
        })
        break;
      case "wedding-set":
        props.viewer.load(WeddingSetSampleUrl).then(() => {
          showDefaultMaterialsPrompt(props.viewer!);
        })
        break;
      case "flower":
        props.viewer.load(FlowerSampleUrl).then(() => {
          showDefaultMaterialsPrompt(props.viewer!);
        })
        break;
      case "sphere":
        const geometry = new SphereGeometry(1, 32, 32);
        const sphere = new Mesh(geometry, mat);
        props.viewer.scene.modelRoot.add(sphere);
        if(presetGroup) presetGroup.apply(props.viewer , presetGroup.presets[0])
        setProject({ ...project});
        break;
      case "cube":
        const geometry1 = new BoxGeometry(1, 1, 1);
        const cube = new Mesh(geometry1, mat);
        props.viewer.scene.modelRoot.add(cube);
        setProject({ ...project}); // to re-render the editor
        if(presetGroup) presetGroup.apply(props.viewer , presetGroup.presets[0])

        break;
    }
    if(props.isModelLoadedRef) {
      props.isModelLoadedRef.current = true;
    }

  }, [props.viewer]);

  if (!show) return null;

  //todo: make this component configurable with different welcome image or text
  return (
    <div id="dropzone-bg" className="w-full h-full overflow-hidden flex flex-col items-center justify-center  absolute"
      style={{zIndex: 10}}
      onDragOver={(e) => e.preventDefault()}
      onDrop={forwardDragEvent}
    >
      {/* <div className="flex-1" /> */}
      <Tooltip content="Hide Dropzone" placement="left" color="secondary">
        <Button
          onClick={close}
          className="absolute top-unit-xl right-unit-xl z-10 w-fit h-fit rounded-full pointer-events-auto"
          variant="solid"
          isIconOnly
          startContent={
            <div className="w-8 h-8 p-2">
              <CloseIcon className="w-full h-full" />
            </div>
          }
          disableRipple
        ></Button>
      </Tooltip>
      <div className="z-10 w-full flex flex-col items-center px-unit-xl relative">
        <div className=" flex flex-col gap-unit-2xl items-center">
          <img
            src="https://playground.ijewel3d.com/v2/dropzone-icon.webp"
            className="w-[100px] cursor-pointer pointer-events-auto"
            onDragOver={(e) => e.preventDefault()}
            onDrop={forwardDragEvent}
            onClick={onClick}
          />
          <Button
            // onDragEnter={forwardDragEvent}
            onDragOver={(e) => e.preventDefault()}
            onDrop={forwardDragEvent}
            onClick={onClick}
            className="p-unit-xl rounded-full border-default-foreground !border-[1px] pointer-events-auto"
            variant="bordered"
            disableRipple
          >
            Drag and drop a 3D file here
          </Button>
          {props.editorSettings?.showWelcome && <img src={"https://playground.ijewel3d.com/v2/Welcome to iJewel Playground.svg"} className="mt-unit-xl" />}
        </div>
        {props.editorSettings?.showSampleModels && <div className="flex flex-col items-center gap-unit-lg z-10" style={{ translate: "0 50%" }}>
          <p className="text-sm">Load a sample</p>
          <div className="flex gap-unit-lg justify-center">
            <SampleModel Icon={RingIcon} title="Ring" onClick={()=>loadSample("ring")} />
            <SampleModel Icon={TwoRingIcon} title="Two Rings" onClick={()=>loadSample("2ring")} />
            <SampleModel Icon={FlowerIcon} title="Complex Ring" onClick={()=>loadSample("flower")} />
            {/* <SampleModel Icon={WeddingSetIcon} title="Wedding Set" onClick={()=>loadSample("wedding-set")} /> */}
            <SampleModel Icon={SphereIcon} title="Sphere" onClick={()=>loadSample("sphere")} />
            <SampleModel Icon={CubeIcon} title="Cube" onClick={()=>loadSample("cube")} />
          </div>
        </div>}
      </div>
      <img src="https://playground.ijewel3d.com/v2/dropzone-bg.webp" className="w-full h-full object-cover absolute" />

    </div>
  );
};

const SampleModel = ({ Icon, title, onClick } : { Icon: any, title: string, onClick: () => void }) => {
  return (
    <Tooltip content={title} size="sm" placement="bottom" color="secondary">
      <div className="w-unit-3xl h-unit-3xl rounded-md p-unit-xs border-default border-2 text-default-400 hover:text-white hover:bg-primary duration-200 cursor-pointer" onClick={onClick}>
        <Icon className="w-full h-full" />
      </div>
    </Tooltip>
  )
}

export { DropZoneBackground };
