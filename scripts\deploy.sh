#!/usr/bin/env bash

export RCLONE_CONFIG_S3CONN_TYPE=s3
export RCLONE_CONFIG_S3CONN_PROVIDER=Cloudflare
export RCLONE_CONFIG_S3CONN_ENV_AUTH=false
export RCLONE_CONFIG_S3CONN_REGION=auto

if [ -z "$1" ]; then
  echo "Version required not defined"
  exit 1
fi
if [ "$1" != "v$npm_package_version" ]; then
  echo "Version mismatch: $1 != $version"
  exit 1
fi

set -euo pipefail

dist_path="dist"
bucket="ijewel3d-releases"
prefix="libs/mini-editor"
version="$npm_package_version"
tgz_prefix="libs/web"
tgz_file="ijewel3d-mini-editor-$npm_package_version.tgz"

# Check if Changelog.md exists
if [ ! -f "CHANGELOG.md" ]; then
    echo "Error: Changelog.md not found"
    exit 1
fi

# Verify changelog entry exists for the version
if ! grep -q "## \[$version\]" CHANGELOG.md; then
    echo "Error: No changelog entry found for version $version"
    exit 1
fi

# check if rclone is installed
if ! command -v rclone &> /dev/null; then
  echo "rclone not found"
  exit 1
fi

# Fetch the latest version
latest_version_url="https://releases.ijewel3d.com/$prefix/latest-version.txt"
latest_version=$(curl -s $latest_version_url | tr -d '\000' | tr -d '\n')

if [ -z "$latest_version" ]; then
  echo "Unable to retrieve the latest version. Aborting."
  exit 1
fi

echo "Latest version: $latest_version"
echo "Current version: $version"

# Compare versions
newest=$(npx semver "$latest_version" "$version" | tail -n 1)
if [ "$newest" != "$version" ]; then
  echo "Current version is not newer than the latest version. Aborting upload."
  exit 1
fi

if [ ! -d "$dist_path" ]; then
  echo "File not found: $dist_path"
  exit 1
fi

if [ ! -f "$tgz_file" ]; then
  echo "File not found: $tgz_file"
  exit 1
fi

echo "$version" > "$dist_path/latest-version.txt"

echo "Uploading $dist_path to ${bucket}/${prefix}/${version}"
"node_modules/@repalash/rclone.js/bin/rclone" copyto "$dist_path" s3conn:"${bucket}/${prefix}/${version}" --verbose || exit 1

echo "Uploading $tgz_file to ${bucket}/${tgz_prefix}/${tgz_file}"
"node_modules/@repalash/rclone.js/bin/rclone" copyto "$tgz_file" s3conn:"${bucket}/${tgz_prefix}/${tgz_file}" --verbose --s3-no-check-bucket || exit 1

echo "Uploading $dist_path/latest-version.txt to ${bucket}/${prefix}/latest-version.txt"
"node_modules/@repalash/rclone.js/bin/rclone" copyto "$dist_path/latest-version.txt" s3conn:"${bucket}/${prefix}/latest-version.txt" --verbose --s3-no-check-bucket || exit 1
