import { But<PERSON> } from "@nextui-org/react";
import toast, { useToasterStore } from "@minieditor/react-hot-toast";

//keep track of all toasts

export const ConfirmToast = (message: string, id: string, onConfirm: () => void , iconElem?: any) => {
  const rect = document.getElementById("webgi-mini-editor-canvas")?.getBoundingClientRect();
  const duration = 10000;

  if (!rect) return;
  toast(
    (t) => (
      <div className="flex flex-col items-start gap-2 text-sm">
        <p>{message}</p>
        <div className="flex gap-2">
          <Button
            onClick={() => {
              toast.dismiss(id);
              onConfirm();
            }}
            color="primary"
            size="lg"
            endContent={iconElem}
            className="px-unit-xl text-sm"
          >
            Yes
          </Button>
          <Button
            onClick={() => {
              toast.dismiss(id);
            }}
            size="lg"
            className="px-unit-xl text-sm"
          >
            No
          </Button>
        </div>
      </div>
    ),
    {
      id: id,
      duration: duration,
      position: "top-left",
      style: {
        marginLeft: rect.left, // position the toast
      },
    }
  );
};

export const WarningToast = (message: string, id: string, iconElem?: any) => {
  const rect = document.getElementById("webgi-mini-editor-canvas")?.getBoundingClientRect();
  const duration = 5000;

  if (!rect) return;
  toast(
    (t) => (
      <div className="flex flex-col items-start gap-2 text-sm">
        <p>{message}</p>
        <div className="flex gap-2">
          <Button
            onClick={() => {
              toast.dismiss(id);
            }}
            size="lg"
            className="px-unit-xl text-sm"
          >
            OK
          </Button>
        </div>
      </div>
    ),
    {
      id: id,
      duration: duration,
      position: "top-left",
      style: {
        marginLeft: rect.left, // position the toast
      },
    }
  );
};
