import {FC, useCallback, useEffect, useRef, useState} from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>Shadow, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>lt<PERSON>} from "@nextui-org/react";
import {CanvasSnipperPlugin, IAsset, MaterialPresetPlugin, PickingPlugin, PresetGroup, PresetLibraryPlugin, RendererUiPlugin, TPreset, ViewerApp,} from "webgi";

import {useProject} from "./provider/ProjectProvider";
import {AdvancedSettings} from "./AdvancedSettings";
import {AnimatePresence, motion, MotionConfig, useMotionValue} from "framer-motion";
import PresetsOptions from "./PresetsOptions";
import MaterialOptions, { addCustomMaterialPresetsPlgn } from "./MaterialOptions";
import {ExportSettings} from "./ExportSettings";
import PresetIcon from "./icons/Preset";
import EnvIcon from "./icons/Env";
import GemEnvIcon from "./icons/GemEnv";
import BackgroundIcon from "./icons/Background";
import GroundIcon from "./icons/Ground";
import StagesIcon from "./icons/Stages";
import ExportIcon from "./icons/Export";
import TuneIcon from "./icons/Tune";
import metalIcon from "../assets/metal-icon.webp?inline";
import gemIcon from "../assets/gem-icon.webp?inline";
import ceramicIcon from "../assets/ceramic-icon.webp?inline";
import mattIcon from "../assets/matt-icon.webp?inline";
import OtherIcon from "./icons/Other";
import {useUi} from "./provider/UiProvider";
import {EditorSettings} from "../main";
import DoubleArrowRight from './icons/DoubleArrowRight.tsx'
import {IjewelEditorPlugin} from "./viewer/IjewelEditorPlugin.ts";
import {IModel} from "webgi/interfaces";
import {Object3D} from "three";
import { EmbedSettings } from "./EmbedSettings.tsx";
import Code from "./icons/Code.tsx";
import PrimHelpIcon from "./icons/PrimHelpIcon.tsx";
import Poses from "./icons/Poses.tsx";
import PosesSettings from "./PosesSettings.tsx";
import VideoHintsModal from "./VideoHintsModal.tsx";
import type ED_SETTING from "../types.ts";
import { generateCustomSceneSettingFile } from "./utils/functions.ts";
import toast from "@minieditor/react-hot-toast";
import { BrandingSetting } from "./BrandingSetting.tsx";
import Branding from "./icons/Branding.tsx";

const mobileDefaultWidth = 150;
const defaultWidth = 235;

interface EditorTabsProps {
  setHasEdited: any;
  viewer?: ViewerApp;
  editorSettings?: EditorSettings;
  setShowSubscribeModal: any;
  reverseLayout?: boolean;
}

function setTabsWidth(width: number) {
  const tabs = document.getElementById("editor-tabs");
  if (tabs) {
    tabs.style.width = `${width}px`;
  }
}

// todo remove global prop and make state?
let lastSelectedObject: any;
const EditorTabs: FC<EditorTabsProps> = (props) => {
  const [selectedMaterialTab, setSelectedMaterialTab] = useState<PresetMatTab |'hidden'>("metal");
  const scroller = useRef<HTMLDivElement>(null);
  const [selectedMesh, setSelectedMesh] = useState<IModel & Object3D| undefined>(undefined);
  const materialTab = useRef<HTMLDivElement>(null);
  const presetTab = useRef<HTMLDivElement>(null);
  const { project } = useProject();
  const [selectedPresetTab, setSelectedPresetTab] = useState<PresetTab>("Environment");
  const [presetGroups, setPresetGroups] = useState<PresetGroup[]>();
  const { freeze } = useUi();
  const [isDragging, setIsDragging] = useState(false);
  const mWidth = useMotionValue(defaultWidth);
  const [isHidden, setIsHidden] = useState(false);
  const [menuVideosHints, setMenuVideosHints] = useState<{isMenuOpen: boolean, menuType?: string | null }>({isMenuOpen: false , menuType: null})
  const [customAssetsStore, setCustomAssetsStore] = useState<null | Record<string, ED_SETTING.CUSTOM_ASSET_ITEM[]>>(null);
  const [isSceneSettingSaving, setIsSceneSettingSaving] = useState(false);

  const handleDrag = useCallback((event: any, info: any) => {
    // console.log(info);
    const delta =  - info.delta.x * (props.reverseLayout ? -1 : 1)
    let newWidth = mWidth.get() + delta;
    if (newWidth > 188 && newWidth < 400) {
      const val = mWidth.get() + delta
      mWidth.set(val);
      setTabsWidth(val);
    }
  }, []);

  const [lastTab, setLastTab] = useState<{ key: PresetTab | PresetMatTab | ""; isMaterial: boolean }>({
    key: "Environment",
    isMaterial: false,
  });

  const getLastTabUsed = useCallback(
    () =>
      selectedMesh && selectedMaterialTab !== "hidden"
        ? { key: selectedMaterialTab, isMaterial: true }
        : { key: selectedPresetTab, isMaterial: false },
    [selectedPresetTab, selectedMaterialTab, selectedMesh]
  );
  const changeTab = useCallback((key: PresetTab | PresetMatTab | 'hidden', isMaterial?: boolean) => {
    if (isMaterial) {
      setSelectedMaterialTab(key as PresetMatTab);
      setSelectedPresetTab("hidden");
    } else if (key !== "hidden") {
      if(key !== 'hide' && key) setSelectedPresetTab(key);
      setSelectedMaterialTab("hidden");
    }
    if(isHidden && key === 'hide') {
      if(lastTab) changeTab(lastTab.key as any, lastTab.isMaterial);
      return;
    }
    if(key === 'hide') setLastTab(getLastTabUsed())
    setIsHidden(key === 'hide');
    const isMobile = window.matchMedia("(max-width: 1024px)").matches;
    const width = key === 'hide' ? 40 : (mWidth.get() < 41) ? isMobile ? mobileDefaultWidth : defaultWidth : 0;
    if(width) {
      mWidth.set(width);
      setTabsWidth(width);
    }

  }, [getLastTabUsed, setSelectedMaterialTab, mWidth, setSelectedPresetTab, lastTab , isHidden , selectedPresetTab]);

  const addCustomAssets = (viewer: ViewerApp, customAssetsMap: any) => {
    const presetLibrary = viewer.getPlugin(PresetLibraryPlugin)!;
    if (customAssetsMap) {
      presetLibrary.presetGroups.forEach((group: any) => {
        let grpCustomAssets = customAssetsMap[group.name];
        if (group.name == "VJSON" && customAssetsMap["ScenePreset"]?.length > 0) {
          // custom scene preset add to VJSON group of plugin.
          grpCustomAssets = [...(grpCustomAssets ?? []), ...customAssetsMap["ScenePreset"]];
        }

        if (grpCustomAssets) group.addPresets([...grpCustomAssets]);
      });

      if (customAssetsMap?.["Material"]?.length) {
        addCustomMaterialPresetsPlgn(props.viewer, customAssetsMap);
      }
    }
  };
  
  useEffect(() => {
    if (props.editorSettings?.isLogin) {
      props.editorSettings?.customAssets
        ?.getCustomAssets?.()
        .then((data) => {
          setCustomAssetsStore(data);
        })
        .catch((error) => console.log("fetching custom assets failed",error));
    }
  }, [props.editorSettings?.isLogin]);
  
  useEffect(() => {
    if (!props.viewer) return;
    // add custom assets into plugin acknowledge
    if(customAssetsStore) addCustomAssets(props.viewer, customAssetsStore);

    const presetLibrary = props.viewer.getPlugin(PresetLibraryPlugin)!;
    const updateAssets = () => {
      if(customAssetsStore) addCustomAssets(props.viewer as any, customAssetsStore);
      setPresetGroups([...presetLibrary.presetGroups]);
    }
    
    updateAssets();
    const editor = props.viewer.getPlugin(IjewelEditorPlugin);
    editor?.addEventListener("updatedAssets", updateAssets);

    return () => {
      editor?.removeEventListener("updatedAssets", updateAssets);
    };
  }, [props.viewer, customAssetsStore]);

  useEffect(() => {
    if (!props.viewer) return;
    const small = window.matchMedia("(max-width: 1024px)").matches;
    if(small){
      changeTab("hide");
      setIsHidden(true);
    } else changeTab("Environment");
    setTabsWidth(mWidth.get());

  }, [props.viewer]);

  useEffect(() => {
    if(!props.viewer) return;
    let p = props.viewer.getPluginByType("Picking") as PickingPlugin;
    if(!p){
      console.error("Picking plugin not found");
      return;
    }
    const listener = () => {
      let obj = p?.getSelectedObject();
      if (obj === lastSelectedObject) return;
      lastSelectedObject = obj;
      if (!obj || !obj.material) {
        // when mash unselected
        setSelectedMesh(undefined);
        setSelectedPresetTab("Environment");
        setSelectedMaterialTab('hidden');
        if (isHidden) setLastTab({ key: "Environment", isMaterial: false });
        return;
      } else {
        // when mash unselected
        setSelectedMesh(obj);
        if ((obj.material as any)?.isDiamondMaterial || (obj.material as any)?.name?.startsWith?.("Gem")) {
          setSelectedMaterialTab("gem" as PresetMatTab);
          if (isHidden) setLastTab({ key: "gem", isMaterial: true });
        } else {
          setSelectedMaterialTab("metal" as PresetMatTab);
          if (isHidden) setLastTab({ key: "metal", isMaterial: true });
        }
        setSelectedPresetTab("hidden");
      }
    }
  
    p?.addEventListener("selectedObjectChanged", listener);

    return () => {
      p?.removeEventListener("selectedObjectChanged", listener);
    };
  }, [isHidden , props.viewer])
  
  useEffect(() => {
    if (!props?.viewer) return;

    const saveScenePresetEvent = async (e: CustomEvent) => {
      if (!props?.viewer) return;
      if (isSceneSettingSaving) return;

      if (e.detail.type === "saveSceneSetting") {
        const assetType = "ScenePreset";
        const { file } = await generateCustomSceneSettingFile(props.viewer);

        if (!props.editorSettings?.isLogin) {
          toast.error("Please login to create a scene preset", {
            id: "asset-upload-failed",
            duration: 2000,
          });
          return;
        }

        setIsSceneSettingSaving(true);
        let id = toast.loading("Uploading Asset...", {
          id: "asset-upload-start",
        });

        try {
          const snapper = (await props.viewer.getOrAddPlugin(CanvasSnipperPlugin)) as CanvasSnipperPlugin;
          const thumbFile = await snapper.getFile(`snapshot.png`, {
            mimeType: "image/" + "png",
            waitForProgressive: true,
            scale: 0.3,
            displayPixelRatio: 1,
          });
          const newPreset = await props.editorSettings?.customAssets?.handleUploadAsset?.({
            assetFile: file,
            assetType,
            thumbnailFile: thumbFile,
          });

          if (!newPreset || !newPreset.path) throw new Error("Failed to upload asset");

          const presetLibrary = props.viewer.getPlugin(PresetLibraryPlugin)!;
          const presetGroup = presetLibrary.presetGroups?.find((g) => g.name === "VJSON");
          presetGroup?.addPresets([newPreset]);

          setCustomAssetsStore?.((p) => {
            const prev = p ?? {};
            return {
              ...prev,
              ["VJSON"]: [...(prev["VJSON"] ?? []), newPreset],
            };
          });
          toast.dismiss(id);
          toast.success("Successfully added in Scene Preset.", {
            id: "asset-upload-success",
            duration: 5000,
          });
        } catch (error) {
          console.error("Asset upload failed:", error);
          toast.dismiss(id);
          toast.error("Asset upload failed", {
            id: "asset-upload-failed",
            duration: 5000,
          });
        } finally {
          setIsSceneSettingSaving(false);
        }
      }
    };

    window.addEventListener("ijewel-editor-event", saveScenePresetEvent as any);
    return () => {
      window.removeEventListener("ijewel-editor-event", saveScenePresetEvent as any);
    };
  }, [props.viewer, isSceneSettingSaving]);


  useEffect(() => {
    if(!props.viewer) return;
    // a hack to re render the tabs when the assets load
    if(selectedPresetTab === "advanced" && !isHidden) {
      changeTab("Environment");
    }
  }, [props.editorSettings]);

  const presetGroupsExist = (type: PresetGroupTab) => {
    return presetGroups?.find((g) => g.name === type)?.presets?.length;
  };

  const materialGroupExist = useCallback(
    (type: string) => {
      if (!props.viewer) return false;
      const m = props.viewer.getPlugin(MaterialPresetPlugin);
      return m?.presets[type]?.length;
    },
    [props.viewer]
  );

  const tabName =
    selectedMesh && selectedMaterialTab !== "hidden"
      ? getPresetName(selectedMaterialTab as any)
      : getTabName(selectedPresetTab);
  
  return (
    <motion.div
      id="editor-tabs"
      // layout
      // style={{
      //   backgroundColor: "steelblue",
      //   // width: props.width,
      //   // width: 200,
      //   // cursor: isDragging ? "row-resize" : "",
      // }}
      className={`flex h-full overflow-visible justify-end resize-x relative ${props.reverseLayout ? 'flex-row-reverse mr-2' : 'ml-2'}`}
    >
      <motion.div
        className={`h-full flex justify-center items-center absolute ${props.reverseLayout ? 'right-0 cursor-w-resize' : '-left-2 cursor-e-resize'} ${isHidden ? 'hidden' : ''}`}
        drag="x"
        dragConstraints={{ top: 0, left: 0, right: 0, bottom: 0 }}
        dragElastic={0}
        dragMomentum={false}
        onDrag={handleDrag}
        onDragEnd={() => {
          setIsDragging(false);
        }}
        onDragStart={() => {
          setIsDragging(true);
        }}
      >
        <div className="w-unit-xs h-unit-4xl rounded-lg bg-default-300 flex justify-center items-center cursor-e-resize translate-x-1"></div>
      </motion.div>

      <div className={`h-full flex justify-center overflow-y-hidden w-full scrollbar-hide ${isHidden ? 'hidden' : ''}`}>
        <ScrollShadow
          ref={scroller}
          hideScrollBar
          className="w-full h-full flex flex-col items-center "
        >
          <MotionConfig reducedMotion="user">

          <p
              key={"title"}
              className="text-default-foreground font-inherit !text-sm font-semibold mb-unit-xl flex gap-2"
            >
              {tabName}
                {/* // selectedMesh ? (Array.isArray(selectedMesh.material) ? selectedMesh.material : [selectedMesh.material])?.join(", ") : */}
              {['Scene', 'ModelStage', "Environment" ,"Background" ,"Advanced", 'Gem Env', 'Ground', 'Export' , 'Gems', 'Metals'].includes(tabName) && <Button 
                  isIconOnly
                  radius="none"
                  variant="light"
                  disableRipple
                  startContent={<PrimHelpIcon />}
                  onClick={() => setMenuVideosHints({ menuType: tabName === 'Export' ? 'ExportL' : tabName , isMenuOpen: true })}
                />}

             </p>
              <div
                className={"flex h-fit items-start justify-center gap-1 w-full flex-wrap gap-y-unit-xl"}>
                {selectedPresetTab === "advanced" && (
                  <AdvancedSettings viewer={props.viewer} />
                )}
                {selectedPresetTab === "export" && (
                  <ExportSettings viewer={props.viewer} />
                )}
                
                {selectedPresetTab === "embed" && (
                  <EmbedSettings viewer={props.viewer} editorSettings={props.editorSettings} project={project} setShowSubscribeModal={props.setShowSubscribeModal} changeMenuTab={changeTab}/>
                )}
                {selectedPresetTab === "branding" && <BrandingSetting editorSettings={props.editorSettings} project={project} />}
              
              <PosesSettings viewer={props.viewer} selectedPresetTab={selectedPresetTab}/>
               
                {!['advanced' , 'export' , 'embed', 'poses', 'branding'].includes(selectedPresetTab) && (
                    <>
                      {selectedPresetTab !== "hidden" && (
                        <PresetsOptions
                          viewer={props.viewer}
                          setHasEdited={props.setHasEdited}
                          groups={presetGroups}
                          selectedPresetTab={selectedPresetTab}
                          isPremium={props.editorSettings?.isPremium}
                          isBusiness={props.editorSettings?.isBusiness}
                          setShowSubscribeModal={props.setShowSubscribeModal}
                          isLogin={props.editorSettings?.isLogin}
                          customAssetsStore={customAssetsStore}
                          setCustomAssetsStore={setCustomAssetsStore}
                          customAssets={props.editorSettings?.customAssets}
                          fullEditor={props.editorSettings?.fullEditor}
                        />
                      )}
                      {selectedMesh && selectedMaterialTab !== "hidden" && (
                        <MaterialOptions
                          selectedMesh={selectedMesh}
                          viewer={props.viewer}
                          setHasEdited={props.setHasEdited}
                          selectedMaterialTab={selectedMaterialTab}
                          isPremium={props.editorSettings?.isPremium}
                          isBusiness={props.editorSettings?.isBusiness}
                          setShowSubscribeModal={props.setShowSubscribeModal}
                          isLogin={props.editorSettings?.isLogin}
                          customAssetsStore={customAssetsStore}
                          setCustomAssetsStore={setCustomAssetsStore}
                          customAssets={props.editorSettings?.customAssets}
                        />
                      )}
                    </>
                  )}
              </div>
          </MotionConfig>
        </ScrollShadow>
      </div>
      <VideoHintsModal
        addPluginHints={props.editorSettings?.fullEditor}
        viewer={props?.viewer}
        isMenuOpen={menuVideosHints.isMenuOpen}
        menuType={menuVideosHints.menuType as any}
        setMenuVideosHints={setMenuVideosHints}
      />
        <div id='menu-tabs-container' className="w-[50px] flex flex-col items-center relative overflow-visible">
        <ScrollShadow size={10} hideScrollBar className="w-full h-[calc(100%-35px)] mb-10" > 
          <div
            id='menu-material-tabs'
            className={
            "h-fit w-full flex flex-col justify-center relative " +
            (selectedMesh ? "block " : " absolute opacity-0 ")
              }
          >
          {/* <p className="text-small mb-unit-md  h-unit-2xl z-40 font-normal  line-clamp-1">
              Selected: {selectedMesh?.material?.name}
          </p> */}
            <Tabs
                selectedKey={selectedMaterialTab}
                // onSelectionChange={(key) => changeTab(key as any , true)}
                color="primary"
                variant="light"
                fullWidth
            isDisabled={freeze}
            className={
              selectedMesh
                ? "block "
                : " absolute right-0 top-0 opacity-0 pointer-events-none w-full"
            }
                disabledKeys={["title"]}
                classNames={{
              tabList: ["first:rounded-none last:rounded-none flex flex-col gap-unit-xl p-0"],
              tabContent: "text-[15px] group-data-[selected=true]:text-primary h-full",
              cursor: `h-[32px] w-[10px] bg-primary cursor-style absolute rounded-md inset-y-0 ${props.reverseLayout ? '!-left-[12%]': '!left-[90%]'}`,
              tab: `px-unit-md ${props.reverseLayout ? "pl-2" : "pr-4"}`,
                }}
            >
            {materialGroupExist("metal") && (
              <Tab
                key="metal"
                title={
                  <Tooltip delay={300} placement="left" content="Metals">
                    <img
                      src={metalIcon}
                      onClick={() => changeTab("metal" , true)}
                      className={`rounded-full h-full ${
                        selectedMaterialTab === "metal"
                          ? "border-2 border-primary h-full"
                          : ""
                      }`}
                    />
                  </Tooltip>
                }
              />
            )}
            {materialGroupExist("gem") && (
              <Tab
                key="gem"
                title={
                  <Tooltip delay={300} placement="left" content="Gems">
                    <img
                      src={gemIcon}
                      onClick={() => changeTab("gem" , true)}
                      className={`rounded-full h-full ${
                        selectedMaterialTab === "gem"
                          ? "border-2 border-primary"
                          : ""
                      }`}
                    />
                  </Tooltip>
                }
              />
            )}
            {materialGroupExist("ceramic") && (
              <Tab
                key="ceramic"
                title={
                  <Tooltip delay={300} placement="left" content="ceramic">
                    <img
                      src={ceramicIcon}
                      onClick={() => changeTab("ceramic" , true)}
                      className={`rounded-full h-full ${
                        selectedMaterialTab === "ceramic"
                          ? "border-2 border-primary"
                          : ""
                      }`}
                    />
                  </Tooltip>
                }
              />
            )}
            {materialGroupExist("pearl") && (
              <Tab
                key="pearl"
                title={
                  <Tooltip delay={300} placement="left" content="pearl">
                    <img
                      src={mattIcon}
                      onClick={() => changeTab("pearl" , true)}
                      className={`rounded-full h-full ${
                        selectedMaterialTab === "pearl"
                          ? "border-2 border-primary"
                          : ""
                      }`}
                    />
                  </Tooltip>
                }
              />
            )}
            {materialGroupExist("other") && (
              <Tab
                key="other"
                className="mb-4"
                title={
                  <Tooltip delay={300} placement="left" content="other">
                    {/* <img
                      src={otherIcon}
                      onClick={() => changeTab("other" , true)}
                      className={`rounded-full h-full ${
                        selectedMaterialTab === "other"
                          ? "border-2 border-primary"
                          : ""
                      }`}
                    /> */}
                    <div className="w-full h-full">
                      <OtherIcon className="w-full h-full" onClick={() => changeTab("other" , true)}/>
                    </div>
                  </Tooltip>
                }
              />
            )}
            <Tab key="hidden" title="" className="absolute bottom-0 opacity-0 pointer-events-none w-full -z-20"/>
            </Tabs>
          </div>

          <Tabs
              id="menu-preset-tabs"
              ref={presetTab}
              selectedKey={selectedPresetTab}
              onSelectionChange={(key) => changeTab(key as PresetTab)}
          color="default"
              variant="light"
          isDisabled={freeze}
              fullWidth
              classNames={{
                tabList: ["first:rounded-none last:rounded-none flex flex-col gap-unit-xl p-0 h-full"],
            tabContent:
              " group-data-[selected=true]:text-primary text-default-foreground",
              cursor: `h-[32px] w-[10px] bg-primary cursor-style absolute rounded-md inset-y-0 ${props.reverseLayout ? '!-left-[12%]': '!left-[90%]'}`,
                tab: `px-unit-md !text-default ${props.reverseLayout ? "pl-2" : "pr-4"}`,
              }}
            >
              {/* {selectedMesh && <Tab key="metal" title="Metal" />}
          {selectedMesh && <Tab key="gem" title="Gem" />} */}

          {presetGroupsExist("Environment") && (
            <Tab
              key="Environment"
              title={
                <Tooltip delay={300} placement="left" content="Environments">
                  <div onClick={() => changeTab("Environment")}>
                    <EnvIcon />
                  </div>
                </Tooltip>
              }
            />
          )}
          {presetGroupsExist("GemEnvironment") && (
            <Tab
              key="GemEnvironment"
              title={
                <Tooltip
                  delay={300}
                  placement="left"
                  content="Gem Environments"
                >
                  <div onClick={() => changeTab("GemEnvironment")}>
                    <GemEnvIcon />
                  </div>
                </Tooltip>
              }
            />
          )}
          {presetGroupsExist("Background") && (
            <Tab
              key="Background"
              title={
                <Tooltip delay={300} placement="left" content="Backgrounds">
                  <div onClick={() => changeTab("Background")}>
                    <BackgroundIcon />
                  </div>
                </Tooltip>
              }
            />
          )}
          {presetGroupsExist("VJSON") && (
            <Tab
              key="VJSON"
              title={
                <Tooltip delay={300} placement="left" content="Scene Presets">
                  <div onClick={() => changeTab("VJSON")}>
                    <PresetIcon />
                  </div>
                </Tooltip>
              }
            />
          )}
          {presetGroupsExist("ModelStage") && (
            <Tab
              key="ModelStage"
              title={
                <Tooltip delay={300} placement="left" content="Stages">
                  <div onClick={() => changeTab("ModelStage")}>
                    <StagesIcon />
                  </div>
                </Tooltip>
              }
            />
          )}
          {presetGroupsExist("Ground") && (
            <Tab
              key="Ground"
              title={
                <Tooltip delay={300} placement="left" content="Grounds">
                  <div onClick={() => changeTab("Ground")}>
                    <GroundIcon />
                  </div>
                </Tooltip>
              }
            />
          )}
          {props.editorSettings?.showAdvanced && (
            <Tab
              key="advanced"
              title={
                <Tooltip
                  delay={300}
                  placement="left"
                  content="Advanced Settings"
                >
                  <div onClick={() => changeTab("advanced")}>
                    <TuneIcon />
                  </div>
                </Tooltip>
              }
            />
          )}
          <Tab
            key="poses"
            title={
              <Tooltip delay={300} placement="left" content='Poses'>
                <div onClick={() => changeTab("poses")}>
                  <Poses/>
                </div>
              </Tooltip>
          }/>
          {props.editorSettings?.showExport && (
            <Tab
              key="export"
              title={
                <Tooltip delay={300} placement="left" content="Export">
                  <div onClick={() => changeTab("export")}>
                    <ExportIcon />
                  </div>
                </Tooltip>
              }
            />
          )}
          {props.editorSettings?.showEmbedding && props.editorSettings.embed && <Tab
            key="embed"
            title={
              <Tooltip delay={300} placement="left" content="Embed Settings">
                <div onClick={() => changeTab("embed")}>
                  <Code />
                </div>
              </Tooltip>
            }
          />}
          {props.editorSettings?.showBrandingSetting && props.editorSettings.embed && <Tab
            key="branding"
            title={
              <Tooltip delay={300} placement="left" content="Branding Settings">
                <div onClick={() => changeTab("branding")}>
                  <Branding />
                </div>
              </Tooltip>
            }
          />}
            <Tab key="hidden" title="" className="absolute top-0 opacity-0 pointer-events-none w-full -z-20"/>
              
          {/* <Tab key="CameraViews" title="Camera views" /> */}
        </Tabs>
        </ScrollShadow>
        <Button
          variant="light"
          className={`absolute justify-center bottom-0 h-[32px] mx-auto ${props.reverseLayout ? "pl-2" : "pr-4"}`}
          onClick={() => changeTab("hide")}
        >
          <Tooltip delay={300} placement="left" content={`${isHidden ? "Show" : "Hide"} Panel`}>
            <div>
              <DoubleArrowRight
                className={(() => {
                  let className = isHidden ? "rotate-180" : "";
                  if (!props.reverseLayout) className = isHidden ? "" : "rotate-180";
                  return className;
                })()}
              />
            </div>
          </Tooltip>
        </Button>
      </div>
    </motion.div>
  );
};

export type  PresetGroupTab = "Environment" | "GemEnvironment" | "Background" | "VJSON" | "Ground" | "ModelStage"
export type  PresetMatTab = "gem" | "metal" | "ceramic" | "pearl" | "other"
export type  PresetTab = PresetGroupTab | PresetMatTab |
    "advanced" | "export" | "hidden" | "hide" | 'embed'| 'poses' | 'branding';

const getTabName = (key: PresetTab) => {
  switch (key) {
    case "VJSON":
      return "Scene";
    case "Environment":
      return "Environment";
    case "GemEnvironment":
      return "Gem Env";
    case "Background":
      return "Background";
    case "Ground":
      return "Ground";
    case "advanced":
      return "Advanced";
    case "export":
      return "Export";
    case "poses":
      return "Poses";
    case "embed":
      return "Embed (Beta)";
    case "branding":
      return "Branding";   
    default:
      return key;
  }
};

const getPresetName = (key: PresetMatTab) => {
  switch (key) {
    case "metal":
      return "Metals";
    case "gem":
      return "Gems";
    case "ceramic":
      return "Ceramics";
    case "pearl":
      return "Pearls";
    case "other":
      return "Other";
    default:
      return key;
  }
};

    

export {EditorTabs};