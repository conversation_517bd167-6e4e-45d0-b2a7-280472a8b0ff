export async function processImage(file: File, targetWidth: number, targetHeight: number): Promise<string> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.src = URL.createObjectURL(file);
  
      img.onload = () => {
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");
  
        const targetAspectRatio = targetWidth / targetHeight;
        const imageAspectRatio = img.width / img.height;
  
        let drawWidth, drawHeight, drawX, drawY;
  
        // If the image's aspect ratio is greater than the target's,
        // it means the image is wider than the target.
        if (imageAspectRatio > targetAspectRatio) {
          drawHeight = targetHeight;
          drawWidth = drawHeight * imageAspectRatio;
          drawX = (targetWidth - drawWidth) / 2;
          drawY = 0;
        } else {
          drawWidth = targetWidth;
          drawHeight = drawWidth / imageAspectRatio;
          drawX = 0;
          drawY = (targetHeight - drawHeight) / 2;
        }
  
        canvas.width = targetWidth;
        canvas.height = targetHeight;
        ctx?.drawImage(img, drawX, drawY, drawWidth, drawHeight);
  
        // Convert the canvas data to a Blob in the desired format
        let format = canvas.toDataURL("image/webp").indexOf("data:image/webp") === 0 ? "image/webp" : "image/png";
        format = format === "image/png" ? "image/jpg" : format;
        let data = canvas.toDataURL(format, 1.0)
        resolve(data);
        // canvas.toBlob((blob) => {
        //   if (!blob) {
        //     console.error("Failed to convert canvas to blob");
        //     return;
        //   }
  
        //   // Convert the Blob to a File and return it
        //   const processedFile = new File([blob], `processed_image.${format === "image/webp" ? "webp" : "png"}`, {
        //     type: format,
        //   });
        //   resolve(processedFile);
        // }, format);
      };
  
      img.onerror = () => {
        console.error("Failed to load the image");
      };
    });
  }