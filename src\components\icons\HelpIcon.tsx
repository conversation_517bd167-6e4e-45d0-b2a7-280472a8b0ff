import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="10"
    height="10"
    viewBox="0 0 10 10"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
    {...props}
  >
    <path d="M4.99995 7.99043C5.16662 7.99043 5.30829 7.9321 5.42495 7.81543C5.54162 7.69876 5.59995 7.5571 5.59995 7.39043C5.59995 7.22376 5.54162 7.0821 5.42495 6.96543C5.30829 6.84876 5.16662 6.79043 4.99995 6.79043C4.83328 6.79043 4.69162 6.84876 4.57495 6.96543C4.45828 7.0821 4.39995 7.22376 4.39995 7.39043C4.39995 7.5571 4.45828 7.69876 4.57495 7.81543C4.69162 7.9321 4.83328 7.99043 4.99995 7.99043ZM4.54995 6.07793H5.46245C5.46245 5.7696 5.48953 5.55085 5.5437 5.42168C5.59787 5.29251 5.72912 5.12793 5.93745 4.92793C6.22912 4.6446 6.4312 4.40293 6.5437 4.20293C6.6562 4.00293 6.71245 3.7821 6.71245 3.54043C6.71245 3.0821 6.5562 2.70918 6.2437 2.42168C5.9312 2.13418 5.53329 1.99043 5.04995 1.99043C4.62495 1.99043 4.2562 2.10293 3.9437 2.32793C3.6312 2.55293 3.41245 2.8571 3.28745 3.24043L4.09995 3.57793C4.17495 3.3446 4.2937 3.16335 4.4562 3.03418C4.6187 2.90501 4.80828 2.84043 5.02495 2.84043C5.25829 2.84043 5.44995 2.9071 5.59995 3.04043C5.74995 3.17376 5.82495 3.34876 5.82495 3.56543C5.82495 3.7571 5.76037 3.92793 5.6312 4.07793C5.50204 4.22793 5.35828 4.37376 5.19995 4.51543C4.90829 4.7821 4.72703 5.00085 4.6562 5.17168C4.58537 5.34251 4.54995 5.6446 4.54995 6.07793ZM4.99995 9.79043C4.34162 9.79043 3.72078 9.66543 3.13745 9.41543C2.55412 9.16543 2.0437 8.82168 1.6062 8.38418C1.1687 7.94668 0.824951 7.43626 0.574951 6.85293C0.324951 6.2696 0.199951 5.64876 0.199951 4.99043C0.199951 4.32376 0.324951 3.70085 0.574951 3.12168C0.824951 2.54251 1.1687 2.03418 1.6062 1.59668C2.0437 1.15918 2.55412 0.81543 3.13745 0.56543C3.72078 0.31543 4.34162 0.19043 4.99995 0.19043C5.66662 0.19043 6.28954 0.31543 6.8687 0.56543C7.44787 0.81543 7.9562 1.15918 8.3937 1.59668C8.8312 2.03418 9.17495 2.54251 9.42495 3.12168C9.67495 3.70085 9.79995 4.32376 9.79995 4.99043C9.79995 5.64876 9.67495 6.2696 9.42495 6.85293C9.17495 7.43626 8.8312 7.94668 8.3937 8.38418C7.9562 8.82168 7.44787 9.16543 6.8687 9.41543C6.28954 9.66543 5.66662 9.79043 4.99995 9.79043ZM4.99995 8.89043C6.08328 8.89043 7.00412 8.51126 7.76245 7.75293C8.52079 6.9946 8.89995 6.07376 8.89995 4.99043C8.89995 3.9071 8.52079 2.98626 7.76245 2.22793C7.00412 1.4696 6.08328 1.09043 4.99995 1.09043C3.91662 1.09043 2.99578 1.4696 2.23745 2.22793C1.47912 2.98626 1.09995 3.9071 1.09995 4.99043C1.09995 6.07376 1.47912 6.9946 2.23745 7.75293C2.99578 8.51126 3.91662 8.89043 4.99995 8.89043Z" />
  </svg>
);
export default SvgComponent;
