import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={18}
    height={18}
    fill="none"
    {...props}
  >
    <path
      fill="currentColor"
      d="M6.692 13.039 12.981 9 6.692 4.962v8.077ZM2.308 17.5c-.505 0-.933-.175-1.283-.525A1.745 1.745 0 0 1 .5 15.692V2.308c0-.505.175-.933.525-1.283C1.375.675 1.803.5 2.308.5h13.384c.505 0 .933.175 1.283.525.35.35.525.778.525 1.283v13.384c0 .505-.175.933-.525 1.283-.35.35-.778.525-1.283.525H2.308Zm0-1.5h13.384a.294.294 0 0 0 .212-.096.294.294 0 0 0 .096-.212V2.308a.294.294 0 0 0-.096-.212.294.294 0 0 0-.212-.096H2.308a.294.294 0 0 0-.212.096.294.294 0 0 0-.096.212v13.384c0 .077.032.148.096.212a.294.294 0 0 0 .212.096Z"
    />
  </svg>
)
export default SvgComponent
