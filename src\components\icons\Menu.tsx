import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={800}
    height={800}
    fill="none"
    viewBox="0 0 24 24"
    // className="backdrop-invert"
    {...props}
  >
    <path
      // className="backdrop-invert"
      // style={{mixBlendMode : "difference"}}
      stroke="currentColor"
      strokeLinecap="round"
      strokeWidth={2}
      d="M4 18h6M4 12h12M4 6h16"
    />
  </svg>
)
export default SvgComponent
