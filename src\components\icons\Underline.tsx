import * as React from "react"
import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={20}
    fill="none"
    {...props}
  >
    <mask
      id="a"
      width={20}
      height={21}
      x={0}
      y={-1}
      maskUnits="userSpaceOnUse"
      style={{
        maskType: "alpha",
      }}
    >
      <path fill="#D9D9D9" d="M0-.01h20v20H0z" />
    </mask>
    <g >
      <path
        fill="currentColor"
        d="M5.289 16.26v-1.083h9.422v1.084H5.29ZM10 13.598c-1.226 0-2.179-.368-2.857-1.105-.679-.737-1.018-1.725-1.018-2.964V3.334h1.424v6.27c0 .798.213 1.437.639 1.917.426.48 1.03.72 1.813.72.782 0 1.387-.24 1.812-.72.426-.48.639-1.119.639-1.917v-6.27h1.423v6.194c0 1.24-.34 2.227-1.018 2.964-.678.737-1.63 1.105-2.857 1.105Z"
      />
    </g>
  </svg>
)
export default SvgComponent
