{"name": "@ijewel3d/mini-editor", "version": "0.1.40", "main": "dist/bundle.js", "module": "dist/bundle.js", "files": ["dist"], "type": "module", "scripts": {"dev": "vite", "dev:wt": "tsc && vite build --watch", "build": "tsc && vite build", "build-iife": "tsc && vite --config vite.config-iife.js build", "build-t-editor": "tsc && vite --config vite.config-textEditor.ts build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "deploy": "bash scripts/deploy.sh", "preview": "vite preview"}, "dependencies": {"@lexical/react": "^0.17.0", "@nextui-org/react": "2.2.9", "framer-motion": "^10.16.11", "lexical": "^0.17.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@minieditor/react-hot-toast": "2.4.2"}, "peerDependencies": {"@lexical/react": "^0.17.0", "@nextui-org/react": "2.2.9", "framer-motion": "^10.16.11", "lexical": "^0.17.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^22.8.2", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/webgi": "https://dist.pixotronics.com/webgi/runtime/bundle-types-0.11.2.tgz", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "path": "^0.12.7", "postcss": "^8.4.31", "rollup-plugin-node-externals": "^6.1.2", "rollup-plugin-visualizer": "^5.12.0", "tailwindcss": "^3.3.5", "typescript": "^5.7.3", "vite": "^5.0.0", "vite-plugin-css-injected-by-js": "^3.3.0", "vite-plugin-dts": "^3.7.3", "webgi": "https://dist.pixotronics.com/webgi/runtime/bundle-0.11.2.tgz", "@repalash/rclone.js": "^0.6.6", "semver": "^7.7.1"}}