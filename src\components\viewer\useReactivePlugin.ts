import { useMemo, useReducer } from "react";
import { AViewerPlugin } from "webgi";

export default function useReactivePlugin(pluginInstance: AViewerPlugin<any> | undefined) {
    const [, forceUpdate] = useReducer((x) => x + 1, 0);
    const reactivePluginInstance = useMemo(() => {
      if (!pluginInstance) return pluginInstance;
    return new Proxy(pluginInstance, {
      set(target, property, value) {
        const result = Reflect.set(target, property, value);
        forceUpdate(); // Trigger re-render
        return result;
      },
      // Extend this proxy as needed for other operations
    });
  }, [pluginInstance]);

  return reactivePluginInstance;
}
