import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={84}
    height={20}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)">
      <path
        fill="#373737"
        d="M4.581 6.662a.595.595 0 0 0-.152-.404.715.715 0 0 0-.39-.226L6.73 4.964v11.92c0 .8-.212 1.516-.64 2.143-.426.627-1.07.952-1.934.973-.972-.055-1.664-.53-2.086-1.422-.168-.37-.25-.75-.25-1.141h.215c0 .37.076.719.229 1.046.165.284.425.471.777.554.353.082.667.052.95-.095.196-.12.333-.318.419-.597.082-.278.133-.556.146-.838.016-.278.022-.477.022-.596L4.58 6.66ZM16.07 9.8c0 .015-.015.024-.044.03h.044v.208H9.718c-.022.817.168 1.627.571 2.432.4.804 1.067 1.312 1.994 1.523 1.143.14 2.171-.132 3.083-.817.158-.14.304-.293.434-.459a.117.117 0 0 1 .064-.042.102.102 0 0 1 .*************** 0 0 1 .*************** 0 0 1-.022.083 5.368 5.368 0 0 1-.444.48c-.86.92-1.943 1.471-3.248 1.652-1.464.049-2.613-.444-3.448-1.48-.835-1.038-1.283-2.175-1.34-3.43a5.366 5.366 0 0 1 .642-2.82c.47-.874 1.206-1.51 2.203-1.908a5.232 5.232 0 0 1 2.508-.263c1.2.245 2.073.89 2.616 1.936.428.887.628 1.817.61 2.793Zm-6.352.033h4.105a.098.098 0 0 0 .01-.03c.339-.866.52-1.71.542-2.533-.035-1.003-.562-1.66-1.575-1.967-1.193-.223-2.082.178-2.66 1.203a6.68 6.68 0 0 0-.422 2.804v.523ZM32.258 5.49a.357.357 0 0 0-.336-.492h1.314c-.355.027-.603.199-.75.514l-3.736 9.353.054.137h-2.311l-2.194-5.511-2.149 5.374.054.137h-2.311l-3.769-9.457c-.146-.32-.396-.501-.758-.544h3.387a.398.398 0 0 0-.298.171.356.356 0 0 0-.038.34l3.625 9.06 2.137-5.366-1.454-3.66c-.146-.322-.397-.502-.76-.545h3.388-.01a.407.407 0 0 0-.25.131.37.37 0 0 0-.076.38l.327.795.108.293 3.19 7.971 3.616-9.08ZM41.075 9.8c0 .015-.016.024-.045.03h.045v.208h-6.353c-.022.817.168 1.627.568 2.432.403.804 1.067 1.312 1.994 1.523 1.143.14 2.172-.132 3.083-.817.158-.14.304-.293.435-.459.012-.02.035-.033.066-.042a.102.102 0 0 1 .*************** 0 0 1 .*************** 0 0 1-.022.083 5.353 5.353 0 0 1-.444.48c-.86.92-1.943 1.471-3.248 1.652-1.464.049-2.61-.444-3.448-1.48-.835-1.038-1.282-2.182-1.34-3.436a5.335 5.335 0 0 1 .642-2.82c.47-.874 1.206-1.51 2.203-1.908a5.232 5.232 0 0 1 2.508-.263c1.2.245 2.073.89 2.616 1.936.429.893.632 1.823.61 2.799Zm-6.353.033h4.105a.098.098 0 0 0 .01-.03c.34-.866.52-1.71.542-2.533-.038-1.003-.562-1.66-1.574-1.966-1.194-.224-2.083.177-2.66 1.202a6.68 6.68 0 0 0-.423 2.804v.523ZM44.148 14.446c0 .146.05.272.152.376a.582.582 0 0 0 .381.177h-3.225a.606.606 0 0 0 .38-.171.505.505 0 0 0 .153-.37V1.612a.543.543 0 0 0-.152-.382.63.63 0 0 0-.381-.193L44.148 0v14.446Z"
      />
      <path
        fill="#373737"
        d="M1.044 6.82v7.589l2.162-.679V5.178L.514 6.224a.594.594 0 0 1 .53.596ZM3.717 1.006l-.793-.58C2.92.421 2.917.421 2.914.418h-.003c-.003-.003-.01-.003-.013-.003H.82c-.003 0-.01 0-.013.003C.803.422.8.422.796.425l-.793.581h3.714ZM.029 1.126l1.197 1.021.606.517h.003c.003.003.006.003.01.006.003.003.01.003.012.003.004 0 .01 0 .013-.003.003 0 .006-.003.01-.006h.003l1.803-1.541-3.657.003Z"
      />
      <path
        fill="#6E72F2"
        d="M52.796 9.078a.337.337 0 0 1 0-.16c.003-.024.006-.051.012-.076a1.961 1.961 0 0 0-.13-.874c-.111-.273-.314-.487-.616-.643a1.745 1.745 0 0 0-1.162-.104c-.403.123-.72.373-.952.759a1.678 1.678 0 0 0-.235 1.18.841.841 0 0 0 .352.478c.188.122.372.22.553.296.257.113.524.224.793.33.381.141.75.3 1.108.478.362.18.68.413.953.703.31.346.463.743.457 1.2a2.18 2.18 0 0 1-.838 1.587 3.149 3.149 0 0 1-1.74.69c-.68.037-1.267-.125-1.762-.489-.489-.367-.806-.868-.946-1.51-.003-.025-.006-.053-.013-.083a.639.639 0 0 1-.016-.257l.172.006c.114.003.244.01.394.015.149.004.279.007.393.013.115.003.172.006.172.006.003 0 .003.021 0 .06-.003.04-.01.09-.016.145-.003.052-.01.098-.016.143l-.006.09c-.035.504.088.938.365 1.296.282.355.704.508 1.273.456.498-.08.867-.306 1.108-.676.244-.37.365-.786.365-1.254-.006-.41-.206-.707-.6-.884-.365-.184-.74-.346-1.124-.49a6.603 6.603 0 0 1-1.66-.835c-.486-.348-.772-.804-.854-1.373-.035-.651.18-1.171.65-1.554a2.933 2.933 0 0 1 1.648-.67c.422-.04.841-.012 1.254.083.416.095.784.282 1.108.554.324.272.54.608.65 1.012.004.012.007.022.014.028.034.11.044.22.034.33h-1.142v-.006Zm5.578-2.022h2.987v2.291a2.183 2.183 0 0 0-.702-1.554c-.438-.419-.968-.627-1.596-.627h-.69v7.466c0 .077.026.144.08.202.054.055.12.086.2.095h-1.695a.307.307 0 0 0 .2-.095.286.286 0 0 0 .079-.202V7.166h-.68c-.628 0-1.161.208-1.609.627-.441.413-.676.933-.708 1.554v-2.29h4.134Zm8.86 0h.68a.287.287 0 0 0-.2.089.256.256 0 0 0-.08.193c0 .29-.003.7-.006 1.23v3.703a2.991 2.991 0 0 1-.632 1.691c-.216.273-.482.49-.8.655-.34.171-.714.272-1.13.303-.416.024-.8-.028-1.159-.16a2.858 2.858 0 0 1-1.05-.718 2.196 2.196 0 0 1-.524-1.015 5.562 5.562 0 0 1-.121-1.147V7.353a.274.274 0 0 0-.08-.199.323.323 0 0 0-.206-.095h1.699a.366.366 0 0 0-.207.11.304.304 0 0 0-.08.202c0 .422-.002.841-.005 1.263a80.556 80.556 0 0 0 .012 3.16c0 .66.15 1.287.451 1.884.136.263.324.492.559.688.558.41 1.152.462 1.784.16.295-.157.55-.368.759-.628.384-.501.59-1.064.616-1.688.003-.153.006-.55.012-1.193.004-.642.007-1.318.007-2.034 0-.716-.004-1.26-.007-1.636a.283.283 0 0 0-.092-.202.276.276 0 0 0-.2-.089Zm4.36.077c1.225.284 2.006 1.06 2.348 2.324.118.505.172 1.016.16 1.526a5.84 5.84 0 0 1-.236 1.725 3.262 3.262 0 0 1-.889 1.456 2.988 2.988 0 0 1-1.746.737 8.827 8.827 0 0 1-.695.028H68.218c.19-.074.286-.193.286-.364V7.42c0-.168-.095-.29-.286-.364h1.416l.908.006c.229-.006.464 0 .702.022.114.009.232.024.35.049Zm-.417 7.533a3.105 3.105 0 0 0 1.438-1.539 5.278 5.278 0 0 0 .435-2.131 4.93 4.93 0 0 0-.428-2.132c-.292-.667-.775-1.18-1.442-1.539a1.545 1.545 0 0 0-.638-.159l-.908-.012v7.677h.905c.22 0 .432-.055.638-.165Zm4.985-7.61c-.188.113-.28.266-.28.456v7.065c0 .08.026.153.073.22.054.068.12.11.207.132h-1.696c.188-.074.28-.193.28-.364V7.42c0-.168-.092-.29-.28-.364h1.696ZM83.05 8.87c.133.21.244.434.336.664.178.468.267.95.267 1.45 0 .501-.089.987-.267 1.455-.092.23-.203.453-.336.664a5.136 5.136 0 0 1-.445.593 3.92 3.92 0 0 1-.546.496c-.193.15-.4.281-.616.391a3.15 3.15 0 0 1-1.387.337 3.101 3.101 0 0 1-1.381-.337 3.701 3.701 0 0 1-.616-.391 3.94 3.94 0 0 1-.55-.496 5.136 5.136 0 0 1-.444-.593 4.096 4.096 0 0 1-.336-.664 4.07 4.07 0 0 1-.267-1.456c0-.498.09-.981.267-1.45.092-.229.203-.452.336-.663.134-.211.283-.41.445-.593.168-.184.35-.35.55-.499.193-.15.4-.278.615-.385.441-.22.902-.33 1.381-.337.483.003.946.116 1.387.337.216.107.423.235.616.385.197.15.381.318.546.499.162.183.311.382.445.593Zm-.623 3.318a7.28 7.28 0 0 0 0-2.4 6.597 6.597 0 0 0-.13-.594 4.206 4.206 0 0 0-.216-.587 3.469 3.469 0 0 0-.32-.55 2.464 2.464 0 0 0-.457-.463 2.038 2.038 0 0 0-1.248-.428c-.467.006-.883.15-1.245.428-.174.135-.326.291-.457.462a3.217 3.217 0 0 0-.32.55c-.09.19-.16.386-.216.588a5.677 5.677 0 0 0-.13.593c-.064.401-.099.799-.099 1.2 0 .403.032.804.099 ***********.079.395.13.594.057.202.13.397.216.587.089.19.193.373.32.545.13.17.283.327.457.467.362.276.775.42 1.245.429a2.081 2.081 0 0 0 1.248-.429c.174-.14.327-.293.457-.467.127-.172.231-.355.32-.545.09-.19.16-.385.216-.587a7.6 7.6 0 0 0 .13-.594Z"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h84v20H0z" />
      </clipPath>
    </defs>
  </svg>
)
export default SvgComponent
