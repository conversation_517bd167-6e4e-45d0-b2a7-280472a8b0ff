import {
  addEditorPlugins,
  CanvasSnipperPlugin,
  CoreEditorApp,
  DiamondPlugin,
  DropzonePlugin,
  LoadingScreenPlugin,
  OutlinePlugin,
  PickingPlugin,
  Rhino3dmLoadPlugin,
  TweakpaneUiPlugin,
  ViewerApp,
  AssetExporterPlugin,
  addBasePlugins,
  MaterialConfiguratorPlugin,
  MaterialConfiguratorBasePlugin,
  getUrlQueryParam,
  IMaterial,
  InteractionPromptPlugin,
  SwitchNodeBasePlugin,
  SwitchNodePlugin,
  ExtrasUiPlugin,
  TransfrSharePlugin,
  GroundPlugin,
  MeshStandardMaterial2,
  PosePlugin,
  AssetManagerPlugin,
  Importer,
  TextureLoader,
  CameraViewPlugin,
  GLTFAnimationPlugin,
  Color,
  FullScreenPlugin,
} from "webgi";

import {IjewelEditorPlugin} from "./IjewelEditorPlugin";
import {EditorSettings, Project} from "../../main";
import {SimpleBackgroundEnvUiPlugin2} from "./SimpleBackgroundEnvUiPlugin2 ";
import { isVersionLess } from "../utils/functions";
import { SVGTextureLoader } from "./SVGTextureLoader";
// import { MaterialConfiguratorPlugin } from "./";
// import {  } from "three/examples/jsm/renderers/CSS3DRenderer";

function checkQuery(key: string, def = true) {
  return !['false', 'no', 'f', '0'].includes(getUrlQueryParam(key, def ? 'yes' : 'no').toLowerCase())
}

export async function setupViewer(
  project: Project,
  editorSettings?: EditorSettings,
) {
  let viewer: ViewerApp;
  const url = new URL(window.location.href)

  const rgbm = checkQuery('rgbm', true)
  const msaa = checkQuery('msaa', false)
  const debug = checkQuery('debug', false)
  const caching = checkQuery('cache', true)
  const depthTonemap = checkQuery('depthTonemap', true)
  const depthPrepass = checkQuery('depthPrepass', true)
  const zPrePass = checkQuery('zPrepass', true)
  const autoScale = checkQuery('autoScale', true)
  const autoCenter = checkQuery('autoCenter', true)
  const autoScaleRadius = parseFloat(getUrlQueryParam('autoScaleRadius', '2'))

  //set default logo before loading screen plugin is initialized
  if(LoadingScreenPlugin.LS_DEFAULT_LOGO !== undefined){
    LoadingScreenPlugin.LS_DEFAULT_LOGO = "https://playground.ijewel3d.com/logo_black.svg";
  }

  const options = {
    canvas: document.getElementById("webgi-mini-editor-canvas") as HTMLCanvasElement,
    // container: document.getElementById("webgi-mini-editor-canvas-container") as HTMLElement,
    useRgbm: rgbm,
    useGBufferDepth: zPrePass || depthPrepass,
    dropzone: false,
    assetManager: true,
    isAntialiased: msaa,
    modelRootScale: parseFloat(getUrlQueryParam('modelRootScale', '1')),
  }
  if (!project.basePath) project.basePath = "";

  if(!editorSettings?.fullEditor) {
    // Initialize the viewer
    viewer = new ViewerApp(options);
    viewer.renderEnabled = false
    viewer.renderer.displayCanvasScaling = Math.min(Math.max(window.devicePixelRatio, 1), 2.5)
    // setQuality(viewer);

    const dropzone = viewer.addPluginSync(DropzonePlugin)
    dropzone!.importerParams.autoScale = autoScale
    dropzone!.importerParams.autoCenter = autoCenter
    dropzone!.importerParams.autoScaleRadius = autoScaleRadius

    await viewer.addPlugin(AssetExporterPlugin);
    await viewer.addPlugin(PosePlugin);

    await addBasePlugins(viewer);

    const loading = await viewer.getOrAddPlugin(LoadingScreenPlugin)
    loading.isEditor = true;
    loading.showFileNames = false;

    const interactionPrompt = viewer.getPlugin(InteractionPromptPlugin)!;
    if(interactionPrompt) interactionPrompt.disable('miniEditor')
    viewer.addPlugin(PickingPlugin)
    let outline = await viewer.getOrAddPlugin(OutlinePlugin);
    viewer.serializePluginsIgnored = [LoadingScreenPlugin.PluginType , InteractionPromptPlugin.PluginType]

    if (outline.config?.highlightMaterialSameNames !== undefined)
    outline.config.highlightMaterialSameNames = true;

    let r3dm = viewer.getPlugin(Rhino3dmLoadPlugin) as Rhino3dmLoadPlugin;
    r3dm.hideLineMesh = true;



  }else {
    const editor = new CoreEditorApp(options);

    await editor.getOrAddPlugin(SwitchNodePlugin2);
    await editor.getOrAddPlugin(IjewelEditorPlugin);
    await editor.getOrAddPlugin(SimpleBackgroundEnvUiPlugin2);

    await addEditorPlugins(editor, {
      caching: caching,
      ground: true,
      bloom: true,
      depthTonemap: depthTonemap,
      importPopup: true, // loading screen
      debug: debug,
      presetLibrary: false,
      materialConfigurator: false,
      switchNode: false,
    });

    if (isVersionLess(ViewerApp.VERSION, '0.9.21')) {
      const loader = editor.getPlugin(LoadingScreenPlugin)!
      loader.showOnSceneEmpty = false
    }

    let r3dm = await editor.getOrAddPlugin(Rhino3dmLoadPlugin)!;
    r3dm.hideLineMesh = false;

    const sharePlugin = await editor.getOrAddPlugin(TransfrSharePlugin)
    sharePlugin.shareViewerLink = ()=>{
        const url = new URL(window.location.href)
        url.pathname = '/v2/viewer.html'
        // console.log(url.toString())
        return sharePlugin.shareLink(url)
    }
    if((sharePlugin as any).uiConfig){
      (sharePlugin as any).uiConfig.label = 'Share Link'
    }

    const extras = await editor.getOrAddPlugin(ExtrasUiPlugin)
    extras.showColorScheme = false;


    await editor.setupUi();
    const webgiLogo = document.getElementById('webgi-logo')!
    webgiLogo.style.backgroundImage = `url(https://playground.ijewel3d.com/logo_black.svg)`
    webgiLogo.onclick = () => {
      window.open('https://ijewel3d.com/', '_blank')
    }

    
    //override fullscreen toggle button
    const wrapper = document.getElementById("canvas-wrapper")
    if(wrapper){
      wrapper?.querySelector("#fsToggle")?.addEventListener("click", (e) => {
        e.stopPropagation();
        e.preventDefault();
        const fsPlugin = viewer.getPlugin(FullScreenPlugin);
        if (fsPlugin) {
          fsPlugin.toggle(wrapper)
        }
      } , { capture: true });
    }

    viewer = editor
  }

  const manager = viewer.getPlugin(AssetManagerPlugin)!;
  //todo: delete this after webgi 0.11.0
  if (isVersionLess(ViewerApp.VERSION, '0.11.0')) {
    manager.importer?.Importers.splice(0, 0, new Importer(SVGTextureLoader as any, ['svg', 'data:image/svg'], false))
  }
   //todo: delete this after webgi 0.11.2
  if (isVersionLess(ViewerApp.VERSION, '0.11.2')) {
    manager.importer?.Importers.splice(0, 0, new Importer(TextureLoader as any, ['avif', 'data:image/avif'], false))
  }

  // const loading = viewer.getPlugin(LoadingScreenPlugin)!;
  const switchNode = await viewer.getOrAddPlugin(SwitchNodePlugin2);
  switchNode.autoSnapIcons = true;
  const diamondPlugin = await viewer.getOrAddPlugin(DiamondPlugin);
  if (editorSettings?.diamondKey){
    diamondPlugin.setKey(editorSettings.diamondKey);
  }
  await viewer.getOrAddPlugin(MaterialConfiguratorPlugin2)
  await viewer.getOrAddPlugin(CanvasSnipperPlugin);

  const tp = viewer.getOrAddPluginSync(TweakpaneUiPlugin)
  if(tp) {
    tp.colorMode = 'white';
    tp.setupPlugins(MaterialConfiguratorPlugin2)
    tp.setupPluginUi(ExtrasUiPlugin)
    tp.setupPluginUi(SwitchNodePlugin2)
    tp.setupPluginUi(TransfrSharePlugin)
  }

  let r3dm = viewer.getPlugin(Rhino3dmLoadPlugin) as Rhino3dmLoadPlugin;
  r3dm.importMaterials = true;
  r3dm.forceLayerMaterials = true;
  if(r3dm.hidePointMesh !== undefined) r3dm.hidePointMesh = true;
  if(r3dm.loadUserDataStrings !== undefined) r3dm.loadUserDataStrings = false;

  const editor = await viewer.getOrAddPlugin(IjewelEditorPlugin);

  await editor.init(
    project.basePath,
    editorSettings,
    project
  );


  viewer.renderer.refreshPipeline();

  const simpleBackgroundEnvUiPlugin2 = await viewer.getOrAddPlugin(SimpleBackgroundEnvUiPlugin2);
  simpleBackgroundEnvUiPlugin2.autoGroundTonemap = editorSettings?.autoGroundTonemap ?? true;
  simpleBackgroundEnvUiPlugin2.onAdded(viewer);

  return viewer;
}

export function disposeViewer(viewer: ViewerApp) {
  if (!viewer) return;
  viewer.scene.disposeSceneModels();
  viewer.scene.dispose();
  viewer.renderer.dispose();
  // viewer.dispose();
}

const switchNodeClass = getUrlQueryParam("theme") === "grid" ? SwitchNodePlugin : SwitchNodeBasePlugin;
class SwitchNodePlugin2 extends switchNodeClass{
  public static PluginType = SwitchNodePlugin.PluginType;
}

const clas = getUrlQueryParam("theme") === "grid" ? MaterialConfiguratorPlugin : MaterialConfiguratorBasePlugin;
export class MaterialConfiguratorPlugin2 extends clas {
  public static PluginType = "MaterialConfiguratorPlugin";
  options: { name: string; icon: any; index: number; uuid: string }[][] = [];
  sceneMaterials: {icon: any; material: IMaterial}[] = [];
  updateSceneMaterials = false;
  useThumbnail = true;
  enableEditContextMenus = true;
  // applyOnLoad = true;

  async _refreshUi() {
    if (!(await super._refreshUi())) return false;
    const getIcon = ( material: IMaterial, preview : keyof MeshStandardMaterial2 | "generate:sphere" ) => {
      let m : any = this.options.find((o) => o.find((o) => o.uuid === material.uuid))?.find((o) => o.uuid === material.uuid);
      if (!m) m = this.sceneMaterials.find((o) => o.material.uuid === material.uuid);
      return m ? m.icon : this.getPreview(material , preview, true);
    };
    const materialExists = (material: IMaterial) => {
      if (!this._viewer) return false;
      if(material.name === "BaseGroundMaterial_0_1") return false
      let found = false;
      this._viewer.scene.traverse((object) => {
        //@ts-ignore
        if (object.isMesh && (object).material?.uuid === material.uuid) {
          found = true;
        }
      });
      return found;
    }

    this.options = this.variations.map((varition) => {
      return varition.materials.map((material, index) => ({
        name: material.name || material.uuid,
        label : material.userData?.label,
        uuid: material.uuid,
        icon: this.useThumbnail ? material.userData?.icon || getIcon(material , varition.preview) : getIcon( material , varition.preview),
        onRemove : () => this.removeMaterial(varition.uuid, material.uuid),
        index,
      }));
    });

    //scene materials
    if(this.updateSceneMaterials){
      this.updateSceneMaterials = false;
      this.sceneMaterials = this._viewer?.getManager()!.materials?.getAllMaterials().filter((material => materialExists(material))).map((material) => {
        return {
          icon: this.useThumbnail ? material.userData?.icon || getIcon(material , "color") : getIcon(material , "color"),
          material: material
        }
      }) || [] as any
    }
    this.dispatchEvent({ type: "refreshUi" } as any);
    return true;
  }

  removeMaterial = (variationKey : string ,materialUuid: string) => {
    const variation = this.variations.find(v => v.uuid === variationKey)
    if(!variation) return
    variation.materials = variation.materials.filter(m => m.uuid !== materialUuid)
    this.refreshUi()
  }
}


export function clearViewer(viewer: ViewerApp) {
  if (!viewer) return;
  viewer.scene.background = null;
  viewer.scene.backgroundColor = new Color("white");
  viewer.getPlugin(CameraViewPlugin)?.fromJSON({});
  const sn = viewer.getPlugin(SwitchNodePlugin)
  if(sn) sn.variations = [];

  const animation = viewer.getPlugin(GLTFAnimationPlugin)
  if(animation) {
    while (animation.animations.length) animation.animations.pop()
  }

  viewer.getPlugin(GroundPlugin)?.fromJSON({});
  const configurator = viewer.getPluginByType(MaterialConfiguratorPlugin.PluginType) as MaterialConfiguratorPlugin;
  if(configurator) {
    configurator.fromJSON?.({})
    configurator.refreshUi?.()
  }
  viewer.getPlugin(SwitchNodePlugin)?.fromJSON({})

  viewer.scene.disposeSceneModels(); 
  viewer.getPluginByType<DiamondPlugin>(DiamondPlugin.PluginType)?.disposeAllCacheMaps();


  //clear modelstage 
  const modelStage : any = viewer.getPlugin("ModelStagePlugin");
  if(modelStage) modelStage.setModel(null);

  // viewer.getPlugin(IjewelEditorPlugin)!.project = {}
  // viewer.scene.setEnvironment(null);

  viewer.setDirty();
}


//todo: maybe implement this in the future
// function setQuality(viewer: IViewerApp, quality : "720p" | "1080p" | "2k" | "4k" = "1080p") {
//   let targetResolution;

//   switch (quality) {
//     case "720p":
//       targetResolution = { width: 1280, height: 720 };
//       break;
//     case "1080p":
//       targetResolution = { width: 1920, height: 1080 };
//       break;
//     case "2k":
//       targetResolution = { width: 2560, height: 1440 };
//       break;
//     case "4k":
//       targetResolution = { width: 3840, height: 2160 };
//       break;
//     default:
//       console.error("Unknown quality level");
//       return;
//   }



//   viewer.renderer.refreshPipeline();
//   const canvas = viewer.canvas;
//   const displayWidth = canvas.clientWidth;
//   const displayHeight = canvas.clientHeight;

//   // Calculate the scaling factor for the chosen resolution
//   const scaleX = targetResolution.width / displayWidth;
//   const scaleY = targetResolution.height / displayHeight;
//   const scaleFactor = Math.min(scaleX, scaleY);
//   viewer.renderer.displayCanvasScaling = scaleFactor;
// }
