import "./index.css";

import { LexicalComposer } from "@lexical/react/LexicalComposer";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
// import ClickableLinkPlugin from "@lexical/react/LexicalClickableLinkPlugin";
import TextEditorToolbar from "./TextEditorToolbar";
import FirstLinePlugin, { MyCustomAutoLinkPlugin } from "./CustomPlugin";
import { ListItemNode, ListNode } from "@lexical/list";
import { ListPlugin } from "@lexical/react/LexicalListPlugin";
import OnChangePlugin from "./OnChangePlugin";
import { AutoLinkNode, LinkNode } from "@lexical/link";
import { LinkPlugin } from "@lexical/react/LexicalLinkPlugin";
import { TreeViewPlugin } from "./CustomPlugin";
import CommandExecutor from "./Commands";
import {
  type EditorState,
  type LexicalEditor,
  type SerializedEditorState,
} from "lexical";

import { useMemo } from "react";
import { getRandomPlaceholder } from "../src/components/utils/functions";

export interface TextEditorProjectData {
  name: string;
  description: string;
}

export interface TextEditorProps {
  readOnly?: boolean;
  initialState: SerializedEditorState | TextEditorProjectData;
  onChange?: (
    editorNewState: EditorState,
    projectData: TextEditorProjectData,
    editor: LexicalEditor
  ) => void;
}

export function TextEditor(props: TextEditorProps) {
  const { onChange, initialState, readOnly } = props;

  const newTextEditorConfig = useMemo(
    () => ({
      ...textEditorConfig,
      editable: !readOnly,
    }),
    [readOnly]
  );

  const placeholder = useMemo(() => getRandomPlaceholder(), []);

  return (
    <>
      <LexicalComposer initialConfig={newTextEditorConfig}>
        <div id="text-editor-portal" />
        <div className="text-editor-container !rounded-small relative">
          {!readOnly ? <TextEditorToolbar /> : <div className="pt-3" />}
          <div className="text-editor-inner ">
            <OnChangePlugin
              onChange={onChange}
              initialState={Object.freeze(initialState)}
            />
            <ListPlugin />
            {/* <ClickableLinkPlugin /> */}
            <FirstLinePlugin />
            <RichTextPlugin
              contentEditable={
                <ContentEditable
                  className="text-editor-input"
                  aria-placeholder={"Write a description"}
                  placeholder={
                    <div className="text-editor-placeholder text-default-400">
                      <p className="text-editor-first-line mb-unit-md">
                        {placeholder.title}
                      </p>
                      {placeholder.description}
                    </div>
                  }
                />
              }
              ErrorBoundary={LexicalErrorBoundary}
            />
            <LinkPlugin />
            <MyCustomAutoLinkPlugin />
            <HistoryPlugin />
            <AutoFocusPlugin />
            {/* <TreeViewPlugin /> */}
          </div>
        </div>
        {/* Register custom command for adding child nodes */}
        <CommandExecutor />
      </LexicalComposer>
    </>
  );
}

const TextEditorTheme = {
  code: "text-editor-code",
  heading: {
    h1: "text-editor-heading-h1",
    h2: "text-editor-heading-h2",
    h3: "text-editor-heading-h3",
    h4: "text-editor-heading-h4",
    h5: "text-editor-heading-h5",
  },
  image: "text-editor-image",
  link: "text-editor-link",
  list: {
    listitem: "text-editor-listitem",
    nested: {
      listitem: "text-editor-nested-listitem",
    },
    ol: "text-editor-list-ol",
    ul: "text-editor-list-ul",
  },
  ltr: "ltr",
  paragraph: "text-editor-paragraph",
  placeholder: "text-editor-placeholder text-editor-first-line",
  quote: "text-editor-quote",
  rtl: "rtl",
  text: {
    bold: "text-editor-text-bold",
    code: "text-editor-text-code",
    hashtag: "text-editor-text-hashtag",
    italic: "text-editor-text-italic",
    overflowed: "text-editor-text-overflowed",
    strikethrough: "text-editor-text-strikethrough",
    underline: "text-editor-text-underline",
    underlineStrikethrough: "text-editor-text-underlineStrikethrough",
  },
};

export const textEditorConfig = {
  namespace: "Text Editor",
  nodes: [ListNode, ListItemNode, AutoLinkNode, LinkNode],
  // Handling of errors during update
  onError(error: Error) {
    throw error;
  },
  // The editor theme
  theme: TextEditorTheme,
};
