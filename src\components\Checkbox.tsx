import { Checkbox, CheckboxProps } from "@nextui-org/react";
import { FC, CSSProperties } from "react";

interface CustomCheckboxProps extends CheckboxProps {
  label: any;
  className?: string;
  defaultSelected?: boolean;
  onValueChange?: any;
  isSelected?: boolean;
}

const MyCheckbox: FC<CustomCheckboxProps> = (props) => {
  return (
    <Checkbox
      onValueChange={props.onValueChange}
      defaultSelected={props.defaultSelected}
      isSelected={props.isSelected}
      size="sm"
      className={props.className}
      classNames={{ label: ["text-tiny"] }}
      {...props}
    >
      {props.label}
    </Checkbox>
  );
};

export { MyCheckbox as Checkbox };
/* Rectangle 13 */
