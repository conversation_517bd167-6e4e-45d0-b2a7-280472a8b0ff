import { FC, useCallback, useEffect, useMemo, useState } from "react";
import type ED_SETTING from "../types";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Tooltip,
  Modal,
  Ava<PERSON>,
  Spinner,
} from "@nextui-org/react";
import Code from "./icons/Code";
import { Button } from "./Button";
import ContentEdit from "./icons/ContentEdit";
import Controller from "./icons/Controller";
import AppearanceEdit from "./icons/AppearanceEdit";
import SizeIcon from "./icons/SizeIcon";
import Arrow from "./icons/Arrow";
import { Input } from "./Input";
import Download from "./icons/Download";
import { Img } from "./Img";
import Copy from "./icons/Copy";
import { UpgradeToBusinessButton } from "./UpgradeButton";
import { EditorSettings, type Project } from "../main";
import Close from "./icons/Close";
import noimageIcon from "../assets/no_image.jpg?inline";
import Snap from "./icons/Snap";
import { useProject } from "./provider/ProjectProvider";
import { useUi } from "./provider/UiProvider";
import { getEmbedDefaultSettings } from "./utils/limits";
import { Help, SwitchController } from "./utils/shared";

import { CanvasSnipperPlugin, RendererUiPlugin, ViewerApp, timeout } from "webgi";
import toast from "@minieditor/react-hot-toast";

interface EmbedSettingsProps {
  viewer?: ViewerApp;
  editorSettings?: EditorSettings;
  project: Project;
  setShowSubscribeModal: any;
  selectedPresetTab?: string;
  changeMenuTab: (key: 'branding' | any) => void
}

type SubTabsTypes = "appearance" | "content" | "controls";
interface SubTabsProps {
  selectedTab: string;
  handleChangeTab: (tab: SubTabsTypes) => void;
}

const EmbedSettings: FC<EmbedSettingsProps> = (props) => {
  const { viewer, project, setShowSubscribeModal, editorSettings, changeMenuTab } = props;

  const isPremium = editorSettings?.isPremium,
    isBusiness = editorSettings?.isBusiness,
    embedBaseurl = editorSettings?.embed?.baseurl,
    previewBaseurl = editorSettings?.embed?.previewBaseurl;

  const { embed } = useUi();
  const { setProject } = useProject();

  const [selectedTab, setSelectedTab] = useState<SubTabsTypes>("appearance");

  useEffect(() => {
    if (editorSettings?.embed?.enabled && !embed.logoList && editorSettings.isLogin) {
      editorSettings?.embed?.getlogoList?.().then((data) => {
        embed.setLogoList(data);

      });
    }
  }, [editorSettings?.embed?.enabled, embed.logoList, editorSettings?.isLogin]);
  
  const handleUpdateSettings = (newSettings: {}) => {
    embed.setSettings((p) => {
      const sett = { ...p, ...newSettings }
      setProject((prev) => ({ ...prev, embedSettings: sett }));
      return sett;
    });
  };
  const handleChangeTab = (tab: SubTabsTypes) => {
    setSelectedTab(tab);
  };

  const commonProps = {
    settings: embed.settings,
    handleUpdateSettings: handleUpdateSettings,
    isPremium,
    isBusiness,
    setShowSubscribeModal,
  };

  return (
    <>
      {!editorSettings?.embed?.enabled && editorSettings?.embed?.warnningMessage && (
        <div className="bg-[#fdf8e3] text-default-foreground border-[#f8eab1] border-[1px] p-2 rounded-small mb-2 text-xs w-full">
          {editorSettings?.embed?.warnningMessage}
        </div>
      )}
      <div className={`flex flex-col h-fit w-full select-none px-1 ${editorSettings?.embed?.enabled ? "" : "opacity-40 pointer-events-none"}`}>
        <EmbededCodeSection settings={embed.settings} project={project} embedBaseurl={embedBaseurl} embedPreviewBaseurl={previewBaseurl} />

        <div className="mt-5">
          <SubTabs {...{ selectedTab, handleChangeTab }} />
        </div>

        {selectedTab === "appearance" && (
          <AppearanceView
            viewer={viewer}
            currentLogoUrl={project.logo}
            canRemoveHologram={editorSettings?.embed?.canRemoveHologram}
            changeMenuTab={changeMenuTab}
            handleUploadEmbedPoster={editorSettings?.embed?.handleUploadEmbedPoster}
            {...commonProps}
          />
        )}

        {selectedTab === "content" && <ContentView {...commonProps} />}

        {selectedTab === "controls" && <ControlsView {...commonProps} />}
      </div>
    </>
  );
};

export { EmbedSettings };

const SubTabs = (props: SubTabsProps) => {
  const { selectedTab, handleChangeTab } = props;
  return (
    <Tabs
      selectedKey={selectedTab}
      onSelectionChange={(key) => handleChangeTab(key as any)}
      color="primary"
      variant="underlined"
      fullWidth
      className="h-full"
      classNames={{
        tabList: "flex gap-unit-xl p-0 h-full gap-1",
        tabContent: "group-data-[selected=true]:text-primary text-default-foreground mb-1",
        cursor: "h-[3px] rounded-full w-full",
      }}
    >
      <Tab
        key="appearance"
        title={
          <Tooltip delay={300} placement="top" content="Appearance Settings">
            <div onClick={() => handleChangeTab("appearance")}>
              <AppearanceEdit />
            </div>
          </Tooltip>
        }
      />
      <Tab
        key="content"
        title={
          <Tooltip delay={300} placement="top" content="Content Settings">
            <div onClick={() => handleChangeTab("content")}>
              <ContentEdit />
            </div>
          </Tooltip>
        }
      />
      <Tab
        key="controls"
        title={
          <Tooltip delay={300} placement="top" content="Display Controls">
            <div onClick={() => handleChangeTab("controls")}>
              <Controller />
            </div>
          </Tooltip>
        }
      />
    </Tabs>
  );
};

interface CommonViewProps {
  settings: ED_SETTING.EMBED_SETTING;
  handleUpdateSettings: (newSettings: {}) => void;
  isPremium?: boolean;
  isBusiness?: boolean;
  setShowSubscribeModal: any;
}

const AppearanceView = (
  props: CommonViewProps & {
    viewer?: ViewerApp;
    isDisabled?: boolean;
    currentLogoUrl?: string | null;
    canRemoveHologram?: boolean;
    changeMenuTab: (key: "branding" | any) => void;
    handleUploadEmbedPoster?: ED_SETTING.EMBED["handleUploadEmbedPoster"];
  }
) => {
  const {
    viewer,
    currentLogoUrl,
    handleUpdateSettings,
    settings,
    isPremium,
    isBusiness,
    canRemoveHologram,
    setShowSubscribeModal,
    changeMenuTab,
    handleUploadEmbedPoster,
  } = props;

  const {
    embed: { embedPosterUrl, setEmbedPosterUrl },
  } = useUi();
  const { setProject } = useProject();

  const [loadingPoster, setLoadingPoster] = useState(false);

  const handleSwitchChange = (field: string, value: boolean) => {
    handleUpdateSettings({ [field]: value });
  };

  const handleEmbedPoster = useCallback(async () => {
    if (!viewer) {
      toast.error("Viewer not found");
      return;
    }
    setLoadingPoster(true);
    const id = toast.loading("Uploading poster...", { id: "uploading-poster" });
    const snapper = await viewer.getOrAddPlugin(CanvasSnipperPlugin)!;

    const p = viewer.getPlugin(RendererUiPlugin);
    if (p) p.enabled = false;
    await viewer.doOnce("postFrame");
    const width = settings.isResponsive ? Number(settings.width) : Number(settings.posterWidth);
    const height = settings.isResponsive ? Number(settings.height) : Number(settings.posterHeight);
    viewer.setSize({ width, height });

    viewer.setDirty();

    await timeout(500);
    await viewer.doOnce("postFrame");
    const file = await snapper.getFile(`snapshot.png`, { mimeType: "image/png", waitForProgressive: true });

    viewer.setSize();
    viewer.setDirty();

    if (p) p.enabled = true;

    if (!file) {
      toast.dismiss(id);
      toast.error("Poster processing of file failed.");
      setLoadingPoster(false);
      return null;
    } else {
      await handleUploadEmbedPoster?.({ file })
        .then((data) => {
          if (!data) throw new Error("Apply poster to project failed");
          setEmbedPosterUrl(data.path);
          setProject((prev) => ({ ...prev, embedPosterUrl: data.path }));

          toast.dismiss(id);
          toast.success("Poster uploaded successfully", {
            id: "poster-upload-success",
          });
        })
        .catch((err) => {
          toast.dismiss(id);
          toast.error(err.message, { id: "poster-upload-error" });
        })
        .finally(() => {
          setLoadingPoster(false);
        });
    }
  }, [viewer, settings]);

  return (
    <section className="mt-3">
      <div id="size-control">
        <div className="flex items-center gap-unit-md">
          <SizeIcon />
          <span className="text-xs font-semibold">Size</span>
          <Help content="You can resize here" />
        </div>

        <SwitchController field="isResponsive" value={settings.isResponsive} onChange={handleSwitchChange} title="Responsive" />

        <div className="flex gap-unit-md">
          <Input
            onChange={(value) => handleUpdateSettings({ width: value })}
            value={settings.width}
            isDisabled={!settings.isResponsive}
            placeholder="1280"
            className="w-full"
            type="number"
            startContent={
              <div className="w-unit-xs -translate-x-0.5">
                <Arrow className="scale-[1.2]" />
              </div>
            }
          />
          <Input
            onChange={(value) => handleUpdateSettings({ height: value })}
            value={settings.height}
            isDisabled={!settings.isResponsive}
            placeholder="720"
            className="w-full"
            type="number"
            startContent={
              <div className="w-unit-xs -translate-x-0.5">
                <Arrow className="scale-[1.2] rotate-90" />
              </div>
            }
          />
        </div>
      </div>

      <div id="title-control" className="mt-2">
        <SwitchController field="isTitle" value={settings.isTitle} onChange={handleSwitchChange} title="Title" />

        <div id="embed-poster" className="py-3 flex gap-unit-md flex-col">
          <span className={`text-xs font-normal`}>High Resolution Poster</span>
          <div className="mt-1 flex flex-col gap-unit-lg">
            <div className="relative">
              {loadingPoster && <Spinner className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2" />}
              <Img
                src={embedPosterUrl || noimageIcon}
                className={`rounded-small min-h-unit-4xl max-h-unit-6xl w-full object-contain ${loadingPoster ? "opacity-50" : ""}`}
              />
            </div>

            {!settings.isResponsive && (
              <div className="flex gap-unit-md">
                <Input
                  onChange={(value) => handleUpdateSettings({ posterWidth: value })}
                  value={settings.posterWidth ?? "1280"}
                  isDisabled={loadingPoster}
                  placeholder="1280"
                  className="w-full"
                  type="number"
                  startContent={
                    <div className="w-unit-xs -translate-x-0.5">
                      <Arrow className="scale-[1.2]" />
                    </div>
                  }
                />
                <Input
                  onChange={(value) => handleUpdateSettings({ posterHeight: value })}
                  value={settings.posterHeight ?? "720"}
                  isDisabled={loadingPoster}
                  placeholder="720"
                  className="w-full"
                  type="number"
                  startContent={
                    <div className="w-unit-xs -translate-x-0.5">
                      <Arrow className="scale-[1.2] rotate-90" />
                    </div>
                  }
                />
              </div>
            )}
            <Button
              name={embedPosterUrl ? "Update Poster" : "Add Poster"}
              endContent={<Snap />}
              isDisabled={loadingPoster}
              size="lg"
              onPress={handleEmbedPoster}
              color="primary"
              className="!text-tiny !font-mainSemiBold"
              fullWidth
            />
          </div>
        </div>

        <Divider className="bg-[#E7E9E9] my-1 w-[95%] mx-auto" />

        <div className="mt-2 mb-1 flex justify-center">
          {!isBusiness ? (
            <UpgradeToBusinessButton onClick={() => setShowSubscribeModal?.(true, "business")} />
          ) : (
            <span className="min-w-[120px] text-center px-2 py-1 font-bold text-[10px] leading-none rounded-full bg-[linear-gradient(229.19deg,_#FCC253_-33.33%,_#FFE0A4_18.24%,_#B5821F_69.81%,_#FFD788_121.38%)] text-[#FFFFFF]">
              Business
            </span>
          )}
        </div>

        <SwitchController
          field="isRemoveLogo"
          value={settings.isRemoveLogo}
          onChange={handleSwitchChange}
          title="Remove Logo"
          isDisabled={!isBusiness}
        />

        <SwitchController
          field="isRemoveLogoLink"
          value={settings.isRemoveLogoLink}
          onChange={handleSwitchChange}
          title="Remove Backlink"
          isDisabled={!(isPremium || isBusiness)}
        />

        {canRemoveHologram && (
          <SwitchController
            field="isRemoveHologram"
            value={settings.isRemoveHologram}
            onChange={handleSwitchChange}
            title="Remove Hologram"
            isDisabled={!isBusiness}
            helpMessage="If enabled, diamond hologram on the left bottom will be removed"
          />
        )}

        <SwitchController
          field="isRemoveLoadingBgImg"
          value={settings.isRemoveLoadingBgImg}
          onChange={handleSwitchChange}
          title="Remove Loading screen background Image"
          isDisabled={!isBusiness}
        />

        <Divider className="bg-[#E7E9E9] my-1 w-[95%] mx-auto" />
        <div id="screen-loading-logo" className="py-2">
          <span className={`text-xs font-normal ${!isBusiness ? "text-[#87878a]" : ""}`}>Loading screen icon</span>

          <div className="mt-3 flex flex-wrap gap-unit-md items-center">
            <Avatar
              key="current-logo"
              className="min-w-11 min-h-11 overflow-hidden"
              isBordered
              radius="full"
              fallback
              src={currentLogoUrl ? currentLogoUrl : noimageIcon}
            />
            <Button
              disabled={!isBusiness}
              onClick={() => changeMenuTab("branding")}
              name={currentLogoUrl ? "Change Logo" : "Upload Logo"}
              size="md"
              color="primary"
              className="!text-tiny rounded-full !font-mainSemiBold max-w-[110px]"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

const ContentView = (props: CommonViewProps) => {
  const { handleUpdateSettings, settings, isBusiness, isPremium, setShowSubscribeModal } = props;

  const handleSwitchChange = (field: string, value: boolean) => {
    handleUpdateSettings({ [field]: value });
  };

  return (
    <div className="mt-3">
      <p className="text-xs font-semibold leading-4">Choose how to present the model</p>
      <SwitchController field="isShowPrompt" value={settings.isShowPrompt} onChange={handleSwitchChange} title="Show interaction prompt" />

      <Divider className="bg-[#E7E9E9] my-1 w-[95%] mx-auto" />

      <div className="mt-2 mb-1 flex justify-center">
        {!isBusiness ? (
          <UpgradeToBusinessButton onClick={() => setShowSubscribeModal?.(true, "business")} />
        ) : (
          <span className="min-w-[120px] text-center px-2 py-1 font-bold text-[10px] leading-none rounded-full bg-[linear-gradient(229.19deg,_#FCC253_-33.33%,_#FFE0A4_18.24%,_#B5821F_69.81%,_#FFD788_121.38%)] text-[#FFFFFF]">
            Business
          </span>
        )}
      </div>

      <SwitchController
        field="isTurntableAnimation"
        value={settings.isTurntableAnimation}
        onChange={handleSwitchChange}
        title="Turntable animation"
        isDisabled={!isBusiness}
      />
      <SwitchController field="isAutoplay" value={settings.isAutoplay} onChange={handleSwitchChange} title="Autoplay" />
      <SwitchController
        field="isTransparentBackground"
        value={settings.isTransparentBackground}
        onChange={handleSwitchChange}
        title="Transparent background"
        isDisabled={!(isBusiness || isPremium)}
      />
    </div>
  );
};

const ControlsView = (props: CommonViewProps) => {
  const { handleUpdateSettings, settings, isBusiness, setShowSubscribeModal } = props;

  const handleSwitchChange = (field: string, value: boolean) => {
    handleUpdateSettings({ [field]: value });
  };

  return (
    <div className="mt-3">
      <div className="flex items-center gap-unit-md">
        <span>
          <Controller />
        </span>
        <span className="text-xs font-semibold">Displayed buttons</span>
        <Help content="Customize the displayed buttons" />
      </div>
      <div id="title-control" className="mt-2">
        <SwitchController field="isConfigurator" value={settings.isConfigurator} onChange={handleSwitchChange} title="Configurator" />
        <SwitchController field="isEnabledZoom" value={settings.isEnabledZoom} onChange={handleSwitchChange} title="Zoom In/Out" />
        <SwitchController field="isShare" value={settings.isShare} onChange={handleSwitchChange} title="Share" />
        <SwitchController field="isShowModelInfo" onChange={handleSwitchChange} value={settings.isShowModelInfo} title="Show Model Info" />

        <Divider className="bg-[#E7E9E9] my-1 w-[95%] mx-auto" />

        <div className="mt-2 mb-1 flex justify-center">
          {!isBusiness ? (
            <UpgradeToBusinessButton onClick={() => setShowSubscribeModal?.(true, "business")} />
          ) : (
            <span className="min-w-[120px] text-center px-2 py-1 font-bold text-[10px] leading-none rounded-full bg-[linear-gradient(229.19deg,_#FCC253_-33.33%,_#FFE0A4_18.24%,_#B5821F_69.81%,_#FFD788_121.38%)] text-[#FFFFFF]">
              Business
            </span>
          )}
        </div>

        <SwitchController field="isQuality" onChange={handleSwitchChange} value={settings.isQuality} title="Quality" isDisabled={!isBusiness} />
        <SwitchController
          field="isResetView"
          onChange={handleSwitchChange}
          value={settings.isResetView}
          title="Reset View"
          isDisabled={!isBusiness}
        />
        <SwitchController
          field="isRotateCamera"
          onChange={handleSwitchChange}
          value={settings.isRotateCamera}
          title="Rotate Camera"
          isDisabled={!isBusiness}
        />
        <SwitchController
          field="isPlayCameraViews"
          value={settings.isPlayCameraViews}
          onChange={handleSwitchChange}
          title="Play Camera Views"
          isDisabled={!isBusiness}
        />
        <SwitchController
          field="isPlayAnimations"
          value={settings.isPlayAnimations}
          onChange={handleSwitchChange}
          title="Play Animations"
          isDisabled={!isBusiness}
        />
        <SwitchController
          field="isFitObject"
          value={settings.isFitObject}
          onChange={handleSwitchChange}
          title="Fit Object/Scene"
          isDisabled={!isBusiness}
        />
        <SwitchController
          field="isFullScreen"
          value={settings.isFullScreen}
          onChange={handleSwitchChange}
          title="Full Screen"
          isDisabled={!isBusiness}
        />
      </div>
    </div>
  );
};

const CodeViewModal = (props: { isOpen: boolean; onOpenChange: (check: boolean) => void; htmlCode: string }) => {
  const { isOpen, onOpenChange, htmlCode } = props;

  const [isCopied, setIsCopied] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  const codeCopyCallback = async () => {
    setTimeout(() => {
      setIsCopied(false);
    }, 2000);
  };

  const downloadCodeCallback = async () => {
    setTimeout(() => {
      setIsDownloading(false);
    }, 2000);
  };

  return (
    <Modal
      key="code-view-modal"
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      backdrop="blur"
      radius="md"
      size="4xl"
      className="p-5"
      classNames={{
        closeButton: "hidden",
      }}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex gap-4 items-center p-0 mb-4">
              <Code width="32px" height="32px" /> <span className="text-xl leading-none font-bold">HTML code</span>
              <div className="flex-1" />
              <span className="p-2 cursor-pointer" onClick={onClose}>
                <Close width={14} height={14} fill="#898989" stroke="#898989" />
              </span>
            </ModalHeader>
            <ModalBody className="border-1 border-[#CFCFCF] rounded-md p-0">
              <div id="editor" className="overflow-auto">
                <pre>
                  <code>{htmlCode}</code>
                </pre>
              </div>
            </ModalBody>
            <ModalFooter className="px-0 pb-0">
              <div className="flex-1 flex justify-end">
                {/* <Checkbox
                  key={"use-bbcode"}
                  label="Use BBcode"
                  // isSelected={false}
                  onValueChange={(value: boolean) => ""}
                  classNames={{ label: ["text-sm"] }}
                /> */}
                <div className="flex gap-unit-xl">
                  <Button
                    color="primary"
                    varient="ghost"
                    isDisabled={isDownloading}
                    onPress={() => {
                      setIsDownloading(true);
                      handleDownload(htmlCode, "index.html", downloadCodeCallback);
                    }}
                    startContent={<Download />}
                    className="min-w-[136px] h-[30px] w-fit px-3.5 rounded-full gap-unit-lg text-sm font-semibold"
                  >
                    <span className="text-sm font-semibold">Download Sample</span>
                  </Button>
                  <Button
                    color="primary"
                    isDisabled={isCopied}
                    onPress={() => {
                      setIsCopied(true);
                      handleCopy(htmlCode, codeCopyCallback);
                    }}
                    startContent={<Copy />}
                    className="min-w-[136px] h-[30px] w-fit px-3.5 rounded-full gap-unit-lg"
                  >
                    <span className="text-sm font-semibold">{isCopied ? "Copied!" : "Copy HTML"}</span>
                  </Button>
                </div>
              </div>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};

const EmbededCodeSection = (props: {
  settings: { [x in string]: any };
  project: Project;
  embedBaseurl?: string;
  embedPreviewBaseurl?: string;
}) => {
  const { settings, project, embedBaseurl, embedPreviewBaseurl } = props;
  const [openCodeModal, setOpenCodeModal] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [hljs, setHljs] = useState(null);

  const { htmlCode, previewUrl } = useMemo(
    () =>
      generateEmbeddingCode({
        settings,
        project,
        embedPreviewBaseurl,
        baseUrl: embedBaseurl ?? window.location.origin,
      }),
    [settings, project]
  );

  const codeCopyCallback = async () => {
    setIsCopied(true);
    setTimeout(() => {
      setIsCopied(false);
    }, 2000);
  };

  useEffect(() => {
    const script = document.createElement("script");
    const link = document.createElement("link");

    script.src = "https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js";
    script.async = true;

    link.href = "https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/default.min.css";
    link.rel = "stylesheet";

    document.body.appendChild(script);
    document.body.appendChild(link);
    script.onload = () => {
      setHljs((window as any).hljs);
    };

    return () => {
      document.body.removeChild(script);
      document.body.removeChild(link);
    };
  }, []);

  useEffect(() => {
    if (hljs && openCodeModal) {
      const nodes = document.querySelectorAll("#editor > pre > code");
      nodes.forEach((node) => (hljs as any).highlightBlock(node));
    }
  }, [hljs, openCodeModal]);

  return (
    <div className="flex flex-col items-center h-fit gap-unit-lg w-full">
      <Button
        isDisabled={isCopied}
        name={isCopied ? "Copied!" : "Copy HTML code"}
        size="lg"
        onPress={() => handleCopy(htmlCode, codeCopyCallback)}
        color="primary"
        className="!text-tiny !font-mainSemiBold"
        fullWidth
      />
      <Button
        name="View code"
        onClick={() => {
          setOpenCodeModal(true);
        }}
        size="lg"
        varient="ghost"
        className="!text-tiny !font-mainSemiBold border-[#2E2E2E]"
        fullWidth
      />
      <Button
        name="Preview"
        as="a"
        target="_blank"
        href={previewUrl}
        size="lg"
        className="!text-tiny !font-mainSemiBold border-[#2E2E2E]"
        fullWidth
      />

      <CodeViewModal isOpen={openCodeModal} onOpenChange={setOpenCodeModal} htmlCode={htmlCode} />
    </div>
  );
};

async function handleCopy(code: string, cb: () => void) {
  try {
    await navigator.clipboard.writeText(code);
    cb();
  } catch (err) {
    console.error("Failed to copy text: ", err);
  }
}

const handleDownload = (text: string, filename: string, cb: () => void) => {
  // Create a Blob with the text content
  const blob = new Blob([text], { type: "text/plain" });
  // Create a URL for the Blob
  const url = URL.createObjectURL(blob);
  // Create a temporary anchor element
  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  // Programmatically click the anchor to trigger the download
  link.click();
  // Clean up the URL object
  URL.revokeObjectURL(url);
  cb();
};

const generateEmbeddingCode = ({
  baseUrl,
  settings,
  project,
  embedPreviewBaseurl,
}: {
  baseUrl: string;
  settings: { [key: string]: any };
  project: Project;
  embedPreviewBaseurl?: string;
}) => {
  const url = new URL(baseUrl);
  const previewUrl = new URL(embedPreviewBaseurl ?? baseUrl);
  const defaultSettings = getEmbedDefaultSettings()

  let newSetting = { ...settings };

  url.searchParams.append("slug", project.slug ?? "");
  previewUrl.searchParams.append("slug", project.slug ?? "");

  let frameDynamicPropsStr = "";

  if (settings.isResponsive) {
    frameDynamicPropsStr += `width="${newSetting.width}px"`;
    frameDynamicPropsStr += ` height="${newSetting.height}px"`;
    previewUrl.searchParams.append("width", `${newSetting.width}px`);
    previewUrl.searchParams.append("height", `${newSetting.height}px`);
  } else {
    frameDynamicPropsStr += ` width="100%"`;
    frameDynamicPropsStr += ` height="100%"`;
  }

  delete newSetting.width;
  delete newSetting.height;
  delete newSetting.isResponsive;

  // Iterate over the object and append each key-value pair as a query parameter.
  Object.entries(newSetting).forEach(([key, value]: [string, any]) => {
    if (value !== null && value !== undefined && defaultSettings[key as keyof typeof defaultSettings] !== value) {
      // Exclude null or undefined values.
      url.searchParams.append(key, value);
      previewUrl.searchParams.append(key, value);
    }
  });

  const htmlCode = `<iframe title="${project.name}"
  frameborder="0" 
  allowfullscreen 
  mozallowfullscreen="true" 
  webkitallowfullscreen="true" 
  xr-spatial-tracking 
  execution-while-out-of-viewport 
  execution-while-not-rendered web-share
  allow="autoplay; fullscreen; xr-spatial-tracking; web-share" 
  ${frameDynamicPropsStr.trim()}
  src="${url.toString()}"/>`;

  return {
    htmlCode,
    iframeSrc: url.toString(),
    previewUrl: previewUrl.toString(),
  };
};

