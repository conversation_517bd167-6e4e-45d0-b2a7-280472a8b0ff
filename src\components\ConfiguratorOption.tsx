import { FC, CSSProperties, useCallback } from "react";
import { Img } from "./Img";
import LockIcon from "./icons/Lock";
import toast from "@minieditor/react-hot-toast";

interface IconProps {
  src: string;
  onClick: () => void;
  className?: string;
  active?: boolean;
  isLocked: boolean;
  setShowSubscribeModal: any;
  isLogin?: boolean;
  isLoading?: boolean;
}

const ConfiguratorOption: FC<IconProps> = (props) => {

  const handleClicked = useCallback(() => {
    if(props.isLocked && !props.isLogin){
      toast.error("Please login to unlock this asset");
      return;
    }
    if(props.isLocked){
      props.setShowSubscribeModal(true);
      return;
    }
    props.onClick();
  }, [props.onClick, props.isLocked]);



  return (
    <div
      onClick={() => handleClicked()}
      className={
        "flex justify-center items-center  flex-col cursor-pointer h-fit relative  " +
        props.className
      }
    >
      {props.isLocked && (
        <div
          className="absolute top-0 z-50 left-0 w-full h-full bg-black/40 flex justify-center items-center border-2 rounded-full"
        >
          <LockIcon className="text-white" />
        </div>
      )}
      {props.isLoading && (
        <div
          className="absolute top-0 z-50 left-0 w-full h-full bg-black/40 flex justify-center items-center border-2 rounded-full"
        >
          <LockIcon className="text-white" />
        </div>
      )}
      <Img
        src={props.src}
        className={`rounded-full h-unit-3xl w-unit-3xl object-cover border-2 
        ${
          props.active
            ? " border-primary hover:border-red-700 "
            : "border-transparent hover:border-default-400"
        }}`}
      ></Img>
    </div>
  );
};

export { ConfiguratorOption };
