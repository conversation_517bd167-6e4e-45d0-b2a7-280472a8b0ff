import {
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
  Input,
  Link,
  Textarea,
} from "@nextui-org/react";
import { FC, useCallback, useState } from "react";
import { Icon } from "./Icon";
import { Button } from "@nextui-org/react";
import toast from "@minieditor/react-hot-toast";
import { useProject } from "./provider/ProjectProvider";
import { ViewerApp } from "webgi";
import { IjewelEditorPlugin } from "./viewer/IjewelEditorPlugin";
import ArrowDownIcon from "./icons/ArrowDown";
import { EditorSettings } from "../main";
import TutorialsIcon from "./icons/Tutorial";

interface HeaderProps {
  hasEdited: boolean;
  setHasEdited: (value: boolean) => void;
  editorSettings?: EditorSettings;
  viewer?: ViewerApp;
}

const Header: FC<HeaderProps> = (props) => {
  const { setProject, project } = useProject();
  const [saving, setSaving] = useState(false);

  const resetDefaultSettings = useCallback(async () => {
    if (!props.viewer) return;
    const ijewelEditorPlugin = props.viewer.getPlugin(IjewelEditorPlugin)!;
    toast.loading("Reseting...", { id: "saving" });
    props.setHasEdited(true);
    const defaultConfig = await fetch(project.basePath + "default.json").then(
      (res) => res.json()
    );
    if (defaultConfig) {
      defaultConfig.name = "";
      defaultConfig.description = "";
      setProject(defaultConfig);
      ijewelEditorPlugin.fromJSON(defaultConfig);
      await ijewelEditorPlugin.applyConfig();
      toast.success("Project Reset", { id: "saving" });
      return;
    }

    setProject((prev) => ({
      ...prev,
      name: project?.name ?? "",
      description: project?.description ?? "",
      logo: project?.logo ?? "",
    }));
    toast.success("Settings Reset", { id: "saving" });
  }, [project]);

  const handleSaveDropdownClick = useCallback(
    async (key: string) => {
      if (!props.viewer) return;
      const ijewelEditorPlugin = props.viewer.getPlugin(IjewelEditorPlugin)!;

      !props.editorSettings?.saveCallback &&
        toast.loading(key === "reset" ? "Reseting..." : "Saving...", {
          id: "saving",
        });
      setSaving(true);
      
      switch (key) {
        case "save":
          if (props.editorSettings?.saveCallback) {
            await props.editorSettings.saveCallback(ijewelEditorPlugin.getConfig(true)!)
          } else {
            await ijewelEditorPlugin.export(true, false);
          }
          break;
        case "saveAndPreview":
          if (props.editorSettings?.saveCallback) {
            await props.editorSettings.saveCallback(ijewelEditorPlugin.getConfig(true)!);
          } else {
            await ijewelEditorPlugin.export(true, false);
          }
          // open preview page in new tab
          setTimeout(() => {
            window.open(props.editorSettings?.previewLink, "_blank");
          }, 1000);
          break;
        case "reset":
          await resetDefaultSettings();
          break;
        default:
          break;
      }

      !props.editorSettings?.saveCallback &&
        toast.success(key === "reset" ? "settings reset" : "Saved", {
          id: "saving",
        });

      setSaving(false);
      props.setHasEdited(false);
    },
    [props.viewer]
  );

  return (
    <div className="w-full shrink-0 flex gap-unit-xl items-center h-[32px] px-4">
      <img
        src={props.editorSettings?.logo ?? "https://playground.ijewel3d.com/mini-editor-logo.svg"}
        alt="ijewel"
        className="h-full"
      />
      <Button
        href={props.editorSettings?.tutorialLink ?? "https://www.youtube.com/@iJewel3d"}
        as={Link}
        isExternal
        content="Tutorials"
        startContent={
          <div className="">
            <TutorialsIcon />
          </div>
        }
        size="sm"
        variant="bordered"
        className="h-full rounded-large text-sm font-mainSemiBold px-unit-xl"
        disableRipple

      >
        Tutorials
      </Button>
      <div className="flex-1"></div>
      {!props.editorSettings?.hideSaveButton && (
        <div className="flex justify-center items-center h-full">
          <Button
            isDisabled={saving}
            onClick={() => handleSaveDropdownClick("save")}
            name="Save"
            size="lg"
            color="primary"
            fullWidth
            className={
              "h-full rounded-r-none text-sm font-mainSemiBold px-unit-xl"
            }
            disableRipple
          >
            Save
          </Button>
          <Dropdown
            className="rounded-small"
            key={"save-dropdown"}
            // showArrow
            size="sm"
            offset={10}
            // shadow=""
            placement="bottom-end"
            classNames={{
              base: "",
              content: "p-0 shadow-md",
            }}
          >
            <DropdownTrigger>
              <Button
                disabled={!props.hasEdited}
                // onClick={() => handleSaveDropdownClick("save")}
                name="drop"
                size="sm"
                color="primary"
                startContent={<ArrowDownIcon />}
                className={
                  "h-full rounded-large rounded-l-none  !px-0 ml-[1px]"
                }
                disableRipple
              ></Button>
            </DropdownTrigger>
            <DropdownMenu
              onAction={(key) => handleSaveDropdownClick(key as string)}
              className="p-0  "
              classNames={{
                list: "gap-0 [&>*]:rounded-none [&>*]:p-unit-lg [&>*]:px-unit-xl",
              }}
            >
              {props.editorSettings?.previewLink && (
                <DropdownItem
                  key={"saveAndPreview"}
                  hidden={!props.editorSettings?.previewLink}
                  className={` border-b-default-100 border-b-1 text-sm`}
                >
                  <p className="font-mainSemiBold text-sm">Save & Preview</p>
                  <p className="text-sm">The preview opens in a new tab</p>
                </DropdownItem>
              ) as any}
              <DropdownItem key={"reset"} className="">
                <p className="font-mainSemiBold text-sm">Reset settings</p>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </div>
      )}
    </div>
  );
};

export { Header };
