import { createContext, createElement, useContext, useState } from "react";
import { Project } from "../../main";

const ProjectContext = createContext({
  project: {} as Project,
  setProject: (_: Project) => {},
} as { project: Project ; setProject: (project: Project | ((project : Project) => Project )) => void });
export const useProject = () => useContext(ProjectContext);

function useSetupProject(initialValue?: Project) {
  const [project, setProject] = useState<Project>(initialValue || {});
  return {
    project,
    setProject,
  };
}

export function ProjectProvider({ children, initialValue }: { children: any; initialValue?: any }) {
  const value = useSetupProject(initialValue);
  return createElement(ProjectContext.Provider, { value }, children);
}

