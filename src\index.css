@layer mini-editor;
@layer mini-editor {
  @tailwind base;
  @tailwind components;
  @tailwind utilities;
}

@font-face {
  font-family: "Segoe UI";
  src: url("https://fonts.cdnfonts.com/css/segoe-ui-4");
}

@font-face {
  font-family: "Inter-regular";
  src: url("./assets/Inter-Regular.woff2");
}
@font-face {
  font-family: "Inter-semibold";
  src: url("./assets/Inter-SemiBold.woff2");
}

/* To prevent scroll */
/* body {
  position: fixed;
} */

/* body,
html {
  background: #f0f0f0 !important;
} */

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.spinner {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid black;
  border-top-color: rgba(255, 255, 255, 0.2);
  animation: spin 1s ease-in-out infinite;
  margin-top: 30px;
}

.backdrop-content {
  width: 100%;
  height: 100%;
  backdrop-filter: invert(100%);
  -webkit-backdrop-filter: invert(100%);
  pointer-events: none;
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}

.theimg {
  backdrop-filter: blur(0px);
  animation: fadein ease-out 0.5s;
}

@keyframes fadein {
  0% {
    backdrop-filter: opacity(0%) blur(0px);
  }
  28% {
    backdrop-filter: opacity(100%) blur(10px);
  }
  100% {
    backdrop-filter: opacity(100%) blur(10px);
  }
}

.cursor-style {
  position: absolute;
  transition: top 0.5s ease, left 0.5s ease, width 0.5s ease, height 0.5s ease;
}

.posAnim {
  transition: top 0.5s ease, left 0.5s ease, width 0.5s ease, height 0.5s ease;
}

.util-button {
  box-sizing: content-box !important;
}

/*!*For tweakpane editor, todo remove*!*/
.mode-buttons-container{
  max-width: 300px !important;
  left: unset !important;
  right: 10px !important;
  top: 20px !important;
  border-radius: .5rem .5rem 0 0 !important;

  display: none !important;
}
.mode-button{
  text-wrap: nowrap;
}

#tweakpaneUiContainer {
  position: unset !important;
  width: 100% !important;
  max-width: 100% !important;
  left: 0 !important;
  margin-top: 0 !important;
  padding: 0 !important;
}

/*Top small scroll horiz*/
/*.mode-buttons-container{*/
/*  max-width: 300px !important;*/
/*  left: unset !important;*/
/*  right: 10px !important;*/
/*  top: 20px !important;*/
/*  border-radius: .5rem .5rem 0 0 !important;*/

/*}*/
/*.mode-button{*/
/*  text-wrap: nowrap;*/
/*}*/

#tweakpaneUiContainer > .tp-rotv > .tp-rotv_b {
  display: none !important;
}
/*#tweakpaneUiContainer > .tp-rotv.tp-rotv-expanded .tp-rotv_c, .tp-fldv.tp-fldv-expanded>.tp-fldv_c {*/
/*  padding-top: 0 !important;*/
/*}*/



#tweakpaneUiContainer::-webkit-scrollbar
{
  display: none;
  /*width: 8px;  !* for vertical scrollbars *!*/
  /*height: 8px; !* for horizontal scrollbars *!*/
}
/*#tweakpaneUiContainer ::-webkit-scrollbar-track*/
/*{*/
/*  background: #28223Ccc !important;*/
/*  border-radius: 6px;*/
/*}*/
/*#tweakpaneUiContainer ::-webkit-scrollbar-thumb*/
/*{*/
/*  background: #ffffff66;*/
/*  border-radius: 6px;*/
/*}*/
/*#tweakpaneUiContainer ::-webkit-scrollbar-corner {background: rgba(0,0,0,0.5);}*/

/*windowise*/
.wwise-overlay{
  position: absolute !important;
}

div:has(>.wwise-wrapper){
  z-index: 100;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}
.wwise .modal>.main .title {
  font-size: 30px;
  line-height: 40px;
}

.tabList-last-tab{
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
.tabList-last-tab::before{
  flex-grow: 1;
  content: ' ';
}

#tweakpaneUiContainer{
  max-height: 100% !important;
}

.util-buttons-container {
  top: 4.25rem !important;
  right: 1rem !important;
  position: absolute !important;
  gap: 8px !important;
  padding: .25rem !important;
  flex-direction: column !important;
  height: fit-content !important;
  z-index: 1 !important;
}
.button-bar>.button-bar-button:not(:last-child)::after {
  content: "" !important;
  position: absolute !important;
  bottom: -5px !important;
  left: 20% !important;
  height: 2px !important;
  width: 60% !important;
  top: auto !important;
}

/* blink animation */
@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}

.blink {
  background-color: #6e73f227;
  animation: blink 1s infinite;
}

.tp-rotv{
  background-color: transparent !important;
  box-shadow: none !important;
}
.tp-fldv_i::before{
  background-color: transparent !important;
}
/* top border */
.tp-rotv_b, .tp-fldv_b{
  /* background-color: transparent !important; */
  /* border : solid 0px 1px #6e73f2 !important; */
  /* border-top : solid 1px #cfcfcf !important; */
}
.tp-fldv_t{
  font-family: Inter-semibold, sans-serif !important;
}
#customContextMenu{
  background-color: white !important;
  color: #2E2E2E !important;
  border-radius: 0.5rem !important;
  padding: 0.3rem !important;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2) !important;
  border : none !important;
  /* width: 200px !important; */
}
.customContextMenuItems{
  padding: 0.3rem !important;
  cursor: pointer !important;
  font-family: Inter, sans-serif !important;
  border-radius: 0.3rem !important;
  color: #2E2E2E !important;
  padding-right: 20px !important;
}
.customContextMenuItems:hover{
  background-color: #6e73f2 !important;
  color: white !important;
}