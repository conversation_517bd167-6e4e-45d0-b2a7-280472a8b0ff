import { ContextType, createContext, createElement, useCallback, useContext, useState } from "react";
import { ExportLimits, freeUserLimits, getBrandingDefaultSettings, getEmbedDefaultSettings } from "../utils/limits";
import { DiamondPlugin, IMaterial, Material, MaterialConfiguratorBasePlugin, MaterialConfiguratorPlugin, Mesh, MeshPhysicalMaterial, MeshStandardMaterial, Object3D, ViewerApp } from "webgi";
import { ConfirmToast, WarningToast } from "../ConfirmToast";
import { IjewelEditorPlugin } from "../viewer/IjewelEditorPlugin";
import PaintIcon from "../icons/Paint";
import ED_SETTING from "../../types";

const embedDefaultSettings = getEmbedDefaultSettings()
const brandingDefaultSettings = getBrandingDefaultSettings()

interface UiContextType {
  freeze: boolean;
  setFreeze: (freeze: boolean) => void;
  limits: ExportLimits;
  setLimits: (limits: ExportLimits) => void;
  showDefaultMaterialsPrompt: (viewer: ViewerApp) => void;
  autoApplyModelStageConfig: boolean;
  setAutoApplyModelStageConfig: (autoApplyModelStageConfig: boolean) => void;
  showDefaultMaterialConfigurator: (viewer: ViewerApp) => void;
  showCacheKeysWarnnin: (viewer: ViewerApp) => void;
  embed: {
    logoList: any[] | null;
    setLogoList: React.Dispatch<React.SetStateAction<any[] | null>>
    settings: ED_SETTING.EMBED_SETTING;
    setSettings: React.Dispatch<React.SetStateAction<ED_SETTING.EMBED_SETTING>>
    embedPosterUrl: string | null;
    setEmbedPosterUrl: React.Dispatch<React.SetStateAction<string | null>>
  };
  branding:{
    settings: ED_SETTING.BRANDING_SETTING
    setSettings: React.Dispatch<React.SetStateAction<ED_SETTING.BRANDING_SETTING>>
  }
}

export const UiContext = createContext<UiContextType>({
  freeze: false,
  setFreeze: () => {},
  limits: freeUserLimits,
  setLimits: () => {},
  showDefaultMaterialsPrompt: () => {},
  autoApplyModelStageConfig: false,
  setAutoApplyModelStageConfig: () => {},
  showDefaultMaterialConfigurator: () => {},
  showCacheKeysWarnnin: () => {},
  embed: { logoList: null, setLogoList: () => {}, settings: embedDefaultSettings, setSettings: () => {}, embedPosterUrl: null, setEmbedPosterUrl: () => {}},
  branding: { settings: brandingDefaultSettings, setSettings: () => {} },
});

export const useUi = () => useContext(UiContext);

function useSetupUi(initialValue?: any) {
  const [freeze, setFreeze] = useState<boolean>(initialValue?.freeze || false);
  const [limits, setLimits] = useState<ExportLimits>(initialValue?.limits || freeUserLimits);
  const [autoApplyModelStageConfig, setAutoApplyModelStageConfig] = useState<boolean>(initialValue?.autoApplyModelStageConfig || false);
  // embedding states
  const [logoList, setLogoList] = useState<any[] | null>(null)
  const [embedSettings, setEmbedSettings] = useState<Record<string, any>>(embedDefaultSettings);
  // branding states
  const [brandingSettings, setBrandingSettings] = useState<ED_SETTING.BRANDING_SETTING>(brandingDefaultSettings);
  // embed poster url
  const [embedPosterUrl, setEmbedPosterUrl] = useState<string | null>(null);

  const addColorVariations = useCallback(
    (
      configurator: MaterialConfiguratorPlugin,
      baseMaterial: MeshStandardMaterial,
      colors: { color: number; name: string; icon: string }[]
    ) => {
      colors.forEach((c) => {
        const newMat = baseMaterial.clone!();
        newMat.color.setHex(c.color);
        // Keep the scene material name during addition, then rename
        newMat.name = baseMaterial.name;
        // newMat.uuid = c.name;
        newMat.userData.icon = c.icon;
        newMat.userData.label = c.name;
        newMat.userData.rootPath = undefined
        configurator?.addVariation(newMat as any, baseMaterial.name, false);

        newMat.name = c.name;
      });
    },
    []
  );

  const showDefaultMaterialConfigurator = useCallback(
    (viewer: ViewerApp) => {
      const layers = getLayerNames(viewer)
      if (!layers.size) {
        return;
      }

      ConfirmToast(`This scene has ${layers.size} materials with supported layer names. Do you want to add a default material configurator?`, "default-material-configurator", () => {
        const configurator = viewer.getPlugin(MaterialConfiguratorPlugin);
        if (!configurator) return;

        const layerMaterials: MeshStandardMaterial[] = [];
        viewer.traverseSceneObjects((obj: any) => {
          const material = (obj as Mesh).material as MeshStandardMaterial;
          if (material && layers.has(material.name) && !layerMaterials.find((m)=> material.name === m.name)){
            layerMaterials.push(material);
          }
        });

        layerMaterials.forEach((material) => {
          if (material.name.startsWith("Gem")) {
            addColorVariations(configurator, material, GemColors);
          } else if (material.name.startsWith("Metal")) {
            addColorVariations(configurator, material, MetalColors);
          }
        });

        configurator.refreshUi();
      });
    },
    [addColorVariations]
  );

  const getLayerNames = (viewer : ViewerApp)=>{
    const materialNames = new Set<string>();

    const materials = getLayerMaterials(viewer);
    materials.forEach((material) => {
      if (material.name) {
        materialNames.add(material.name);
      }
    });

    return materialNames
  }

  const getLayerMaterials = (viewer: ViewerApp) => {
    const materials: Set<Material> = new Set();
    viewer.scene.traverse((obj: Object3D) => {
      const material = (obj as Mesh).material;
      if (Array.isArray(material)) {
        material.forEach((m) => {
          if (m && m.name && isSupportedLayerName(m.name)) {
            materials.add(m);
          }
        });
      } else {
        if (material && material.name && isSupportedLayerName(material.name)) {
          materials.add(material);
        }
      }
    }
    );
    return materials;
  }

  const isSupportedLayerName = (layerName: string) => {
    return layerName.startsWith("Gem") || layerName.startsWith("Metal");
  }

  const showDefaultMaterialsPrompt = useCallback(
    (viewer: ViewerApp) => {
      const layers = getLayerNames(viewer)
      if (!layers.size) {
        return;
      }

      // prompt to apply default materials
      ConfirmToast(
        `This scene has ${layers.size} materials with supported layer names. Do you want to apply default materials to them?`,
        "default-materials",
        async () => {
          const plugin = viewer.getPlugin(IjewelEditorPlugin);
          await plugin?.applyDefaultConfig?.(false, true);

          // prompt to add configurator
          showDefaultMaterialConfigurator(viewer);
          showCacheKeysWarnnin(viewer);
        }, <PaintIcon />
      );
    },
    [showDefaultMaterialConfigurator])


    const showCacheKeysWarnnin = useCallback(
      (viewer: ViewerApp) => {
        if(!viewer || !viewer.scene.modelRoot.children.length) return;
        const diamond = viewer.getPlugin(DiamondPlugin)
        if(!diamond || !(diamond as any).getAllCacheMaps) return;
        const cacheKeys =  (diamond as any).getAllCacheMaps();
        if(!cacheKeys || cacheKeys.length < 5) return;
        WarningToast(`This scene has ${cacheKeys.length} cache keys. This may cause performance issues.`, "cache-keys-warning");
    }, [])

  return {
    freeze,
    setFreeze,
    limits,
    setLimits,
    showDefaultMaterialsPrompt,
    autoApplyModelStageConfig,
    setAutoApplyModelStageConfig,
    showDefaultMaterialConfigurator,
    showCacheKeysWarnnin,
    embed: { logoList, setLogoList, settings: embedSettings, setSettings: setEmbedSettings, embedPosterUrl, setEmbedPosterUrl },
    branding: { settings: brandingSettings, setSettings: setBrandingSettings }
  };
}

export function UiProvider({ children, initialValue }: { children: any; initialValue?: any }) {
  const value = useSetupUi(initialValue);
  return createElement(UiContext.Provider, { value }, children);
}

//white gold, yellow gold, rose gold
const MetalColors = [ 
  {name :  "White Gold", color : 0xc2c2c3 , icon : "https://packs.ijewel3d.com/files/1_metal_whitegold_polished_0bfa185673.png"},
  {name :  "Yellow Gold", color : 0xe5b377 , icon : "https://packs.ijewel3d.com/files/1_metal_gold_polished_5bdbd506e2.png"},
  {name :  "Rose Gold", color : 0xf2af83 , icon : "https://packs.ijewel3d.com/files/1_metal_rosegold_polished_ad37f22b2e.png"}
];

//white, emerald, ruby
const GemColors = [
  {name :  "White", color : 0xffffff , icon : "https://packs.ijewel3d.com/files/1_gem_diamond_white_1_9d63920a5a.png"},
  {name :  "Emerald", color : 0x22dfa3 , icon : "https://packs.ijewel3d.com/files/1_gem_emerald_1_b2268c83eb.png"},
  {name :  "Ruby", color : 0xe14276 , icon : "https://packs.ijewel3d.com/files/1_gem_ruby_1_288f497dbb.png"}
];