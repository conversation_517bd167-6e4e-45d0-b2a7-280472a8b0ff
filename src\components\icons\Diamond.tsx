import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={16} height={12} fill="none" {...props}>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.70525 0.89119C3.79901 0.797422 3.92619 0.744743 4.0588 0.744743L11.1341 0.744743C11.2667 0.744743 11.3939 0.797422 11.4876 0.89119L14.3139 3.71751C14.5092 3.91278 14.5092 4.22936 14.3139 4.42462L7.94999 10.7886C7.75473 10.9838 7.43814 10.9838 7.24288 10.7886L0.878921 4.42462C0.683659 4.22936 0.683659 3.91278 0.878921 3.71751L3.70525 0.89119Z"
      fill={`url(#${props.fill})`}
      stroke="#F0F0F0"
      strokeWidth="1.4"
      strokeMiterlimit="10"
    />
    <defs>
      <linearGradient id="premium" x1="7.44196" y1="-2.32953" x2="14.0841" y2="4.6485" gradientUnits="userSpaceOnUse">
        <stop stopColor="#B052FF" />
        <stop offset="1" stopColor="#81C3FF" />
      </linearGradient>
      <linearGradient id="business" x1="1" y1="-5" x2="15.8561" y2="11.6285" gradientUnits="userSpaceOnUse">
        <stop offset="0.333333" stopColor="#FFE0A4" />
        <stop offset="0.666667" stopColor="#B5821F" />
      </linearGradient>
    </defs>
  </svg>
);
export default SvgComponent;
