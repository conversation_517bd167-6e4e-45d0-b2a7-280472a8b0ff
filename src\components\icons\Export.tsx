import * as React from "react"
import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <path
      stroke="currentColor"
      strokeWidth={1.458}
      d="M4 12c0 .828 0 1.243.135 1.569a1.778 1.778 0 0 0 .963.962c.326.136.74.136 1.569.136h.6c.516 0 .775 0 .981.119.052.03.1.065.146.105.178.158.26.402.423.893l.111.333c.196.587.293.88.526 1.05.233.166.542.166 1.161.166h2.77c.619 0 .928 0 1.16-.168.234-.168.331-.46.527-1.047l.111-.334c.164-.49.245-.735.423-.893a.885.885 0 0 1 .146-.105c.206-.12.465-.12.981-.12h.6c.829 0 1.243 0 1.57-.135a1.78 1.78 0 0 0 .962-.962c.135-.326.135-.74.135-1.57M9.778 9.779 12 7.555m0 0 2.222 2.223M12 7.555v6.223M4 7.556c0-1.677 0-2.514.52-3.035C5.043 4 5.88 4 7.557 4h8.888c1.677 0 2.514 0 3.035.52C20 5.043 20 5.88 20 7.557v8.888c0 1.677 0 2.514-.52 3.035-.522.521-1.36.521-3.036.521H7.556c-1.677 0-2.514 0-3.035-.52C4 18.957 4 18.12 4 16.443V7.556Z"
    />
  </svg>
)
export default SvgComponent
