import { motion } from "framer-motion";
import { FC, useState } from "react";

interface IconProps {
  src: string;
  className?: string;
}

const Img: FC<IconProps> = (props) => {
  const [imageLoaded, setImageLoaded] = useState(false);

  return (
    <motion.img
      initial={{ opacity: 0 }}
      animate={{ opacity: imageLoaded ? 1 : 0}}
      onLoad={() => setImageLoaded(true)}
      alt={props.src}
      src={props.src}
      className={" " + props.className}
    ></motion.img>
  );
};

export { Img };
/* Rectangle 13 */
