import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="18"
    height="17"
    viewBox="0 0 18 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M15.0504 3.78462C15.0504 3.49851 14.8179 3.26599 14.5318 3.26599H11.7658C11.4797 3.26599 11.2472 3.49851 11.2472 3.78462C11.2472 4.07074 11.4797 4.30326 11.7658 4.30326H14.5318C14.8179 4.30326 15.0504 4.07074 15.0504 3.78462Z"
      fill="#AFAFAF"
    />
    <path
      d="M15.0504 6.20479C15.0504 5.91868 14.8179 5.68616 14.5318 5.68616H11.7658C11.4797 5.68616 11.2472 5.91868 11.2472 6.20479C11.2472 6.4909 11.4797 6.72342 11.7658 6.72342H14.5318C14.8179 6.72342 15.0504 6.4909 15.0504 6.20479Z"
      fill="#AFAFAF"
    />
    <path
      d="M15.0505 8.62508C15.0505 8.33896 14.818 8.10645 14.5319 8.10645H13.754C13.4679 8.10645 13.2354 8.33896 13.2354 8.62508C13.2354 8.91119 13.4679 9.14371 13.754 9.14371H14.5319C14.818 9.14371 15.0505 8.91119 15.0505 8.62508Z"
      fill="#AFAFAF"
    />
    <path
      d="M3.4679 3.78462C3.4679 3.49851 3.70041 3.26599 3.98653 3.26599H6.75251C7.03862 3.26599 7.27114 3.49851 7.27114 3.78462C7.27114 4.07074 7.03862 4.30326 6.75251 4.30326H3.98653C3.70041 4.30326 3.4679 4.07074 3.4679 3.78462Z"
      fill="#AFAFAF"
    />
    <path
      d="M3.98653 5.68628C3.70041 5.68628 3.4679 5.9188 3.4679 6.20491C3.4679 6.49102 3.70041 6.72354 3.98653 6.72354H6.75251C7.03862 6.72354 7.27114 6.49102 7.27114 6.20491C7.27114 5.9188 7.03862 5.68628 6.75251 5.68628H3.98653Z"
      fill="#AFAFAF"
    />
    <path
      d="M3.98653 8.10645C3.70041 8.10645 3.4679 8.33897 3.4679 8.62508C3.4679 8.91119 3.70041 9.14371 3.98653 9.14371H4.76445C5.05057 9.14371 5.28309 8.91119 5.28309 8.62508C5.28309 8.33897 5.05057 8.10645 4.76445 8.10645H3.98653Z"
      fill="#AFAFAF"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.3313 11.4447C11.1913 11.8985 11.1913 13.131 10.3313 13.5848L9.39173 14.0801C8.58614 14.5054 7.61719 13.9211 7.61719 13.0101V12.0195C7.61719 11.1084 8.58614 10.5241 9.39173 10.9494L10.3313 11.4447ZM9.84639 12.6677C9.96999 12.6029 9.96999 12.4266 9.84639 12.3618L8.90768 11.8665C8.79272 11.806 8.65443 11.8889 8.65443 12.0195V13.01C8.65443 13.1406 8.79273 13.2235 8.90768 13.163L9.84639 12.6677Z"
      fill="#AFAFAF"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.875122 2.40162C0.875122 1.35141 1.72653 0.5 2.77674 0.5H6.40704C7.56875 0.5 8.58871 1.10247 9.17302 2.01264C9.75733 1.10245 10.7773 0.5 11.939 0.5H15.5693C16.6195 0.5 17.4709 1.35141 17.4709 2.40162V10.2802C17.4709 11.3304 16.6195 12.1819 15.5693 12.1819H13.4879C13.4922 12.264 13.4948 12.3452 13.4948 12.4282C13.4948 14.8147 11.5595 16.75 9.17303 16.75C6.78656 16.75 4.85124 14.8147 4.85124 12.4282C4.85124 12.3452 4.85383 12.264 4.85816 12.1819H2.77677C1.72656 12.1819 0.875144 11.3304 0.875144 10.2802L0.875122 2.40162ZM15.5693 11.1448H13.3012C12.8042 9.54396 11.4014 8.34162 9.69174 8.1376V3.78461C9.69174 2.54337 10.6979 1.53731 11.939 1.53731H15.5693C16.0465 1.53731 16.4337 1.92455 16.4337 2.40167V10.2803C16.4337 10.7583 16.0465 11.1448 15.5693 11.1448ZM8.65438 3.78461V8.1376C6.94465 8.34159 5.54188 9.54394 5.04489 11.1448H2.77679C2.29967 11.1448 1.91243 10.7584 1.91243 10.2804V2.4018C1.91243 1.92468 2.29967 1.53744 2.77679 1.53744H6.40708C7.64832 1.53744 8.65438 2.54344 8.65438 3.78461ZM5.8884 12.4282C5.8884 10.6139 7.35869 9.14353 9.17306 9.14353C10.9874 9.14353 12.4577 10.6138 12.4577 12.4282C12.4577 14.2426 10.9874 15.7128 9.17306 15.7128C7.35869 15.7128 5.8884 14.2426 5.8884 12.4282Z"
      fill="#AFAFAF"
    />
  </svg>
);
export default SvgComponent;
