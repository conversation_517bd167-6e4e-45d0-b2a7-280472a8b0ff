import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { PosePlugin, ViewerApp, CanvasSnipperPlugin, Object3D, Mesh, Sphere, Vector3, Box3B, Matrix4 } from "webgi";
import stablePosePng from "../assets/pose-stable.png";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@nextui-org/react";
import PlusSolid from "./icons/PlusSolid";
import ArrowRight from "./icons/ArrowRight";
import Close from "./icons/Close";
import { useProject } from "./provider/ProjectProvider";
import toast from "@minieditor/react-hot-toast";

interface PosesSettingsProps {
  viewer?: ViewerApp;
  selectedPresetTab?: string;
}

type CustomPose = NonNullable<Object3D["_stablePoses"]>[0] & { position?: (string | number | undefined)[]; rotation?: (string | number | undefined)[]; isAdded?: boolean; url?: string };

export default function PosesSettings(props: PosesSettingsProps) {
  const { viewer } = props;
  const [poseData, setPoseData] = useState<{
    poses: CustomPose[];
    currentPoseIdx: number;
    poseOpt: "stable" | "custom" | "original";
    selectedPoseIdx: number | null;
  }>({ poses: [], currentPoseIdx: -1, poseOpt: "original", selectedPoseIdx: null});

  const { setProject } = useProject();

  // const saveAllAddedPoses = async (selectedPoses: CustomPose[]) => {
  //   let allPoses: any = null;
  //   if (selectedPoses.length > 0) {
  //     allPoses = selectedPoses.map((pose) => ({ rotation: pose.rotation, position: pose.position }));
  //   }
  //   setProject((prev) => ({ ...prev, allPoses: allPoses }));
  // };

  const saveSelectedPose = async (idx: number | null, poseOpt?: string) => {
    let pose: any;
    if (idx !== null && poseOpt === "custom") {
      const selectedPose = poseData.poses[idx];
      pose = { rotation: selectedPose.rotation, position: selectedPose.position };
    } else if (poseOpt === "stable" || poseOpt === "original") {
      pose = { rotation: viewer!.scene.modelRoot.rotation.toArray(), position: viewer!.scene.modelRoot.position.toArray() };
    } else {
      pose = null;
    }
    setProject((prev) => ({ ...prev, currentPose: pose }));
  };

  useEffect(() => {
    async function clipSnapshot() {
      const poses = poseData.poses;

      if (!viewer || poses.length < 1) return;

      const currentPoseIdx = poseData.currentPoseIdx;
      const pose = poses[currentPoseIdx];

      // generate snapshot for the current pose
      if (pose && !pose.url) {
        const snapper = await viewer.getOrAddPlugin(CanvasSnipperPlugin)!;
        viewer.doOnce("postFrame", async () => {
          const file = await snapper.getFile("pose-" + currentPoseIdx + ".png", { mimeType: "image/png", scale: 0.7, waitForProgressive: true });
          if (file) {
            const url = URL.createObjectURL(file);
            poses[currentPoseIdx].url = url;
          }
          // save model current pose rotation and position
          pose["rotation"] = viewer.scene.modelRoot.rotation.toArray();
          pose["position"] = viewer.scene.modelRoot.position.toArray();

          setPoseData((prev) => ({
            ...prev,
            poses: poses,
          }));
        });
      }
    }

    clipSnapshot();
  }, [poseData.currentPoseIdx, poseData.poses, viewer]);

  const handlePoseAction = useCallback(
    async (key: string, idx?: number) => {
      if (!viewer) return;

      const _3dObject = viewer.scene.modelRoot.children[0];

      const plugin = viewer.getPlugin(PosePlugin);
      if (!plugin) return;

      if (plugin?.autoUpdateGround === true) plugin.autoUpdateGround = false;
      switch (key) {
        case "computePoses":
          if (plugin) {
            plugin.clearPoses(_3dObject);
            const poseData = plugin.computeStablePoses(_3dObject);
            // plugin.setPose(_3dObject, 0);
            // alignCameraForGroundPose(viewer, viewer.scene.modelRoot.children[0])
            setPoseData((prev) => ({
              ...prev,
              poses: poseData,
              currentPoseIdx: -1,
              selectedPoseIdx: null,
              poseOpt: "custom",
            }));
            // saveAllAddedPoses([]);
          }
          break;

        case "nextPose":
          const nextPoseIdx = (poseData.currentPoseIdx + 1) % poseData.poses.length;
          plugin.setPose(_3dObject, nextPoseIdx);
          setPoseData((prev) => ({
            ...prev,
            currentPoseIdx: nextPoseIdx,
            poseOpt: "custom",
          }));
          break;

        case "previousPose":
          const previousPoseIdx = (poseData.currentPoseIdx - 1 + poseData.poses.length) % poseData.poses.length;
          plugin.setPose(_3dObject, previousPoseIdx);
          setPoseData((prev) => ({
            ...prev,
            currentPoseIdx: previousPoseIdx,
            poseOpt: "custom",
          }));
          break;

        case "stablePoses":
          plugin.setMostStablePose(_3dObject);
          // alignCameraForGroundPose(viewer, viewer.scene.modelRoot.children[0])
          setPoseData((prev) => ({
            ...prev,
            poseOpt: "stable",
            selectedPoseIdx: null,
          }));
          saveSelectedPose(null, "stable");
          break;

        case "selectedPose":
          if (idx === undefined) break;
          plugin.setPose(_3dObject, idx);
          setPoseData((prev) => ({
            ...prev,
            selectedPoseIdx: idx,
            poseOpt: "custom",
          }));
          saveSelectedPose(idx, "custom");
          break;

        case "resetOriginalPose":
          plugin.setOriginalPose(_3dObject);
          setPoseData((prev) => ({
            ...prev,
            poseOpt: "original",
            selectedPoseIdx: null,
          }));
          saveSelectedPose(null, "original");
          break;

        case "deletePose": {
          if (idx === undefined) break;
          let poses = poseData.poses;
          poses[idx].isAdded = false;
          setPoseData((prev) => ({ ...prev, poses }));
          // saveAllAddedPoses(poseData.poses.filter((pose) => pose.isAdded));
          break;
        }

        case "addCurrentPose": {
          let newData = poseData;
          const allSelectedPoses = newData.poses.filter((pose) => pose.isAdded);

          if (allSelectedPoses.length + 1 > 5) {
            toast.error("You can only add up to 5 poses.", { id: "addPoseLimitError" });
            break;
          }

          setPoseData((prev) => {
            const currentPose = prev.poses[prev.currentPoseIdx];
            allSelectedPoses.push(currentPose);
            currentPose.isAdded = true;
            newData = { ...prev };
            return newData;
          });

          // saveAllAddedPoses(allSelectedPoses);
          break;
        }
        default:
          console.warn(`Unknown action: ${key}`);
      }
    },
    [viewer, poseData]
  );

  const isPosesComputed = poseData.poses.length > 0;

  if (props.selectedPresetTab !== "poses") return null;

  return (
    <div className="w-full flex flex-col gap-2.5 px-2">
      <Button
        // isDisabled={props.viewer && props.viewer.scene.modelRoot.children.length > 0}
        color={isPosesComputed ? "default" : "primary"}
        variant="solid"
        isDisabled={props.viewer && props.viewer.scene.modelRoot.children.length < 1}
        onClick={() => handlePoseAction("computePoses")}
        size="lg"
        fullWidth
        className={"!text-tiny !font-mainSemiBold"}
      >
        {isPosesComputed ? "Recompute" : "Compute"}
      </Button>

      {isPosesComputed && (
        <>
          <Button
            color="default"
            variant={poseData.poseOpt === "original" ? "flat" : "bordered"}
            onClick={() => handlePoseAction("resetOriginalPose")}
            size="lg"
            fullWidth
            className={" !text-tiny !font-mainSemiBold bottom-1 border-[#DBDBDB]"}
          >
            Return to Original Pose
          </Button>

          <div className={`rounded-md bg-[#FFFFFF] gap-2  p-2.5`}>
            <span className="rounded-md flex flex-col  items-center gap-1.5 ">
              <div className="flex justify-between self-stretch items-center gap-1">
                <Button className={`bg-[#F0F1FF] active:bg-[#b39ddb] rounded-[30px] min-w-[33px] w-[44px] h-[24px] flex justify-center items-center`} onClick={() => handlePoseAction("previousPose")}>
                  <ArrowRight fill="currentColor" />
                </Button>
                <div className="w-[50px] h-[50px] bg-[#F0F1FF] rounded-md overflow-hidden">
                  {poseData.poses[poseData.currentPoseIdx]?.url ? (
                    <img className="w-full h-full object-cover" src={poseData.poses[poseData.currentPoseIdx].url} alt="Stable" />
                  ) : poseData.currentPoseIdx == -1  ? <div></div>
                  : (
                    <Spinner size="sm" classNames={{ base: "h-full flex justify-center items-center" }} />
                  )}
                </div>
                <Button className={`bg-[#F0F1FF] active:bg-[#b39ddb] rounded-[30px] min-w-[33px] w-[44px] h-[24px] flex justify-center items-center`} onClick={() => handlePoseAction("nextPose")}>
                  <ArrowRight className="rotate-180" fill="currentColor" />
                </Button>
              </div>

              <span className="text-tiny font-semibold text-[#2E2E2E]">
                {poseData.currentPoseIdx + 1} of {poseData.poses.length}
              </span>

              <Button
                color={"primary"}
                isDisabled={poseData.poses[poseData.currentPoseIdx]?.isAdded || !poseData.poses[poseData.currentPoseIdx]?.url}
                variant="solid"
                onClick={() => handlePoseAction("addCurrentPose")}
                size="lg"
                fullWidth
                className={"!text-tiny !font-mainSemiBold gap-1.5"}
              >
                <PlusSolid widths={9} height={9} fill="currentColor"/> Add Current Pose
              </Button>
            </span>
          </div>

          <div className="flex flex-wrap justify-evenly gap-2.5">
            <span className={`w-20 h-20 rounded-md bg-[#FFFFFF] ${poseData.poseOpt === "stable" ? "border-2 border-[#6E72F2]" : ""}`} onClick={() => handlePoseAction("stablePoses")}>
              <Tooltip content="Stable Pose" placement="right">
                <img src={stablePosePng} alt="Stable" />
              </Tooltip>
            </span>

            {poseData.poses.map((item, idx) => {
              if (!item.isAdded) return null;
              return (
                <div className={`relative cursor-pointer`} key={idx} onClick={() => handlePoseAction("selectedPose", idx)}>
                  <div className="w-[80px] h-[80px] bg-[#F0F1FF] rounded-md overflow-hidden">
                    <img
                      className={`w-full h-full object-cover rounded-md ${poseData.poseOpt === "custom" && idx === poseData.selectedPoseIdx && item.isAdded ? "border-2 border-[#6E72F2]" : ""}`}
                      src={item.url}
                      alt="Stable"
                    />
                  </div>
                  {/* <span className="absolute bottom-[1.5px] right-[1.5px] font-semibold text-tiny text-[#2E2E2E] bg-[#f0f0f0] px-1 rounded-br-md">{idx + 1}</span> */}

                  <Button
                    className="absolute top-0 right-0 -translate-y-1/2 translate-x-1/2 p-1 min-w-fit min-h-fit w-3.5 h-3.5 bg-[#D7D7D7!important] active:bg-[#292929!important]"
                    variant="flat"
                    onClick={() => handlePoseAction("deletePose", idx)}
                  >
                    <Close width={6} height={6} fill="currentColor" />
                  </Button>
                </div>
              );
            })}
          </div>
        </>
      )}
    </div>
  );
}


const findCenterPosition = (viewer: ViewerApp, currentModel: Object3D) => {
  if(!currentModel)
    return

  let maxRadius = -1;

  const radiusMap: any = {}

  currentModel.traverse( (object: any) => {
    if (object.isMesh && object.material) {
      if (object.material.isDiamondMaterial) {
        const boundingSphere = new Sphere(new Vector3(object.geometry.boundingSphere.center), object.geometry.boundingSphere.radius)
        boundingSphere.applyMatrix4(object.matrixWorld)
        const radius = boundingSphere.radius;

        const key = radius.toFixed(1)
        if(radiusMap[key]) {
          radiusMap[key].push(object)
        } else {
          radiusMap[key] = []
          radiusMap[key].push(object)
        }

        if (radius > maxRadius) {
          maxRadius = radius
        }
      }
    }
  })
  maxRadius = -1

  Object.keys(radiusMap).forEach((key)=>{
    const radius = parseFloat(key);
    if (radius > maxRadius) {
          maxRadius = radius
    }
  })

  const objects = radiusMap[maxRadius.toFixed(1)]
  const center = new Vector3()
// Find average center of all objects
  if(objects) {
  let count = 0
  for( const object of objects) {
    const boundingSphere = new Sphere(new Vector3(object.geometry.boundingSphere.center), object.geometry.boundingSphere.radius)
    boundingSphere.applyMatrix4(object.matrixWorld)
    const position = new Vector3().copy(object.geometry.boundingSphere.center)
    object.localToWorld(position)
    center.add(position)
    count++
  }
  center.multiplyScalar(1/count)
  } else {
  const posePlugin = viewer.getPlugin(PosePlugin)!;
  // Set the original pose to get the max axis
  posePlugin.setOriginalPose(currentModel)
  currentModel.updateMatrixWorld(true)
  const axis = getMaxAxisFromObjectBounds(currentModel)
  // revert back to ground pose
  posePlugin.setNextPose(currentModel)
  posePlugin.setPose(currentModel, 0)
  currentModel.updateMatrixWorld(true)
  // apply transform to axis
  const rotXform = new Matrix4()
  rotXform.extractRotation(currentModel.matrixWorld)
  axis.applyMatrix4(rotXform)
  const bb = new Box3B().expandByObject(currentModel, true, true)
  const target = bb.getCenter(new Vector3())
  center.copy(target).addScaledVector(axis, 1)
  }
  return center
}


const getMaxAxisFromObjectBounds = (object: Object3D)=> {
	const center = new Vector3()
	const bb = new Box3B().expandByObject(object, true, true)
	const extents = new Vector3().copy(bb.max).sub(bb.min)
	let maxSide = 'x'
	if(extents.x > extents.y) {
		if(extents.x > extents.z) {
			maxSide = 'x'
		} else {
			maxSide = 'z'
		}
	} else {
		if(extents.y > extents.z) {
			maxSide = 'y'
		} else {
			maxSide = 'z'
		}
	}
	const target = bb.getCenter(new Vector3())
	center.copy(target)
	const axis = new Vector3()
	const rotXform = new Matrix4()
	rotXform.extractRotation(object.matrixWorld)
	if( maxSide === 'x') {
		axis.set(1, 0, 0).applyMatrix4(rotXform)
	}
	if( maxSide === 'y') {
		axis.set(0, 1, 0).applyMatrix4(rotXform)
	}
	if( maxSide === 'z') {
		axis.set(0, 0, 1).applyMatrix4(rotXform)
	}
	center.addScaledVector(axis, 1)
	return axis
}

const alignCameraForGroundPose = (viewer: ViewerApp, obj : Object3D) => {
  let centerObject = findCenterPosition(viewer, obj)
  const dist = 7
  const lat = 0
  const lon = 0

  if (centerObject) {
    const position = new Vector3().copy(centerObject)
		const bb = new Box3B().expandByObject(obj, true, true)
		const target = bb.getCenter(new Vector3())

		let direction = new Vector3().copy(position).sub(target).normalize()
		let camPos = new Vector3().copy(position).addScaledVector(direction, dist)
		camPos.y = target.y
      
		direction = new Vector3().copy(camPos).sub(target).normalize()
		const axis = new Vector3().crossVectors(direction, new Vector3(0, 1, 0)).normalize()
		direction.applyAxisAngle(axis, lat * Math.PI/180)
		direction.applyAxisAngle(new Vector3(0, 1, 0), lon * Math.PI/180)
		camPos = new Vector3().copy(position).addScaledVector(direction, dist)
		
		viewer.scene.activeCamera.position.copy(camPos)
		viewer.scene.activeCamera.target.copy(target)
		viewer.scene.activeCamera.positionUpdated(true)
		// without this there are some flickers in camera views while rendering. Basically the controllers updates in the next frame
		viewer.scene.activeCamera.controls?.update()
  }
}
