import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import cssInjectedByJsPlugin from "vite-plugin-css-injected-by-js";
import depsExternal from "rollup-plugin-node-externals";

import path from "path";
const mainFilePath = path.resolve(__dirname, "./src/main.tsx");
const version = "0.0.1";

// https://vitejs.dev/config/
export default defineConfig({
  define: {
    "process.env": {},
  },
  build: {
    lib: {
      // Could also be a dictionary or array of multiple entry points
      entry: mainFilePath,
      name: "ijewelEditor",
      // the proper extensions will be added
      fileName: "bundle",
      formats: ["iife"],
    },
    rollupOptions: {
      // make sure to externalize deps that shouldn't be bundled
      // into your library
      external: ["webgi"],
      output: {
        // Provide global variables to use in the UMD build
        // for externalized deps
        manualChunks: undefined,
        globals: {
          webgi: "window",
        },
      },
    },
  },
  plugins: [react(), cssInjectedByJsPlugin(), depsExternal()],
});
