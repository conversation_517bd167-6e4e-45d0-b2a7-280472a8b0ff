# Changelog for @ijewel3d/mini-editor

## [0.1.40] - 2025-06-16
### Added
- Embedding section, a new feature for high resolution poster.

## [0.1.39] - 2025-05-29
### Fixed
- When loading assets, check if assets path is passed first.

## [0.1.38] - 2025-05-22
### Added
- Branding section, changed branding field name.

## [0.1.37] - 2025-05-21
### Added
- Embedding section, under embed loading icon a Button to take branding section.

## [0.1.36] - 2025-05-21
### Added
-A new feature to Branding logo. With logo layout Setting Changes under Branding Settings tab.

## [0.1.36-dev.3] - 2025-05-20
### Added
- Branding setting, fix setting reset on logo change in ui.

## [0.1.36-dev.2] - 2025-05-15
### Fixed
- A bug in counting layer names in the scene.
- Added a feature in Branding tab to upload logo, and make logo settings.

## [0.1.36-dev.1] - 2025-05-13
### Added
- Added a feature to set a high resolution poster for embedding.

## [0.1.35] - 2025-05-12
### Added
- A toast to confirm clearing all scene settings on clicking clear scene.
### Fixed
- Applying materials to stl files.
- Fullscreen not showing material configurator 

## [0.1.34] - 2025-05-12
### Fixed
- Manu tab not closing when unselecting object.

## [0.1.33] - 2025-05-08
### Fixed
- Added switch to remove hologram in embedding, added setting.

## [0.1.32] - 2025-05-08
- add image .avif support in import

## [0.1.31] - 2025-05-08
### Fixed
- UI scroll issue of editor.
- Samples to get started, text and order changed.
- Streched configurator previews.
- Hide presets panel for mobile and tablet.

### Added
- Added a icon for removing a preset.

## [0.1.30] - 2025-05-05
### Added
- Added "Scene Export" for a vjson export in export section.
- Save Scene Settings and reuse in other projects, allow to use saved embedding settings, camera config, configurators setting, and presets.

## [0.1.29] - 2025-05-05
### Added
- Query params for playground for modelRootScale, autoScale, autoCenter and autoScaleRadius.

## [0.1.28] - 2025-04-22
### Fixed
- Image and video export for webgi version 0.11.1 and above. 

## [0.1.27] - 2025-04-22
### Fixed
- Material configurator plugin right hand side ui. 

## [0.1.26] - 2025-04-21
### Fixed
- Export setting, fixed image and video stretching issue.

## [0.1.25] - 2025-04-21
### Fixed
- Save embedding setting in project json, and use as default.
- Use svg background issue workaround only for webgi versions less than 0.11.0
- Fit object button fits the scene to the selected object if available.

## [0.1.24] - 2025-04-11
### Fixed
- Added a new feature to add custom material in material Preset panel.

## [0.1.23] - 2025-04-10
### Fixed
- A bug in svg backgrounds on Windows.
- fixed a bug, to add 5 tags.

## [0.1.22] - 2025-04-03
### Fixed
- a bug where default settings are not applied in rhino plugin.

## [0.1.21] - 2025-04-02
### Fixed
- Only apply default settings if the model was loaded.

## [0.1.21-dev.2] - 2025-03-28
### Fixed
- error in loading default env map on file drop.
- defualt materials not applied for sample models.

## [0.1.21-dev.1] - 2025-03-28
### Fixed
- Default materials override materials saved in the file.
### Added
- A confirm toast to load Material Configurator from VJSON presets.


## [0.1.20] - 2025-03-21
### Fixed
- Ui buttons, make style uniform.
- Handle a bug in materials mapping for old projects.
- Hanlde loading model stage vjson config.
- Revert changes in lock-file that was causing issues in some dependencies.

## [0.1.19] - 2025-03-20
### Added
- Load assets from packs based on project version

## [0.1.19-dev.3] - 2025-03-19
### Changed
- use meterial configurator index in material mapping instead of url in all cases

## [0.1.19-dev.2] - 2025-03-19
### Fixed
- a bug in materials mapping, when the material is selecetd from the configurator.

## [0.1.19-dev.1] - 2025-03-18
### Added
- `showSampleModels`, `showWelcome` and `showProjectSettings` to control the edtior ui.
## Changed
- on file drop, show materils prompt only if there is no materialConfig in the project
- on file drop, if sceneConfig is present in the project then apply it.
## Fixed
- minor issue in dropzone z index.

## [0.1.18] - 2025-03-14
### Changed
- Changing the material configurator option name will change the material name.
- Remove numbers from the beginning of configrurator option names.
- If a material lable is set, use it without formatting.

## [0.1.17] - 2025-03-12
### Fixed
- Remove rootPath form default material configurator.

## [0.1.16] - 2025-03-12
### Fixed
- fix selected configurator options not applied on load.

## [0.1.15] - 2025-03-04
### Fixed
- SSBevel, call setDirty after enabling or disabling SSbevel.

## [0.1.14] - 2025-02-26
### fix
- Presets, fix asset fetch after user login state change.
- Presets, add confirm popup to hide asset.

## [0.1.13] - 2025-02-25
### fix
- Embedding, fix logo fetch after user login state change.

## [0.1.12] - 2025-02-21
### Added
- Custom assets, thumbnail generation of the asset.
- Custom assets, archive the asset.
- Custom assets, uplaod asset's format validation based on preset type.

## [0.1.11] - 2025-02-20
### fixed
- a bug where it shows default materials instead of configurator prompt.

## [0.1.10] - 2025-02-20
### Changed
- Delay default material configurator prompt.

## [0.1.9] - 2025-02-19
### Changed
- Minor fix in project initilization.

## [0.1.8] - 2025-02-19
### Changed
- show default material configurator prompt when file is loaded from modelUrl.

## [0.1.7] - 2025-02-19
### Changed
- preview type for default material configurators to generate:sphere

### Added
- baseName parameter to editorSettings

## [0.1.6] - 2025-02-17
### fixed
- Alignment of sample icons in dropzone

### Changed
- Use the 2-rings sample model instead of wedding-set

## [0.1.5] - 2025-02-17
### fixed
- Embed tab section info lost on tab change
- Added configurator materials have the wrong icon

### Added
- New sample files in dropzone

## [0.1.4] - 2025-02-13
### Changed
- update webgi to 0.10.5

## [0.1.3] - 2025-02-13
### Added
- Poses tab
- SSBevel to Advanced settings tab
- Custom assets upload

## [0.1.0-temp.1] - YYYY-MM-DD
### Added
- Description of new features

### Changed
- Details of modifications

### Fixed
- Bug fixes and corrections

### Removed
- Deprecated or deleted features
