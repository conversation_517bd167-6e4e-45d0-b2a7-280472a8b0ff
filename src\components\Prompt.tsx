import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
} from "@nextui-org/react";
import React, { useEffect, useState } from "react";

interface PromptProps {
  message: string;
  onSubmit: (value: string) => void;
  onCancel: () => void;
  defaultValue?: string;
}

const Prompt: React.FC<PromptProps> = ({ message, onSubmit, onCancel, defaultValue }) => {
  const [inputValue, setInputValue] = useState(defaultValue || "");
  const [isOpen, onOpenChange] = useState(true);

  const handleSubmit = () => {
    onSubmit(inputValue);
  };

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Enter") {
        handleSubmit();
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [inputValue]);

  const isEncryptionPrompt = message?.startsWith("GLTFEncryption");

  return (
    <Modal
      isOpen={true}
      onOpenChange={onOpenChange}
      placement="top-center"
      backdrop="blur"
      classNames={{ wrapper: "z-[100000]", backdrop: " z-[10000]" }}
      isDismissable={false}
      closeButton={<></>}
      shadow="lg"
      className="overflow-hidden"
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1 text-small text-clip break-words pb-unit-sm">
            {isEncryptionPrompt ? "Access Restricted" : ""}
              <br />
              <span className="text-md font-normal">
                {isEncryptionPrompt ?  "This model is encrypted, please enter the password/key to continue" : message}
              </span>
            </ModalHeader>
            <ModalBody className="overflow-x-hidden">
              <Input
                // label={isEncryptionPrompt ? "Password/Key" : ""}
                placeholder={isEncryptionPrompt ?"Enter your password" : ""}
                type={isEncryptionPrompt ? "password" : "text"}
                autoComplete="new-password"
                variant="bordered"
                value={inputValue}
                onValueChange={setInputValue}
                classNames={{
                  label: "!mb-0",
                }}
              />
            </ModalBody>
            <ModalFooter>
              <Button
                color="primary"
                onPress={handleSubmit}
                className="p-unit-xl"
              >
                Confirm
              </Button>
              <Button
                color="danger"
                onPress={onCancel}
                className="p-unit-xl"
              > 
                Cancel
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};

interface UsePromptReturn {
  showPrompt: (message: string , defaultValue?: string) => Promise<string | null>;
  PromptComponent: React.FC;
}

const usePrompt = (): UsePromptReturn => {
  const [visible, setVisible] = useState(false);
  const [message, setMessage] = useState("");
  const [defaultValue, setDefaultValue] = useState<string | undefined>();
  const [resolvePrompt, setResolvePrompt] =
    useState<(value: string | null) => void | null>();

  const showPrompt = (msg: string, defaultValue?: string): Promise<string | null> => {
    setMessage(msg);
    setVisible(true);
    setDefaultValue(defaultValue);
    return new Promise<string | null>((resolve) => {
      setResolvePrompt(() => resolve);
    });
  };

  const handleResult = (result: string | null) => {
    setVisible(false);
    if (resolvePrompt) {
      resolvePrompt(result);
    }
  };

  const handleSubmit = (value: string) => {
    handleResult(value);
  };

  const handleCancel = () => {
    handleResult(null);
  };

  const PromptComponent = React.useMemo(() => {
    return function PromptWrapper() {
      return visible ? (
        <Prompt
          message={message}
          defaultValue={defaultValue}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
        />
      ) : null;
    };
  }, [visible, message, defaultValue]);

  return { showPrompt, PromptComponent };
};

export { usePrompt, Prompt };
