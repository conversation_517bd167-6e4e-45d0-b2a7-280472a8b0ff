import { Button, Input } from "@nextui-org/react";
import { FC, useEffect, useRef, useState } from "react";

interface InputProps {
  className?: string;
  type: string;
  label?: string;
  placeholder?: string;
  size?: "sm" | "md" | "lg";
  value?: string;
  onChange?: (value: any) => void;
  setIsValid?: (isValid: boolean) => void;
  errorMessage?: string;
  onBlur?: any;
  onSubmit?: any;
  startContent?: any;
  min?: number;
  max?: number;
  isDisabled?: boolean;
  endContent?: any;
}

const MyInput: FC<InputProps> = (props) => {
  const ref = useRef<HTMLInputElement>(null);
  const [validationState, setValidationState] = useState<"valid" | "invalid">("valid");
  const [errorMessage, setErrorMessage] = useState<string | undefined>(undefined);

  const handleKeyDown = (e: any) => {
    if (e.key === "Escape") {
      ref.current?.blur();
    }
    if (e.key === "Enter") {
      props.onSubmit?.();
      ref.current?.blur();
    }
  };

  useEffect(() => {
    if (!ref.current) return;
    ref.current.addEventListener("keydown", handleKeyDown);
    return () => {
      ref.current?.removeEventListener("keydown", handleKeyDown);
    };
  }, [props.onSubmit]);

  const validateValue = (v: string) => {
    if (props.type === "number") {
      const num = Number(v);
      if (isNaN(num) || (num == 0 && props.min !== 0)) {
        setValidationState("invalid");
        props.setIsValid?.(false);
        setErrorMessage("Please enter a valid number");
        return;
      }
      if (props.min !== undefined && num < props.min) {
        setValidationState("invalid");
        props.setIsValid?.(false);
        setErrorMessage(`Value must be at least ${props.min}`);
        return;
      }
      if (props.max !== undefined && num > props.max) {
        setValidationState("invalid");
        props.setIsValid?.(false);
        setErrorMessage(`Value must be at most ${props.max}`);
        return;
      }
    }
    props.setIsValid?.(true);
    setValidationState("valid");
    setErrorMessage(undefined);
  };

  const handleValueChange = (v: string) => {
    validateValue(v);
    props.onChange?.(v);
  };

  return (
    <Input
      isDisabled={props.isDisabled}
      ref={ref}
      key={"outside"}
      type={props.type}
      label={props.label}
      labelPlacement="outside"
      placeholder={props.placeholder}
      size={props.size}
      fullWidth
      value={props.value}
      startContent={props.startContent}
      onValueChange={handleValueChange}
      errorMessage={errorMessage || props.errorMessage}
      isInvalid={validationState === "invalid"}
      onBlur={props.onBlur}
      classNames={{
        label: ["text-default-600 font-inherit ml-unit-lg ", "font-light"],
        input:
          "text-default-foreground h-unit-2xl font-normal  placeholder:text-tiny placeholder:font-normal text-tiny placeholder:text-default-400 ",
        innerWrapper: [" font-bold"],
        inputWrapper: [
          "bg-default-50 border-default-300 border-1 py-unit-lg px-unit-md rounded-md ",
        ],
      }}
      className={props.className + ""}
      endContent={props.endContent}
    />
  );
};

export { MyInput as Input };
