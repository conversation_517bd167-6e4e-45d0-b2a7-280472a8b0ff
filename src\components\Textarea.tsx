import { Button, Input, Textarea } from "@nextui-org/react";
import { FC } from "react";
import { Icon } from "./Icon";

interface TextareaProps {
  className?: string;
  type: string;
  label?: string;
  placeholder?: string;
  size?: "sm" | "md" | "lg";
  onChange?: any;
  value?: string;
}

const MyTextarea: FC<TextareaProps> = (props) => {
  return (
    <Textarea
      key={"outside"}
      type={props.type}
      label={props.label}
      labelPlacement="outside"
      placeholder={props.placeholder}
      size={props.size}
      fullWidth
      value={props.value}
      onValueChange={props.onChange}
      classNames={{
        label: ["text-default-600 font-inherit ml-unit-lg ", "font-light"],
        input: "text-default-foreground h-unit-2xl font-normal  placeholder:text-tiny placeholder:font-normal placeholder:text-default-400 ",
        innerWrapper: [" font-bold"],
        inputWrapper: ["bg-default-100 ", "", "rounded-medium"],
      }}
      className={props.className}
    />
  );
};

export { MyTextarea as Textarea };
