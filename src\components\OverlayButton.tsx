import { But<PERSON> } from "@nextui-org/react";
import { FC } from "react";
import { Icon } from "./Icon";

interface OverlayButtonProps {
  name?: string;
  endIcon?: string;
  startIcon?: string;
  className?: string;
  color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger";
  size?: "sm" | "md" | "lg";
  varient?: "solid" | "bordered" | "light" | "flat" | "faded" | "shadow" | "ghost";
  heiglighted?: boolean;
  onClick?: any;
  fullWidth?: boolean;
}

const OverlayButton: FC<OverlayButtonProps> = (props) => {
  return (
    <Button
      onClick={props.onClick}
      color={props.color}
      endContent={props.endIcon ? <Icon src={props.endIcon} className="h-full" /> : null}
      startContent={props.startIcon ? <Icon src={props.startIcon} className="h-full" /> : null}
      className={
        "w-10 h-10 p-unit-lg rounded-full " +
        props.className +
        (props.heiglighted ? " bg-white bg-gradient-to-r from-[#B150FF] to-[#81C3FF] text-white" : "")
      }
      fullWidth={false}
      size={props.size ? props.size : "sm"}
      style={{ fontWeight: "inherit", fontSize: "inherit" }}
      variant={props.varient}
    >
      {props.name}
    </Button>
  );
};

export { OverlayButton };
