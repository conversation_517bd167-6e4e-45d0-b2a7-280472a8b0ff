import React, { useState, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Arrow from "./icons/SmallArrowRight";
import { But<PERSON> } from "@nextui-org/react";

const sliderVariants = {
  incoming: (direction: number) => ({
    x: direction > 0 ? "100%" : "-100%",
    // scale: 1.2,
    // opacity: 0,
  }),
  active: { x: 0, scale: 1, opacity: 1 },
  exit: (direction: number) => ({
    x: direction > 0 ? "-100%" : "100%",
    // scale: 1,
    // opacity: 0.2,
  }),
};

const sliderTransition = {
  duration: 1,
  ease: [0.56, 0.03, 0.12, 1.04],
};


interface Image {
  imageSrc: string;
}

interface AppProps {
  images: Image[];
  setActive?: (index: number) => void;
}

const wrap = (min: number, max: number, value: number): number => {
  const rangeSize = max - min;
  return ((((value - min) % rangeSize) + rangeSize) % rangeSize) + min;
};

const Carousel: React.FC<AppProps> = ({ images , setActive }) => {
  const [[imageCount, direction], setImageCount] = useState<[number, number]>([
    0, 0,
  ]);

  const activeImageIndex = wrap(0, images.length, imageCount);

  const swipeToImage = useCallback(
    (swipeDirection: number) => {
      setImageCount([imageCount + swipeDirection, swipeDirection]);
      if (setActive) setActive(wrap(0, images.length, imageCount + swipeDirection));
    },
    [imageCount]
  );

//   const dragEndHandler = useCallback(
//     (dragInfo: DragInfo) => {
//       const draggedDistance = dragInfo.offset.x;
//       const swipeThreshold = 50;
//       if (draggedDistance > swipeThreshold) {
//         swipeToImage(-1);
//       } else if (draggedDistance < -swipeThreshold) {
//         swipeToImage(1);
//       }
//     },
//     [swipeToImage]
//   );

  const skipToImage = useCallback(
    (imageId: number) => {
      let changeDirection: number;
      if (imageId > activeImageIndex) {
        changeDirection = 1;
      } else if (imageId < activeImageIndex) {
        changeDirection = -1;
      } else {
        return;
      }
      setImageCount([imageId, changeDirection]);
      if (setActive) setActive(imageId);
    },
    [activeImageIndex]
  );

  return (
    <div className="relative overflow-hidden w-full h-96 bg-white">
      <AnimatePresence initial={false} custom={direction}>
        <motion.div
          key={imageCount}
          style={{
            backgroundImage: `url(${images[activeImageIndex].imageSrc})`,
          }}
          custom={direction}
          variants={sliderVariants}
          initial="incoming"
          animate="active"
          exit="exit"
          transition={sliderTransition}
        //   drag="x"
        //   dragConstraints={{ left: 0, right: 0 }}
        //   dragElastic={1}
        //   onDragEnd={(_, dragInfo) => dragEndHandler(dragInfo)}
          className="absolute h-full w-full bg-cover bg-center"
        />
      </AnimatePresence>
      <div className="flex absolute top-1/2 left-0 w-full px-unit-lg">
        <Button
         isIconOnly
         disableRipple
         variant="light"
         onClick={() => swipeToImage(-1)}
         startContent={<Arrow className="rotate-180"/>}
         ></Button>
        <div className="flex-1"></div>
        <Button
         isIconOnly
         disableRipple
         variant="light"

         onClick={() => swipeToImage(1)}
         startContent={<Arrow />}
            ></Button>
      </div>
      <div className="flex justify-center absolute bottom-unit-lg left-1/2">
        {images.map((image: Image , i) => (
          <div
            key={i + "carousel"}
            onClick={() => skipToImage(i)}
            className={`relative hover:cursor-pointer mr-2 last:mr-0 w-2 h-2 rounded-full ${activeImageIndex === i ? "bg-primary" : "bg-default-foreground"}`}
          >
          </div>
        ))}
      </div>
    </div>
  );
};

export default Carousel;
