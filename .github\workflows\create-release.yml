on:
  push:
    # Sequence of patterns matched against refs/tags
    tags:
      - 'v*' # Push events to matching v*, i.e. v1.0, v20.15.10

name: Create Release

jobs:
  build:
    name: Create Release
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Install Node
        uses: actions/setup-node@v3
        with:
          node-version: 22
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Build
        run: npm run build && npm pack && npm run build-iife
      - name: Deploy
        env:
          RCLONE_CONFIG_S3CONN_ACCESS_KEY_ID: ${{ secrets.S3_ACCESS_KEY_ID }}
          RCLONE_CONFIG_S3CONN_SECRET_ACCESS_KEY: ${{ secrets.S3_SECRET_ACCESS_KEY }}
          RCLONE_CONFIG_S3CONN_ENDPOINT: ${{ secrets.S3_ENDPOINT }}
        run: npm run deploy ${{ github.ref_name }}
