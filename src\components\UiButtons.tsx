import { FC, CSSProperties, useState, useEffect, useCallback, useRef } from "react";
import { CameraViewPlugin, FullScreenPlugin, GLTFAnimationPlugin, InteractionPromptPlugin, OrbitControls3, PickingPlugin, ViewerApp } from "webgi";
import { OverlayButton } from "./OverlayButton";
import { useProject } from "./provider/ProjectProvider";
import { Popover, PopoverTrigger, PopoverContent, Button, Tooltip } from "@nextui-org/react";
import FullScreenIcon from "./icons/FullScreen";
import PLayCameraViewsIcon from "./icons/PlayCameraViews";
import PlayIcon from "./icons/Play";
import ResetIcon from "./icons/Reset";
import Rotate360 from "./icons/Rotate360";
import SettingsIcon from "./icons/Settings";
import FitScreenIcon from "./icons/FitScreen";

interface OverlayTabsProps {
  cameraConfig: any;
  viewer?: ViewerApp;
  className?: string;
  hideQuality?: boolean;
  enableZoom?: boolean;
  showShareButton: boolean;
}

const UiButtons: FC<OverlayTabsProps> = (props) => {
  const [rotating, setRotating] = useState(false);
  const [isQualityOpen, setIsQualityOpen] = useState(false);
  const [quality, setQuality] = useState(0);
  const { project } = useProject();
  const [direction, setDirection] = useState<"top-end" | "bottom" | "left-start">("bottom");
  const [looping, setLooping] = useState(false);
  const [isPlayingAnimations, setIsPlayingAnimations] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [origianlZoom, setOriginalZoom] = useState(true);
  const qualityBtn = useRef<HTMLButtonElement>(null);

  useEffect(() => {}, []);

  useEffect(() => {
    setIsQualityOpen(false);
    setLooping(false);
    setIsPlayingAnimations(false);
  }, [project]);

  useEffect(() => {
    if (!props.viewer) return;
    if (window.innerWidth < window.innerHeight) {
      // if (project.name) {
      //   setDirection("top-end");
      // } else {
      setDirection("left-start");
      // }
      // setDirection("bottom");
    } else {
      setDirection("bottom");
      ``;
    }

    const queryParams = new URLSearchParams(window.location.search);
    const quality = queryParams.get("quality");
    if (quality) {
      if (quality === "medium") setQuality(1);
      else if (quality === "high") setQuality(1.5);
      else if (quality === "ultra") setQuality(2);
    } else {
      const dp = props.viewer.renderer.displayCanvasScaling;
      if (dp >= 1 && dp <= 1.5) setQuality(1);
      else if (dp > 1.5 && dp <= 2) setQuality(1.5);
      else if (dp > 2) setQuality(2);
    }

    const controls = props.viewer.scene.activeCamera.getControls<OrbitControls3>()!;
    props.viewer.getPluginByType<FullScreenPlugin>("FullScreenPlugin")?.addEventListener("exit", () => {
      setIsFullScreen(false);
      controls.enableZoom = props.enableZoom ?? true;
      if ((controls.domElement as any).disableWheelListener) {
        controls.domElement.addEventListener("wheel", (controls.domElement as any).disableWheelListener, true);
      }
    });
    props.viewer.getPluginByType<FullScreenPlugin>("FullScreenPlugin")?.addEventListener("enter", () => {
      setIsFullScreen(true);
      controls.enableZoom = true;

      if ((controls.domElement as any).disableWheelListener) {
        controls.domElement.removeEventListener("wheel", (controls.domElement as any).disableWheelListener, true);
      }
    });
  }, [props.viewer]);

  useEffect(() => {
    if (!props.viewer || !props.cameraConfig) return;
    setRotating(props.cameraConfig.camControls.autoRotate);
  }, [props.cameraConfig, props.viewer]);

  const autoRotate = useCallback(() => {
    if (!props.viewer) return;

    let p = props.viewer.getPlugin(InteractionPromptPlugin)!;
    p.stopAnimation();

    if (looping) {
      // setLooping(false);
      loopCameraViews();
    }
    const controls = props.viewer.scene.activeCamera.controls;
    if (!controls) return;
    controls.enabled = true;
    if (controls.autoRotate === undefined) return;
    controls.autoRotate = !controls.autoRotate;
    setRotating(controls.autoRotate);
  }, [props.viewer, looping]);

  const share = () => {
    //if phone then use navigator.share
    if (navigator.share) {
      navigator.share({
        title: "Share this model",
        url: window.location.href,
      });
      return;
    }
    //else use navigator.clipboard.writeText
    navigator.clipboard.writeText(window.location.href);
    // toast.success("Copied to clipboard");
  };

  const fullscreen = useCallback(() => {
    if (!props.viewer) return;
    const fullScreenPlugin = props.viewer.getPluginByType<FullScreenPlugin>("FullScreenPlugin");

    fullScreenPlugin?.toggle(document.getElementById("canvas-wrapper")!);
  }, [props.viewer, origianlZoom]);

  const resetView = () => {
    if (!props.viewer || !props.cameraConfig) return;
    props.viewer.scene.activeCamera.fromJSON(props.cameraConfig);
    setRotating(props.viewer.scene.activeCamera.controls?.autoRotate!);
  };

  useEffect(() => {
    if (!props.viewer) return;
    //add check for mobile devices
    props.viewer.renderer.displayCanvasScaling = quality;
  }, [quality]);

  const isFullScreenSupported = useCallback(() => {
    return (
      document.fullscreenEnabled || (document as any).webkitFullscreenEnabled || (document as any).mozFullScreenEnabled || (document as any).msFullscreenEnabled
    );
  }, []);

  const loopCameraViews = useCallback(() => {
    if (!props.viewer) return;

    let p = props.viewer.getPlugin(InteractionPromptPlugin)!;
    p.stopAnimation();

    let cameraViews = props.viewer.getPluginByType<CameraViewPlugin>("CameraViews");
    if (!cameraViews) return;
    if (rotating) {
      autoRotate();
    }
    if (looping) {
      cameraViews?.stopAllAnimations();
      setLooping(false);
      return;
    }
    cameraViews!.viewLooping = true;
    cameraViews!.animateAllViews();
    setLooping(true);
  }, [props.viewer, rotating, looping]);

  const playAnimation = async () => {
    if (!props.viewer) return;
    const animPlugin = props.viewer.getPluginByType<GLTFAnimationPlugin>("GLTFAnimation");
    if (!animPlugin) return;
    if (!isPlayingAnimations) {
      animPlugin.playAnimation();
      setIsPlayingAnimations(true);
    } else {
      animPlugin.stopAnimation();
      setIsPlayingAnimations(false);
    }
  };

  const fitScene = useCallback(() => {
    if (!props.viewer) return;
    const picking = props.viewer.getPlugin(PickingPlugin)
    let selected = picking?.getSelectedObject?.()
    props.viewer.fitToView(selected);
  } , [props.viewer]);

  const isCameraViews = () => {
    if (!props.viewer) return;
    let cameraViews = props.viewer.getPluginByType<CameraViewPlugin>("CameraViews");
    if (!cameraViews) return;
    return cameraViews.camViews.length > 0;
  };

  const isGltfAnimations = useCallback(() => {
    if (!props.viewer) return;
    let animPlugin = props.viewer.getPluginByType<GLTFAnimationPlugin>("GLTFAnimation");
    if (!animPlugin) return;
    return animPlugin.animations.length > 0;
  }, [props.viewer]);

  return (
    <div
      className={`flex flex-col max-h-[calc(100%-9.5*theme(spacing.unit-xl))] items-center justify-start absolute md:top-1/2 top-unit-4xl right-unit-xl md:-translate-y-1/2 overflow-y-auto
         p-unit-sm bg-white/60 rounded-full gap-y-unit-md py-unit-md mb-unit-xl scale-85 md:scale-100 select-none`}
         style={{zIndex: 9}}
    >
      {!props.hideQuality && (
        <Popover onClick={() => setIsQualityOpen(!isQualityOpen)} placement={direction} isOpen={isQualityOpen} onOpenChange={setIsQualityOpen}>
          <PopoverTrigger>
            <div>
              <Tooltip placement="left" offset={20} delay={500} content={"Quality"}>
                {/* <Button
                  ref={qualityBtn}
                  disableRipple
                  onClick={() => setIsQualityOpen(!isQualityOpen)}
                  endContent={<Icon src={settingsIcon} className="h-full" />}
                  className={`w-10 h-10 p-unit-lg rounded-full  ${isQualityOpen ? "bg-primary" : "bg-transparent"}`}
                ></Button> */}
                <Button
                  variant={isQualityOpen ? "solid" : "light"}
                  onClick={() => setIsQualityOpen(!isQualityOpen)}
                  isIconOnly
                  size="lg"
                  className="p-4 justify-center"
                  startContent={
                    <div>
                      <SettingsIcon />
                    </div>
                  }
                  color={isQualityOpen ? "primary" : "default"}
                />
              </Tooltip>
            </div>
          </PopoverTrigger>
          <PopoverContent onClick={(e) => e.preventDefault()} className="p-unit-lg w-fit md:m-unit-sm rounded-small ">
            <div className="w-fit flex gap-unit-md">
              {qualities.map((q, i) => (
                <Button
                  disableRipple
                  key={i}
                  onClick={() => {
                    setQuality(q.value);
                  }}
                  color={quality === q.value ? "primary" : "default"}
                  variant="solid"
                  className="px-unit-xl text-tiny font-semibold rounded-md"
                  fullWidth={false}
                >
                  {q.name}
                </Button>
              ))}
            </div>
          </PopoverContent>
        </Popover>
      )}
      {props.cameraConfig && (
        <Tooltip placement={"left"} delay={500} offset={20} content={"Reset View"}>
          <div id="rest">
            {/* <OverlayButton onClick={resetView} color="default" startIcon={resetIcon}></OverlayButton> */}
            <Button
              className="p-4"
              variant={"light"}
              onClick={resetView}
              isIconOnly
              size="lg"
              startContent={
                <div>
                  <ResetIcon className="w-[18px] h-[18px]" />
                </div>
              }
            />
          </div>
        </Tooltip>
      )}
      <Tooltip placement={"left"} delay={500} offset={20} key={"rotate"} content="Rotate Camera">
        <div id="rotate">
          {/* <OverlayButton onClick={autoRotate} highlighted={rotating} startIcon={rotateIcon}></OverlayButton> */}
          <Button
            variant={rotating ? "solid" : "light"}
            onClick={autoRotate}
            isIconOnly
            size="lg"
            className="p-4"
            startContent={
              <div>
                <Rotate360 />
              </div>
            }
            color={rotating ? "primary" : "default"}
          />
        </div>
      </Tooltip>

      {isCameraViews() && (
        <Tooltip placement={"left"} delay={500} offset={20} content="Play Camera Views">
          <div id="loop">
            {/* <OverlayButton onClick={loopCameraViews} highlighted={looping} startIcon={playViewsIcon}></OverlayButton> */}
            <Button
              onClick={loopCameraViews}
              variant={looping ? "solid" : "light"}
              isIconOnly
              size="lg"
              className="p-4"
              startContent={
                <div>
                  <PLayCameraViewsIcon />
                </div>
              }
              color={looping ? "primary" : "default"}
            />
          </div>
        </Tooltip>
      )}

      {isGltfAnimations() && (
        <Tooltip placement={"left"} delay={500} offset={20} content="Play Animations">
          <Button
            onClick={playAnimation}
            variant={isPlayingAnimations ? "solid" : "light"}
            isIconOnly
            size="lg"
            className="p-4"
            startContent={
              <div>
                <PlayIcon />
              </div>
            }
            color={isPlayingAnimations ? "primary" : "default"}
          />
        </Tooltip>
      )}
      <Tooltip placement={"left"} delay={500} offset={20} content="Fit Object/Scene">
        <Button
          onClick={fitScene}
          variant={"light"}
          isIconOnly
          size="lg"
          className="p-4"
          startContent={<div>{<FitScreenIcon />}</div>}
          color={"default"}
        />
      </Tooltip>
      {isFullScreenSupported() && (
        <Tooltip placement={"left"} delay={500} offset={20} content="Fullscreen">
          <Button
            onClick={fullscreen}
            variant={isFullScreen ? "solid" : "light"}
            isIconOnly
            size="lg"
            className="p-4"
            startContent={<div>{<FullScreenIcon />}</div>}
            color={isFullScreen ? "primary" : "default"}
          />
        </Tooltip>
      )}
     {/* {props.showShareButton && <Tooltip placement={"left"} delay={500} offset={20} content="Share">
        <Button
          onClick={share}
          variant={"light"}
          isIconOnly
          size="lg"
          startContent={
            <div>
              <ShareIcon />
            </div>
          }
          color={"default"}
          className="flex md:hidden p-4"
        />
      </Tooltip>} */}
    </div>
  );
};

const qualities = [
  { name: "Medium", value: 1 },
  { name: "High", value: 1.5 },
  { name: "Ultra", value: 2 },
];

export { UiButtons };
