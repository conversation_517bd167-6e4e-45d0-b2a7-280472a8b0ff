import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import {
  $getRoot,
  $createParagraphNode,
  $createTextNode,
  COMMAND_PRIORITY_EDITOR,
  createCommand,
} from "lexical";
import { useEffect } from "react";

export const ADD_CHILD_NODE_COMMAND = createCommand();

export default function CommandExecutor() {
  const [editor] = useLexicalComposerContext();

  // Register the custom command
  useEffect(() => {
    return editor.registerCommand(
      ADD_CHILD_NODE_COMMAND,
      (payload: any) => {
        editor.update(() => {
          const root = $getRoot();

          // Create a new paragraph node
          const newParagraph = $createParagraphNode();

          // Append a text node with some content (payload can be the text content)
          newParagraph.append($createTextNode(payload));

          // Append the paragraph node to the root
          root.append(newParagraph);
        });
        return true;
      },
      COMMAND_PRIORITY_EDITOR
    );
  }, [editor]);

  return null;
}
