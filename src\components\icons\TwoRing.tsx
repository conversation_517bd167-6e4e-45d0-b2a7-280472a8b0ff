import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg viewBox="0 0 26 25" xmlns="http://www.w3.org/2000/svg" fill="currentColor" {...props}>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.9977 3.11153L16.5388 5.09653C12.8621 5.24129 9.43321 7.62602 8.11267 11.3725C6.99037 14.5566 7.66178 17.9647 9.579 20.4404C9.904 20.7656 10.4189 20.8282 10.8155 20.5632C11.2185 20.294 11.3644 19.7767 11.1865 19.34C9.59445 17.3781 9.02985 14.6278 9.94408 12.034C11.3211 8.12721 15.5264 6.10778 19.3212 7.48913C22.0003 8.46436 23.7784 10.8811 24.1079 13.5927C24.2901 14.0084 24.735 14.2583 25.1943 14.1703C25.6812 14.0769 26.0169 13.6356 25.9993 13.1472C25.677 10.9416 24.5966 8.89779 22.9463 7.41862L24.6445 6.20742C24.9294 5.99072 25.1146 5.62404 25.0254 5.27117C25.0432 5.22079 24.8724 2.84867 24.8724 2.84867C24.8505 2.46346 24.6055 2.10554 24.2314 1.96937L18.9764 0.0564714C18.6023 -0.0796957 18.1894 0.0387061 17.9318 0.322193L16.3496 2.11308C16.3496 2.11308 15.9018 2.3738 15.9977 3.11153ZM25.2986 18.3709C23.7235 21.9798 20.1168 24.153 16.3863 23.9916L16.7566 23.9786C16.7819 23.9787 16.8075 23.9778 16.8332 23.9759L16.7566 23.9786C16.2448 23.9766 15.8129 23.574 15.7751 23.0466C15.7417 22.5802 16.0271 22.165 16.4437 22.0217C19.4048 22.1716 22.2555 20.4603 23.5154 17.5848C23.7725 17.1988 24.2689 17.0354 24.7099 17.2196C25.1631 17.4089 25.4038 17.901 25.2986 18.3709ZM22.9965 3.66961L23.0794 4.93486C23.0794 4.93486 21.2829 6.20848 21.2629 6.22188C20.8545 5.99568 20.4257 5.79826 19.975 5.63421C19.5424 5.47671 19.1074 5.35456 18.6702 5.26775L18.1083 3.12532L18.9628 2.2013L22.9965 3.66961ZM9.48558 5.08747L10.0107 3.09804C10.1007 2.35954 9.65075 2.10254 9.65075 2.10254L8.05422 0.324873C7.79439 0.0435362 7.3805 -0.071429 7.00754 0.0678429L1.76809 2.02436C1.39513 2.16363 1.15296 2.52358 1.13419 2.90895C1.13419 2.90895 0.982546 5.28242 1.00071 5.33265C0.914353 5.68624 1.10248 6.05137 1.38909 6.2657L3.09698 7.46274C0.350196 9.96562 -0.781926 14.0424 0.572028 17.787C2.34042 22.6777 7.71088 25.2022 12.4785 23.4219C17.2461 21.6416 19.7304 16.1839 17.962 11.2932C17.5871 10.2563 17.0505 9.32571 16.3923 8.51982C16.0623 8.23683 15.5752 8.19822 15.2001 8.45653C14.7845 8.74279 14.6511 9.29402 14.8704 9.73808C15.4007 10.3839 15.8333 11.1328 16.136 11.9699C17.546 15.8697 15.5962 20.1534 11.8081 21.5679C8.0201 22.9825 3.80319 20.9964 2.39475 17.1011C0.986306 13.2059 2.93901 8.91598 6.72256 7.50312C7.83301 7.08846 8.98118 6.96643 10.0849 7.09946C10.5158 7.06414 10.8886 6.7423 10.9754 6.28994C11.0694 5.80021 10.7962 5.32424 10.3506 5.1559C10.0643 5.11943 9.77564 5.09648 9.48558 5.08747ZM2.94396 4.98018L3.01666 3.71428L7.03837 2.21249L7.90032 3.12937L7.35569 5.2764C6.91917 5.36684 6.48522 5.49261 6.05384 5.65369C5.60449 5.82149 5.17733 6.02246 4.77072 6.25205C4.75063 6.23881 2.94396 4.98018 2.94396 4.98018Z"
    />
  </svg>
);
export default SvgComponent;
