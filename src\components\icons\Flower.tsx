import * as React from "react"
import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    viewBox="0 0 30 30"
    fill="none"
    {...props}
  >
    <path
      fill="currentColor"
      d="M26.932 15.221a3.215 3.215 0 0 0-.458-.573c.204-.209.407-.47.56-.73.382-.679.56-1.487.458-2.243-.127-.861-.483-1.67-1.018-2.243a2.79 2.79 0 0 0-1.069-.756c.051-.444 0-.887-.102-1.33A4.076 4.076 0 0 0 23.93 5.31c-.637-.521-1.451-.756-2.291-.73-.306 0-.535.052-.79.104a4.108 4.108 0 0 0-.356-.86 3.305 3.305 0 0 0-1.68-1.513 3.89 3.89 0 0 0-2.418-.208c-.432.104-.84.286-1.17.573a3.79 3.79 0 0 0-1.172-.573 3.89 3.89 0 0 0-2.418.208c-.764.313-1.374.913-1.756 1.695a2.858 2.858 0 0 0-.255.705 3.505 3.505 0 0 0-.89-.13c-.764 0-1.502.234-2.088.703l-.025.026c-.662.522-1.171 1.252-1.375 2.035a3.372 3.372 0 0 0-.102 1.33 3.49 3.49 0 0 0-1.069.756c-.56.574-.916 1.382-1.018 2.243-.102.834.076 1.669.535 2.399.127.209.28.391.458.574-.204.208-.408.47-.56.73a3.525 3.525 0 0 0-.458 2.243c.127.86.483 1.669 1.018 2.242.305.34.662.6 1.069.757-.051.443 0 .886.102 1.33.229.782.712 1.512 1.374 2.034.637.522 1.451.756 2.291.73.23-.026.484-.052.713-.104.076.287.203.574.356.86a3.305 3.305 0 0 0 1.68 1.513c.79.313 1.63.391 2.418.209.433-.105.84-.313 1.171-.574.357.26.738.47 1.171.574.28.078.586.104.866.104.509 0 1.043-.104 1.552-.313.764-.313 1.375-.913 1.757-1.695a3.09 3.09 0 0 0 .254-.678c.306.078.586.13.891.13.764 0 1.502-.234 2.088-.704l.05-.026a4.076 4.076 0 0 0 1.375-2.034c.127-.443.153-.887.102-1.33a3.49 3.49 0 0 0 1.069-.756c.56-.574.916-1.383 1.018-2.243.178-.835-.026-1.695-.484-2.426Zm-3.334 4.173c-.153.183-.23.417-.23.652 0 .13.052.235.103.34.152.338.178.703.076 1.068a2.219 2.219 0 0 1-.79 1.122c-.28.208-.636.313-.992.313-.28 0-.585-.079-.84-.209a14.954 14.954 0 0 0-.967-3.182 9.925 9.925 0 0 0-1.095-1.93c.026-.026.051-.078.077-.104.229.052.534.157.789.34.483.286.789.625.865.808a.877.877 0 0 0 1.146.521.912.912 0 0 0 .509-1.173c-.23-.652-.84-1.278-1.63-1.748a4.758 4.758 0 0 0-.916-.417c.077-.365.128-.73.153-1.095a9.044 9.044 0 0 1 1.68 0c1.17.13 2.19.443 2.85.678.434.156.816.47 1.044.834.23.366.332.757.28 1.148-.076.495-.254.913-.56 1.226a1.375 1.375 0 0 1-.941.469.968.968 0 0 0-.611.34Zm-4.582 5.086c-.178.365-.484.678-.84.808-.433.183-.916.235-1.324.13a1.373 1.373 0 0 1-.865-.6.885.885 0 0 0-1.476 0 1.373 1.373 0 0 1-.866.6c-.407.105-.865.053-1.323-.13a1.547 1.547 0 0 1-.79-.73 1.987 1.987 0 0 1-.254-.86c.61-.523 1.45-1.357 2.214-2.452.458-.652.815-1.304 1.095-1.93.05 0 .076 0 .127.026.076.235.127.548.127.86 0 .574-.127 1.018-.254 1.174a.9.9 0 0 0-.204.678c.026.235.128.47.331.626a.851.851 0 0 0 .662.209.864.864 0 0 0 .61-.34c.434-.52.663-1.381.663-2.32a4.3 4.3 0 0 0-.102-1.017c.356-.105.687-.235.993-.418.305.444.585.94.84 1.487.483 1.095.738 2.164.84 2.869.05.469 0 .938-.204 1.33Zm-8.96-2.087c-.05.052-.102.078-.127.104l-.026.027c-.33.234-.687.365-1.043.39a1.688 1.688 0 0 1-1.12-.338c-.382-.313-.662-.704-.764-1.096a1.545 1.545 0 0 1 .076-1.069.942.942 0 0 0-.432-1.226.665.665 0 0 0-.306-.078 1.376 1.376 0 0 1-.942-.47c-.28-.312-.483-.756-.56-1.225-.05-.365.026-.73.23-1.07.152-.26.356-.469.61-.651.739.26 1.859.6 3.182.73.764.078 1.502.078 2.19 0 .025.052.025.078.05.13-.152.183-.407.366-.687.548-.484.287-.916.391-1.12.365a1.004 1.004 0 0 0-.662.157.825.825 0 0 0-.356.6c-.026.234.025.469.152.678a.8.8 0 0 0 .586.365c.662.104 1.502-.105 2.29-.574.306-.183.56-.365.815-.6.255.26.56.496.866.678-.204.496-.51.991-.84 1.487a18.035 18.035 0 0 1-2.062 2.138ZM6.9 9.901c.152-.182.229-.417.229-.652 0-.13-.051-.234-.102-.339a1.546 1.546 0 0 1-.076-1.069A2.22 2.22 0 0 1 7.74 6.72c.28-.209.636-.313.992-.313.28 0 .586.078.84.208.153.809.433 1.956.968 3.182.305.704.687 1.356 1.094 1.93-.025.026-.05.078-.076.104a2.466 2.466 0 0 1-.79-.339c-.483-.287-.788-.626-.865-.808a.877.877 0 0 0-1.145-.522.912.912 0 0 0-.51 1.174c.23.652.84 1.277 1.63 1.747.305.182.61.313.916.417a8.08 8.08 0 0 0-.152 1.095 9.027 9.027 0 0 1-1.68 0c-1.171-.13-2.19-.443-2.851-.678a2.106 2.106 0 0 1-1.044-.834c-.23-.365-.331-.756-.28-1.148.076-.495.254-.912.56-1.225.254-.287.585-.444.942-.47a.969.969 0 0 0 .61-.339Zm4.582-5.085c.178-.365.483-.678.84-.809.28-.13.585-.182.865-.182.153 0 .305.026.458.052.357.078.662.287.866.6a.869.869 0 0 0 1.221.26.987.987 0 0 0 .23-.234c.203-.313.509-.522.865-.6.407-.104.865-.052 1.323.13.332.13.612.392.79.73.152.261.229.548.254.861-.61.522-1.45 1.356-2.214 2.452a11.56 11.56 0 0 0-1.095 1.93c-.05 0-.076 0-.127-.027a2.91 2.91 0 0 1-.127-.86c0-.574.127-1.017.254-1.174a.9.9 0 0 0 .204-.678.897.897 0 0 0-.331-.626.851.851 0 0 0-.662-.208.864.864 0 0 0-.61.339c-.434.521-.663 1.382-.663 2.32 0 .366.026.705.102 1.018a4.22 4.22 0 0 0-.993.417 10.165 10.165 0 0 1-.84-1.486 11.487 11.487 0 0 1-.84-2.87c-.025-.468.026-.938.23-1.355Zm8.96 2.112c.05-.052.101-.078.152-.13l.026-.026c.33-.235.687-.365 1.043-.391.407-.027.815.104 1.12.339.382.313.662.704.764 1.095.102.365.076.73-.076 1.07a.942.942 0 0 0 .432 1.225.664.664 0 0 0 .306.078c.356.026.687.183.942.47.305.313.483.756.56 1.225.05.365-.026.73-.23 1.07-.152.26-.356.469-.61.652-.739-.261-1.859-.6-3.182-.73-.382-.027-.738-.053-1.095-.053-.382 0-.738.026-1.094.052-.026-.052-.026-.078-.051-.13.152-.183.407-.365.687-.548.484-.287.916-.391 1.12-.365a.894.894 0 0 0 1.018-.756 1.07 1.07 0 0 0-.152-.678.799.799 0 0 0-.586-.365c-.662-.105-1.502.104-2.29.573-.306.183-.56.392-.816.6a4.348 4.348 0 0 0-.865-.678c.204-.495.51-.99.84-1.486.738-.913 1.502-1.67 2.037-2.113Zm-5.193 4.825c.738 0 1.45.287 1.985.834l.026.027c.178.182.33.365.458.6.254.443.382.938.382 1.434 0 .521-.128 1.017-.382 1.434a3.38 3.38 0 0 1-.458.6 2.798 2.798 0 0 1-2.011.86 2.881 2.881 0 0 1-2.011-.834 2.257 2.257 0 0 1-.433-.574s0-.026-.025-.026l-.026-.026c-.229-.443-.382-.939-.382-1.434 0-.783.306-1.513.815-2.06.585-.522 1.298-.835 2.062-.835Z"
    />
  </svg>
)
export default SvgComponent
