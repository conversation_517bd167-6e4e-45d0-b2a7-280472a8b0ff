<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="https://playground.ijewel3d.com/logo.svg" />
    <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0, initial-scale=1.0" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/gh/repalash/gilroy-free-webfont@master/Gilroy-Extrabold.css" rel="stylesheet" />
    <title>iJewel3D Playground</title>
    <meta name="description" content="iJewel3D Playground - An interactive 3D jewelry design tool." />
    <meta name="keywords" content="3D, jewelry, design, interactive, rendering, iJewel3D, three.js, webgi, pixotronics" />

    <style>
      :root {
        font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
        line-height: 1.5;
        font-weight: 400;

        font-synthesis: none;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      body {
        overflow: hidden;
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
        background-color: white;
        display: flex;
        place-items: center;
        min-width: 320px;
        min-height: 100vh;
      }

      #overlayContainer {
        width: 100%;
        height: 100vh;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        place-items: center;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        text-align: center;
        z-index: 100000;
        background: white;
        transition: opacity ease 500ms;
        position: absolute;
      }

      .logo {
        height: 6em;
        padding-left: 4rem;
        will-change: filter;
        transition: filter 300ms;
      }
      .logo:hover {
        filter: drop-shadow(0 0 2em #646cffaa);
      }

      .card {
        padding: 2em;
        color: #222;
      }
    </style>
  </head>
  <body>
    <noscript>
      <div>
        <h1>iJewel3D Playground</h1>
        <p>
          Welcome to the iJewel3D Playground. This interactive tool allows you to design 3D jewelry. Please enable JavaScript to use the full functionality of
          the site.
        </p>
      </div>
    </noscript>

    <div id="overlayContainer">
      <div>
        <a href="https://ijewel3d.com" target="_blank" style="display: block">
          <img src="https://playground.ijewel3d.com/logo_black.svg" class="logo" alt="iJewel3D Playground" />
        </a>
        <div class="card">Loading Playground</div>
        <span class="loader"></span>
      </div>
    </div>
    <div id="root" style="width: 100vw; height: 100vh; display: flex">
      <!-- <canvas id="mcanvas"></canvas> -->
    </div>

    <!-- <script type="module" src="/src/editor.ts"></script> -->
  </body>
  <script>
    window.addEventListener("DOMContentLoaded", async function () {
      const assets = await fetch("https://playground.ijewel3d.com/v2/assets.json").then((res) => res.json());
      ijewelEditor.setupIjewelDriveEditor2({ baseName: "packs" , assets, editorOptions : {
        logo : "https://playground.ijewel3d.com/playground-logo.svg",
        diamondKey : "3J94488TR7QP2AWMRSCB72STE3NX8DT7-X5W9H3M2X8",
      }});

      await window.timeout(400);
      document.getElementById("overlayContainer").style.opacity = "0";
      await window.timeout(1000);
      document.getElementById("overlayContainer").style.display = "none";

    });
      
  </script>

  <script src="https://dist.pixotronics.com/webgi/runtime/bundle-0.9.20-dev.5.js"></script>
  <script src="https://releases.ijewel3d.com/libs/mini-editor/0.0.17-dev.14/bundle.iife.js"></script>
</html>
