import { <PERSON><PERSON>, <PERSON> } from "@nextui-org/react";
import Close from "./icons/Close";
import OpenNewTab from "./icons/OpenNewTab";
import { Modal } from "./Modal";
import { useEffect, useState } from "react";

import {
  TonemapPlugin,
  BloomPlugin,
  ViewerApp,
  createDiv,
  CameraUiPlugin,
  DiamondPlugin,
  HierarchyUiPlugin,
  RendererUiPlugin,
  SSAOPlugin,
  CanvasSnipperPlugin,
  CanvasRecorderPlugin,
  VignettePlugin,
  AssetExporterPlugin,
  DropzonePlugin,
  SSRPlugin,
  DepthOfFieldPlugin,
} from "webgi";

const addParamsToYoutubeURL = (url: string) => {
  const urlObj = new URL(url);
  urlObj.searchParams.append("autoplay", "1");
  return urlObj.toString();
};

// List all the plugin here
const videoHintsForPlugin: any = {
  BloomPlugin: {
    src: "https://www.youtube.com/embed/Q22V_lYqJug?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Bloom Controls",
    plugin: BloomPlugin,
  },
  CameraUiPlugin1: {
    src: "https://www.youtube.com/embed/nCMbNF9rI5A?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Camera Controls",
    plugin: CameraUiPlugin,
  },
  CameraUiPlugin2: {
    src: "https://www.youtube.com/embed/6wvSWjEiOnM?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Orbit Controls",
    plugin: CameraUiPlugin,
    buttonOrder: 1,
  },
  DepthOfFieldPlugin: {
    src: "https://www.youtube.com/embed/gu4yvDrbaUQ?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Depth of Field Controls",
    plugin: DepthOfFieldPlugin,
  },
  DiamondPlugin: {
    src: "https://www.youtube.com/embed/5gP0vCHrnxA?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Diamond Material Controls",
    plugin: DiamondPlugin,
  },
  HierarchyUiPlugin: {
    src: "https://www.youtube.com/embed/2GOSeyuu8Mw?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Hierarchy Controls",
    plugin: HierarchyUiPlugin,
  },
  RendererUiPlugin: {
    src: "https://www.youtube.com/embed/EqlGBlyNU6w?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Renderer Controls",
    plugin: RendererUiPlugin,
  },
  CanvasSnipperPlugin: {
    src: "https://www.youtube.com/embed/ypVSKkdxTqU?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Exporting Image Controls",
    plugin: CanvasSnipperPlugin,
  },
  SSAOPlugin: {
    src: "https://www.youtube.com/embed/HPxdQ_9j80U?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "SS Ambient Occlution Controls",
    plugin: SSAOPlugin,
  },
  TonemapPlugin: {
    src: "https://www.youtube.com/embed/xI27xeqOZds?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Tonemapping Controls",
    plugin: TonemapPlugin,
  },
  CanvasRecorderPlugin: {
    src: "https://www.youtube.com/embed/whSZVfjPZNI?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Exporting Videos Controls",
    plugin: CanvasRecorderPlugin,
  },
  VignettePlugin: {
    src: "https://www.youtube.com/embed/_3cCUdLvis8?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Vignette Controls",
    plugin: VignettePlugin,
  },
  AssetExporterPlugin: {
    src: "https://www.youtube.com/embed/4daheAzriUg?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "GBL Exporter Controls",
    plugin: AssetExporterPlugin,
    buttonOrder: 1,
  },
  DropzonePlugin: {
    src: "https://www.youtube.com/embed/SF07FyUEFQg?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Importing 3d files Controls",
    plugin: DropzonePlugin,
  },
  SSRPlugin: {
    src: "https://www.youtube.com/embed/bDfLCLxcpEE?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Screen Space Reflections Controls",
    plugin: SSRPlugin,
  },
};

// List all the main menus here
export const menuVideosHints = {
  //LHS
  Advanced: {
    src: "https://www.youtube.com/embed/xkwO6FmOm9E?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Advance Settings",
  },
  Background: {
    src: "https://www.youtube.com/embed/bYe98wkDQw4?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Background Settings",
  },
  "Gem Env": {
    src: "https://www.youtube.com/embed/EmBh7ghS0Tc?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Gem Environment Settings",
  },
  Environment: {
    src: "https://www.youtube.com/embed/oGcTcQsP_ek?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Metal Environment Settings",
  },
  Ground: {
    src: "https://www.youtube.com/embed/R6LIiA1iLas?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Ground Presets Settings",
  },
  ExportL: {
    src: "https://www.youtube.com/embed/gWIf_gVeYTY?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Export & Compress Settings",
  },
  Gems: {
    src: "https://www.youtube.com/embed/2OMt73nfqg8?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Gem Materials Setting",
  },
  Metals: {
    src: "https://www.youtube.com/embed/ZCI8OXbRliY?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Metal Material Setting",
  },
  Scene: {
    src: "https://www.youtube.com/embed/_7PbnkCimuE?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Scene Presets Settings",
  },
  ModelStage: {
    src: "https://www.youtube.com/embed/xf_XQhNC?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
    modalTitle: "Model Stage Settings",
  },
  // RHS
  // Viewer: {
  //   src: "https://www.youtube.com/embed/6wvSWjEiOnM?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
  //   modalTitle: "Viewer Settings",
  // },
  // Plugins: {
  //   src: "https://www.youtube.com/embed/Q22V_lYqJug?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
  //   modalTitle: "Plugins",
  // },
  // Extras: {
  //   src: "https://www.youtube.com/embed/Q22V_lYqJug?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
  //   modalTitle: "Extras",
  // },
  // Scene: {
  //   src: "https://www.youtube.com/embed/Q22V_lYqJug?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
  //   modalTitle: "Scene",
  // },
  // Picking: {
  //   src: "https://www.youtube.com/embed/Q22V_lYqJug?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
  //   modalTitle: "Picking",
  // },
  // "Anti-aliasing": {
  //   src: "https://www.youtube.com/embed/Q22V_lYqJug?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
  //   modalTitle: "Anti-aliasing",
  // },
  // Modifiers: {
  //   src: "https://www.youtube.com/embed/Q22V_lYqJug?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
  //   modalTitle: "Modifiers",
  // },
  // Animations: {
  //   src: "https://www.youtube.com/embed/Q22V_lYqJug?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
  //   modalTitle: "Animations",
  // },
  // "Post Processing": {
  //   src: "https://www.youtube.com/embed/Q22V_lYqJug?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
  //   modalTitle: "Post Processing",
  // },
  // Configurators: {
  //   src: "https://www.youtube.com/embed/Q22V_lYqJug?list=PLIWKi7kUUJPiQO_JwVwE7ZXVUpdfXq7Gi",
  //   modalTitle: "Configurators",
  // },
};

const initialHint = {
  isOpen: false,
  src: "",
  modalTitle: "",
};
export default function VideoHintsModal(props: {
  viewer?: ViewerApp;
  addPluginHints?: boolean;
  isMenuOpen?: boolean;
  menuType?: keyof typeof menuVideosHints;
  menuVideosHints?: typeof menuVideosHints;
  setMenuVideosHints?: React.Dispatch<React.SetStateAction<any>>;
}) {
  const [currentHint, setCurrentHint] = useState(initialHint);

  const handleOnClose = () => {
    setCurrentHint(initialHint);
    props?.setMenuVideosHints?.({ menuType: null, isMenuOpen: false });
  };

  useEffect(() => {
    if (props.isMenuOpen && props.menuType) {
      const menuHint = menuVideosHints[props.menuType];
      setCurrentHint({
        isOpen: true,
        src: menuHint.src,
        modalTitle: menuHint.modalTitle,
      });
    }
  }, [props.isMenuOpen, props.menuType]);

  useEffect(() => {
    if (props.viewer && props.addPluginHints) {
      Object.values(videoHintsForPlugin).forEach((hint: any) => {
        const plugin = props.viewer?.getPlugin(hint.plugin as any);

        if (!plugin) return;
        const ui = plugin?.uiConfig;

        let tpBtn = ui?.uiRef.controller_.view.element.querySelector(".tp-fldv_t");

        if (hint.buttonOrder !== undefined) {
          const allButtons = ui?.uiRef.controller_.view.element.querySelectorAll(".tp-fldv_b > .tp-fldv_t");
          tpBtn = allButtons[hint.buttonOrder];
        }

        if (hint.src) {
          const btn = createDiv({
            innerHTML: `<svg width="13" height="13" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M4.95825 8.20825H6.04159V4.95825H4.95825V8.20825ZM5.49992 3.87492C5.65339 3.87492 5.78204 3.82301 5.88586 3.71919C5.98968 3.61537 6.04159 3.48672 6.04159 3.33325C6.04159 3.17978 5.98968 3.05113 5.88586 2.94731C5.78204 2.8435 5.65339 2.79159 5.49992 2.79159C5.34645 2.79159 5.2178 2.8435 5.11398 2.94731C5.01016 3.05113 4.95825 3.17978 4.95825 3.33325C4.95825 3.48672 5.01016 3.61537 5.11398 3.71919C5.2178 3.82301 5.34645 3.87492 5.49992 3.87492ZM5.49992 10.9166C4.75061 10.9166 4.04645 10.7744 3.38742 10.49C2.72839 10.2056 2.15513 9.81971 1.66763 9.33221C1.18013 8.84471 0.79419 8.27145 0.509814 7.61242C0.225439 6.95339 0.083252 6.24922 0.083252 5.49992C0.083252 4.75061 0.225439 4.04645 0.509814 3.38742C0.79419 2.72839 1.18013 2.15513 1.66763 1.66763C2.15513 1.18013 2.72839 0.79419 3.38742 0.509814C4.04645 0.225439 4.75061 0.083252 5.49992 0.083252C6.24922 0.083252 6.95339 0.225439 7.61242 0.509814C8.27145 0.79419 8.84471 1.18013 9.33221 1.66763C9.81971 2.15513 10.2056 2.72839 10.49 3.38742C10.7744 4.04645 10.9166 4.75061 10.9166 5.49992C10.9166 6.24922 10.7744 6.95339 10.49 7.61242C10.2056 8.27145 9.81971 8.84471 9.33221 9.33221C8.84471 9.81971 8.27145 10.2056 7.61242 10.49C6.95339 10.7744 6.24922 10.9166 5.49992 10.9166ZM5.49992 9.83325C6.70964 9.83325 7.73429 9.41346 8.57388 8.57388C9.41346 7.73429 9.83325 6.70964 9.83325 5.49992C9.83325 4.2902 9.41346 3.26554 8.57388 2.42596C7.73429 1.58638 6.70964 1.16659 5.49992 1.16659C4.2902 1.16659 3.26554 1.58638 2.42596 2.42596C1.58638 3.26554 1.16659 4.2902 1.16659 5.49992C1.16659 6.70964 1.58638 7.73429 2.42596 8.57388C3.26554 9.41346 4.2902 9.83325 5.49992 9.83325Z" fill="#A7A7A7"/>
                      </svg>
                      `,
            classList: ["help", "ml-2", "cursor-pointer"],
            elementTag: "button",
          });

          btn.onclick = (e) => {
            e.preventDefault();
            setCurrentHint({ ...hint, isOpen: true });
          };
          tpBtn.appendChild(btn);
        }
      });
      return () => {
        Object.values(videoHintsForPlugin).forEach((hint: any) => {
          const plugin = props.viewer?.getPlugin(hint.plugin as any);

          if (!plugin) return;
          const ui = plugin?.uiConfig;

          const buttoms = ui?.uiRef.controller_.view.element.querySelectorAll("button.help");

          buttoms.forEach((btn: any) => {
            if (btn) btn.parentNode.removeChild(btn);
          });
        });
      };
    }
  }, [props.viewer]);

  return (
    <Modal
      radius="none"
      placement="center"
      key={"help"}
      isOpen={currentHint.isOpen}
      onOpenChange={handleOnClose}
      backdrop="blur"
      hideCloseButton
      classNames={{ base: "w-fit w-full h-full sm:w-[66.9%] sm:h-[64.3%] max-w-full max-h-full !m-0 !border-0" }}
    >
      {() => (
        <div className="w-full h-full">
          <div className="flex justify-between items-center bg-[#ffffff] px-3 py-2.5">
            <span>{currentHint.modalTitle}</span>
            <span className="flex gap-3 items-center">
              <Button
                isIconOnly
                className="h-[21px] w-[21px]"
                startContent={<OpenNewTab width={13} height={13} />}
                as={Link}
                href={addParamsToYoutubeURL(currentHint.src)}
                target="__blank"
                variant="light"
              />
              <Button
                className="h-[21px] w-[21px]"
                isIconOnly
                startContent={<Close width={13} height={13} fill="#ACACAC" />}
                onClick={handleOnClose}
                variant="light"
              />
            </span>
          </div>
          <iframe
            width="100%"
            height="calc(100%-44px)"
            className="w-full h-[calc(100%-44px)]"
            src={addParamsToYoutubeURL(currentHint.src)}
            title={currentHint.modalTitle}
            allow="allowfullscreen; accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            // referrerpolicy="strict-origin-when-cross-origin"
            // frameborder="0"
          />
        </div>
      )}
    </Modal>
  );
}
