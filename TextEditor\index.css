/* text editor, container css (start) */
.text-editor-container {
    /* margin: 20px auto 20px auto; */
    /* border-radius: 2px; */
    max-width: 600px;
    color: #000;
    position: relative;
    line-height: 17px;
    font-weight: 400;
    text-align: left;
    background: #f7f7f7;
    border: 1px solid #e7e9e9;
  }
  
  .text-editor-inner {
    position: relative;
  }
  
  .text-editor-input {
    min-height: 150px;
    width: 100%;
    flex-shrink: 0;
    resize: none;
    font-size: 14px;
    caret-color: rgb(5, 5, 5);
    position: relative;
    tab-size: 1;
    outline: 0;
    caret-color: #444;
    padding: 12px;
    padding-top: 0% ;
    border-radius: 16px !important;
    overflow: hidden;
  }
  
  .text-editor-first-line {
    font-family: Inter-semibold !important;
    font-size: 16px !important;
    line-height: 18px;
  }
  
  .text-editor-placeholder {
    overflow: hidden;
    position: absolute;
    text-overflow: ellipsis;
    top: 0px;
    left: 12px;
    font-size: 14px;
    padding-right: 12px;
    user-select: none;
    display: inline-block;
    pointer-events: none;
  }
  
  .text-editor-text-bold {
    font-weight: bold;
  }
  
  .text-editor-text-italic {
    font-style: italic;
  }
  
  .text-editor-text-underline {
    text-decoration: underline;
  }
  
  .text-editor-text-strikethrough {
    text-decoration: line-through;
  }
  
  .text-editor-text-underlineStrikethrough {
    text-decoration: underline line-through;
  }
  
  .text-editor-text-code {
    background-color: rgb(240, 242, 245);
    padding: 1px 0.25rem;
    font-family: Menlo, Consolas, Monaco, monospace;
    font-size: 94%;
  }
  
  .text-editor-link {
    color: rgb(33, 111, 219);
    text-decoration: none;
  }
  
  
  .text-editor-code {
    background-color: rgb(240, 242, 245);
    font-family: Menlo, Consolas, Monaco, monospace;
    display: block;
    padding: 8px 8px 8px 52px;
    line-height: 1.53;
    font-size: 13px;
    margin: 0;
    margin-top: 8px;
    margin-bottom: 8px;
    tab-size: 2;
    /* white-space: pre; */
    overflow-x: auto;
    position: relative;
  }
  
  .text-editor-code:before {
    content: attr(data-gutter);
    position: absolute;
    background-color: #eee;
    left: 0;
    top: 0;
    border-right: 1px solid #ccc;
    padding: 8px;
    color: #777;
    white-space: pre-wrap;
    text-align: right;
    min-width: 25px;
  }
  .text-editor-code:after {
    content: attr(data-highlight-language);
    top: 0;
    right: 3px;
    padding: 3px;
    font-size: 10px;
    text-transform: uppercase;
    position: absolute;
    color: rgba(0, 0, 0, 0.5);
  }
  
  .text-editor-tokenComment {
    color: slategray;
  }
  
  .text-editor-tokenPunctuation {
    color: #999;
  }
  
  .text-editor-tokenProperty {
    color: #905;
  }
  
  .text-editor-tokenSelector {
    color: #690;
  }
  
  .text-editor-tokenOperator {
    color: #9a6e3a;
  }
  
  .text-editor-tokenAttr {
    color: #07a;
  }
  
  .text-editor-tokenVariable {
    color: #e90;
  }
  
  .text-editor-tokenFunction {
    color: #dd4a68;
  }
  
  .text-editor-paragraph {
    font-size: 14px;
    margin: 0;
    margin-bottom: 8px;
    position: relative;
  }
  
  .text-editor-paragraph:last-child {
    margin-bottom: 0;
  }
  
  .text-editor-heading-h1 {
    font-size: 24px;
    color: rgb(5, 5, 5);
    font-weight: 400;
    margin: 0;
    margin-bottom: 12px;
    padding: 0;
  }
  
  .text-editor-heading-h2 {
    font-size: 15px;
    color: rgb(101, 103, 107);
    font-weight: 700;
    margin: 0;
    margin-top: 10px;
    padding: 0;
    text-transform: uppercase;
  }
  
  .text-editor-quote {
    margin: 0;
    margin-left: 20px;
    font-size: 15px;
    color: rgb(101, 103, 107);
    border-left-color: rgb(206, 208, 212);
    border-left-width: 4px;
    border-left-style: solid;
    padding-left: 16px;
  }
  
  .text-editor-list-ol {
    padding: 0;
    margin: 0;
    margin-left: 16px;
  }
  
  .text-editor-list-ul {
    padding: 0;
    margin: 0;
    margin-left: 16px;
  }
  
  .text-editor-listitem {
    margin: 8px 32px 8px 32px;
  }
  
  .text-editor-nested-listitem {
    list-style-type: none;
  }
  
  
  .text-editor-toolbar {
    display: flex;
    /* margin-bottom: 1px; */
    /* background: #fff; */
    padding: 12px;
    /* padding-bottom: 0; */
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    vertical-align: middle;
    column-gap: 8px;
  }
  
  .text-editor-toolbar button.text-editor-toolbar-item {
    border: 0;
    display: flex;
    background: none;
    border-radius: 10px;
    /* padding: 8px; */
    /* padding-right: 4px; */
    cursor: pointer;
    vertical-align: middle;
    padding-bottom: 0;
  }
  
  .text-editor-toolbar button.text-editor-toolbar-item:disabled {
    cursor: not-allowed;
  }
  
  /* .text-editor-toolbar button.text-editor-toolbar-item.spaced {
    margin-right: 2px;
  } */
  
  .text-editor-toolbar button.text-editor-toolbar-item i.format {
    background-size: contain;
    display: inline-block;
    height: 18px;
    width: 16px;
    /* margin-top: 2px; */
    vertical-align: -0.25em;
    display: flex;
    /* opacity: 0.6; */
  }
  
  .text-editor-toolbar button.text-editor-toolbar-item:disabled i.format {
    opacity: 0.2;
  }
  
  .text-editor-toolbar button.text-editor-toolbar-item.active {
    color: #2e2e2e;
  }
  
  .text-editor-toolbar button.text-editor-toolbar-item.active i {
    opacity: 1;
    color: #2e2e2e;
  }
  
  .text-editor-toolbar .text-editor-toolbar-item:hover:not([disabled]) {
    /* background-color: #eee; */
    color: #2e2e2e;
    opacity: 1;
  
  
  }
  
  .text-editor-toolbar .divider {
    width: 1px;
    background-color: #eee;
    margin: 0 4px;
  }
  
  .text-editor-toolbar .text-editor-toolbar-item .text {
    display: flex;
    line-height: 20px;
    width: 200px;
    vertical-align: middle;
    font-size: 14px;
    color: #777;
    text-overflow: ellipsis;
    width: 70px;
    overflow: hidden;
    height: 20px;
    text-align: left;
  }
  
  .text-editor-toolbar .text-editor-toolbar-item .icon {
    display: flex;
    width: 20px;
    height: 20px;
    user-select: none;
    margin-right: 8px;
    line-height: 16px;
    background-size: contain;
  }
  /* text editor, container css (end) */
  
  /* text editor, link-editing css (start) */
  .text-editor-link-editor {
    position: absolute;
    z-index: 100;
    top: -10000px;
    left: -10000px;
    margin-top: -6px;
    max-width: 300px;
    width: 100%;
    opacity: 0;
    background-color: #fff;
    box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    transition: opacity 0.5s;
  }
  
  .text-editor-link-editor .link-input {
    display: block;
    width: calc(100% - 24px);
    box-sizing: border-box;
    margin: 8px 12px;
    padding: 8px 12px;
    border-radius: 15px;
    background-color: #eee;
    font-size: 15px;
    color: rgb(5, 5, 5);
    border: 0;
    outline: 0;
    position: relative;
    font-family: inherit;
  }
  
  .text-editor-link-editor .link-input-edit {
    display: block;
    width: calc(100% - 24px);
    box-sizing: border-box;
    background-color: #eee;
    font-size: 15px;
    color: rgb(5, 5, 5);
    border: 0;
    outline: 0;
    position: relative;
    font-family: inherit;
  }
  
  .text-editor-link-editor div.link-edit {
    width: 35px;
    vertical-align: -0.25em;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  
  .text-editor-link-editor .link-input a {
    color: rgb(33, 111, 219);
    text-decoration: none;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    margin-right: 30px;
    text-overflow: ellipsis;
  }
  
  .text-editor-link-editor .link-input a:hover {
    text-decoration: underline;
  }
  
  .text-editor-link-editor .button {
    width: 20px;
    height: 20px;
    display: inline-block;
    padding: 6px;
    border-radius: 8px;
    cursor: pointer;
    margin: 0 2px;
  }
  
  .text-editor-link-editor .button.hovered {
    width: 20px;
    height: 20px;
    display: inline-block;
    background-color: #eee;
  }
  
  .text-editor-link-editor .button i,
  .actions i {
    background-size: contain;
    display: inline-block;
    height: 20px;
    width: 20px;
    vertical-align: -0.25em;
  }
  /* text editor, link-editing css (end) */