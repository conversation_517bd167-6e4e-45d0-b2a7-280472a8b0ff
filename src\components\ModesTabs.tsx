import {FC, useCallback, useEffect, useRef, useState} from "react";
import {Autocomplete, AutocompleteItem, Button, Tab, <PERSON><PERSON>, Toolt<PERSON>} from "@nextui-org/react";
import {AViewerPlugin, Class, PickingPlugin, UiObjectConfig, ViewerApp} from "webgi";
import {motion, useMotionValue} from "framer-motion";
import PresetIcon from "./icons/Preset";
import ExportIcon from "./icons/Export";
import PluginsIcon from "./icons/Plugins";
import ExtrasIcon from "./icons/Extras";
import SceneIcon from "./icons/Scene";
import PickingIcon from "./icons/Picking";
import AntiAliasingIcon from "./icons/Aliased.tsx";
import ModifiersIcon from "./icons/Modifiers.tsx";
import AnimationsIcon from "./icons/Animations.tsx";
import PostProcessingIcon from "./icons/PostProcessing.tsx";
import ViewerIcon from "./icons/Viewer.tsx";
import ConfiguratorsIcon from "./icons/Configurators.tsx";
import {useUi} from "./provider/UiProvider";
import {EditorSettings} from "../main";
import DoubleArrowRight from './icons/DoubleArrowRight.tsx'
import { Modal } from "./Modal.tsx";
import SearchIcon from "./icons/Search.tsx";
import PrimHelpIcon from "./icons/PrimHelpIcon";
import VideoHintsModal from "./VideoHintsModal.tsx";
interface ModesTabsProps {
  viewer?: ViewerApp;
  editorSettings?: EditorSettings;
  reverseLayout?: boolean;
}

const mobileDefaultWidth = 200;
const defaultWidth = 360;

function setTabsWidth(width: number) {
  const tabs = document.getElementById("modes-tabs");
  if (tabs) {
    tabs.style.width = `${width}px`;
  }
}

const icons = {
  'Viewer': <ViewerIcon/>,
  'Export': <ExportIcon/>,
  'Plugins': <PluginsIcon/>,
  'Extras': <ExtrasIcon/>,
  'Scene': <SceneIcon/>,
  'Picking': <PickingIcon/>,
  'Anti-aliasing': <AntiAliasingIcon/>,
  'Modifiers': <ModifiersIcon/>,
  'Animations': <AnimationsIcon/>,
  'Post Processing': <PostProcessingIcon/>,
  'Configurators': <ConfiguratorsIcon/>,
} as any

const ModesTabs: FC<ModesTabsProps> = (props) => {
  const cursor = useRef<HTMLDivElement>(null);
  const presetTab = useRef<HTMLDivElement>(null);
  // const [selectedPresetTab, setSelectedPresetTab] = useState("VJSON");
  // const [presetGroups, setPresetGroups] = useState<any[]>();
  const [modeTabs, setModeTabs] = useState<{title:string, [key: string]: any}[]>();
  const [selectedMode, setSelectedMode] = useState("");
  const { freeze } = useUi();
  const tabsRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const mWidth = useMotionValue(defaultWidth);
  const [isHidden, setIsHidden] = useState(false);
  const [search, setSearch] = useState("");
  const [openSearch, setOpenSearch] = useState(false);
  const [openHelpModal, setOpenHelpModal] = useState(false)
  const [results, setResults] = useState<{label: string, hierarchy: UiObjectConfig[], mode: any}[]>([]);
  const [menuVideosHints, setMenuVideosHints] = useState<{isMenuOpen: boolean, menuType?: string | null }>({isMenuOpen: false , menuType: null})
  

  const handleDrag = useCallback((event: any, info: any) => {
    // console.log(info);
    const delta =  - info.delta.x * (!props.reverseLayout ? -1 : 1)
    let newWidth = mWidth.get() + delta;
    if (newWidth > 188 && newWidth < 600) {
      const val = mWidth.get() + delta
      mWidth.set(val);
      setTabsWidth(val);
    }
  }, []);

  const moveCursor = useCallback(
    (to: string) => {
      let padding = 0;
      if (cursor.current && tabsRef.current) {
        const elm = tabsRef.current.querySelector(
          `[data-key="${to}"]`
        ) as HTMLElement;
        if (!elm) return;
        const cur = cursor.current;

        // Set size
        // Set size with added padding
        const height = elm.dataset.key === 'hide' ? 32 : elm.offsetHeight + 2 * padding // 2* for top and bottom
        cur.style.height = `${height}px`;
        // cur.style.width = `${elm.offsetWidth + 2 * padding}px`; // 2* for left and right
        cur.style.width = `10px`;

        // Set position adjusted for padding
        cur.style.top = `${elm.offsetTop + elm.offsetHeight - height - padding}px`;
        if(!props.reverseLayout)
          cur.style.left = `${elm.offsetLeft - padding - 6}px`;
        else cur.style.right = `${elm.offsetLeft - padding - 6}px`;
      }
    },
    [tabsRef, cursor, props.reverseLayout]
  );

  const modes = (window as any).webgi_editorModes as {title: string; plugins: Class<AViewerPlugin<any>>[]; div?: HTMLDivElement}[]
  const setEditorMode = (window as any).webgi_setEditorMode as (m: any, dispatch: boolean)=>void
  const [lastTab, setLastTab] = useState<string>("");
  //search
  useEffect(() => {
    if(!modes || !props.viewer || !search) return;
    const searchUiObject = (obj: UiObjectConfig , value : string,
       result:{label: string, hierarchy: UiObjectConfig[], mode : any}[], hierarchy: UiObjectConfig[] = [],
       mode: any
    ) => { 
      const uiObject = typeof obj === "function" ? (obj as any)() : obj;

      //exclude whatever is here because it is not useful
      if(uiObject?.label === "Preset/Config export") return;
      
      const label = typeof uiObject?.label === "string" ? uiObject?.label : uiObject?.label?.();
      if(value && label && label.toLowerCase().includes(value.toLowerCase())
      ) {
        result.push({label, hierarchy: [...hierarchy, uiObject ], mode})
      }else if(uiObject?.children){
        uiObject.children.forEach((child : any) => searchUiObject(child , value, result, [...hierarchy, uiObject], mode))
      }
    }
    const result:{label: string, hierarchy: UiObjectConfig[] , mode: any}[] = []
    modes && modes.forEach((mode) => {
      mode.plugins.forEach((plugin) => {
        const uiObject : UiObjectConfig =  (props.viewer!.getPlugin(plugin) as any)?.uiConfig
        searchUiObject(uiObject , search, result,[], mode)
      });
    });
    setResults(result)
  }, [search]);

  const changeTab = useCallback((key: string, setMode = true) => {
    setTimeout(() => {
      moveCursor(key);
    }, 100);
    if(isHidden && key === 'hide') {
      changeTab(lastTab);
      return; 
    }
    if(key === 'hide') setLastTab(selectedMode);
    setSelectedMode(key);
    modes && setMode && setEditorMode && setEditorMode(key === 'hide' ? modes[0] : modes.find(m => m?.title === key), false);
    setIsHidden(key === 'hide');
    const width = key === 'hide' ? 40 : mWidth.get() < 41 ? 360 : 0;
    if(width) {
      mWidth.set(width);
      setTabsWidth(width);
    }
  }, [modes, setEditorMode , selectedMode, mWidth, moveCursor, lastTab, isHidden]);

  useEffect(() => {
    if (props.viewer && modes) {
      const container = document.querySelector("#tweakpaneUiContainer") as HTMLDivElement;
      console.log(container)
      if(container) {
        container.style.height = "100%"
        const elm = container?.firstChild as HTMLDivElement;
        if(elm) {
          elm.classList.add("tp-rotv-expanded")
        }
      }
      setModeTabs(modes);
      //hide the panel for smaller devices
      const small = window.matchMedia("(max-width: 1024px)").matches
      if(small){
        // console.log("hide")
        changeTab('hide');
      }else{
        changeTab(modes[0].title);
      }
    }
    setTabsWidth(mWidth.get())
  }, [props.viewer, modes]);

  useEffect(() => {
    const listener =  (e: CustomEvent)=>{
      const key = e.detail.mode?.title
      if(selectedMode === "hide") return;
      const i = modes?.findIndex(m => m.title === key);
      if(i === -1) return;
      changeTab(key, false);
      if(!modes) return;
      modes[i] = e.detail.mode;
      // if(modes?.find(m=>m.title === key)) changeTab(key, false);
    }
    window.addEventListener('webgi_editorModeChanged' as any, listener)
    return () => {
      window.removeEventListener('webgi_editorModeChanged' as any, listener)
    }
  } , [modes , changeTab , selectedMode])

  const expandHierarchy = (hierarchy: UiObjectConfig[]) => {
    hierarchy.forEach((h) => {
      if(h.expanded !== undefined) h.expanded = true;
    }
  )};

  const onSearchResultClicked = useCallback((value : string) => { 
    setOpenSearch(false)
    if(value !== null) {
      const selected = (value as string).split("-")[1];
      setSearch(selected as string);
      const r = results.find(r => r.label === selected)
      if(!r) return;
      changeTab(r.mode?.title || selectedMode)
      expandHierarchy(r.hierarchy)

      const elem = r.hierarchy[r.hierarchy.length - 1].uiRef.element as HTMLElement;

      //make it blink
      elem.classList.add("blink")

      //wait for it to expand and scroll
      setTimeout(() => { 
        elem.scrollIntoView({behavior: "smooth", block: "center", inline: "center"});
      }, 500)
      setTimeout(() => {
        elem.classList.remove("blink")
      }, 2000)
      

    }
  }, [results , selectedMode]);

  //open search on crtl + f
  const onKeydown = useCallback((e: KeyboardEvent) => {
    if(e.key === "f" && e.ctrlKey) {
      e.preventDefault();
      setOpenSearch(true);
    }else if(e.key === "Escape") {
      setOpenSearch(false)
    }
  }, []);

  useEffect(() => {
    window.addEventListener("keydown", onKeydown , true);
    return () => {
      window.removeEventListener("keydown", onKeydown , true);
    }
  } , []);

  const modeTitle = modes?.find(m => m.title === selectedMode)?.title

  return (
    <motion.div
      id="modes-tabs"
      // layout
      // style={{
      //   backgroundColor: "steelblue",
      //   // width: props.width,
      //   // width: 200,
      //   // cursor: isDragging ? "row-resize" : "",
      // }}
      className={`w-auto flex h-full overflow-visible justify-end resize-x relative ${!props.reverseLayout ? 'flex-row-reverse mr-2' : 'ml-2'}`}
    >
      <Modal placement="center" key={"search"} isOpen={openSearch} size="md" onOpenChange={setOpenSearch} backdrop="blur" hideCloseButton >
        {({ onClose }: any) => (
          <Autocomplete
            autoFocus
            value={search}
            defaultInputValue={search}
            onSelectionChange={
              (value) => {
                onSearchResultClicked(value as string)
              }
            }
            onValueChange={(value: string) => {
              setSearch(value);
            }}
            allowsCustomValue
            onKeyDown={(e) => {
              if ("continuePropagation" in e) {
                e.continuePropagation();
              }
            }}
            shouldCloseOnBlur
            aria-label={"Search"}
            items={results.map((result) => result.label)}
            className="text-black"
          >
            {results.map((result , i) => (
              <AutocompleteItem classNames={{title : "text-black flex"}} shouldHighlightOnFocus key={i + "-" + result.label}
                value={result.label}
              >
                <p className="opacity-65 text-sm max-w-unit-7xl overflow-hidden truncate mr-unit-md">
                  {result.hierarchy.slice(0, -1).map((h) => typeof h?.label === "string" ? h?.label : h?.label?.()).join(" > ")}
                </p>
                {result.label}
              </AutocompleteItem>
            ))}
          </Autocomplete> 
        )}
      </Modal>
          
      <VideoHintsModal
        viewer={props?.viewer}
        isMenuOpen={menuVideosHints.isMenuOpen}
        menuType={menuVideosHints.menuType as any}
        setMenuVideosHints={setMenuVideosHints}
      />

      <motion.div
        className={`h-full flex justify-center items-center absolute 
        ${!props.reverseLayout ? 'right-0 cursor-w-resize' : '-left-2 cursor-e-resize'}
        ${isHidden ? 'hidden' : ''}`}
        drag="x"
        dragConstraints={{ top: 0, left: 0, right: 0, bottom: 0 }}
        dragElastic={0}
        dragMomentum={false}
        onDrag={handleDrag}
        onDragEnd={() => {
          setIsDragging(false);
        }}
        onDragStart={() => {
          setIsDragging(true);
        }}
      >
        <div className="w-unit-xs h-unit-4xl rounded-lg bg-default-300 flex justify-center items-center cursor-e-resize translate-x-1"></div>
      </motion.div>

      <div className={`h-full flex flex-col items-center overflow-y-hidden w-full scrollbar-hide ${(isHidden||!modes?.length) ? 'hidden': ''}`}>
            <div
              key={"title"}
              className="w-full text-default-foreground font-inherit !text-sm font-mainSemiBold mb-unit-sm text-center gap-2 flex items-center justify-center">
              <div/>
              <p className="flex items-center gap-unit-md">
                {modeTitle} 
                {/* <Button 
                  isIconOnly
                  radius="none"
                  variant="light"
                  disableRipple
                  startContent={<PrimHelpIcon />}
                  onClick={() => setMenuVideosHints({ menuType: modeTitle , isMenuOpen: true })}
                /> */}
              </p> 
              <Tooltip content="Search ⌘+F" placement="top" size="sm" color="secondary"> 
                <Button 
                  isIconOnly
                  startContent={<SearchIcon />}
                  onClick={() => setOpenSearch(true)}
                  ></Button>
              </Tooltip>
            </div>
            <div
                className={
                    "flex h-full items-start justify-center gap-1 w-full flex-wrap overflow-hidden "
                }
            >
              <div id="tweakpaneMainPanelSlot"
                   className="z-10 rounded-small h-full w-full flex flex-col transition-width duration-1000"
                   >
              </div>

            </div>
      </div>

      <div
          ref={tabsRef}
          className="w-[50px] flex flex-col items-center relative overflow-visible h-full"
      >
        <div
            ref={cursor}
          className="bg-primary absolute w-full cursor-style rounded-md"
        ></div>
          <Tabs
              // id="presetTabs"
              ref={presetTab}
              selectedKey={selectedMode}
              onSelectionChange={(key) => changeTab(key as any)}
              color="default"
              variant="light"
              isDisabled={freeze}
              fullWidth
              className="h-full"
          classNames={{
            tabList: ["flex flex-col gap-unit-xl p-0 h-full"],
            tabContent:
              " group-data-[selected=true]:text-primary text-default-foreground",
            cursor: "hidden ",
            tab: `px-unit-md !text-default ${props.reverseLayout ? "pl-2" : "pr-4"}`,
          }}
          >
            {modeTabs?.map((mode) => (
                <Tab
                    key={mode.title}
                    title={
                  <Tooltip delay={300} placement="left" content={mode.title}>
                     <div onClick={() => changeTab(mode.title)}>
                       {icons[mode.title] ? icons[mode.title] : <PresetIcon />}
                     </div>
                   </Tooltip>
                }/>))}
            {modeTabs?.length && <Tab
                key={"hide"}
                className={"tabList-last-tab"}
                title={
                  <Tooltip delay={300} placement="left" content={"Hide"}>
                    <div>
                      <DoubleArrowRight className={isHidden ? "rotate-180" : ""}/>
                    </div>
                  </Tooltip>
                }/>}
          </Tabs>
        </div>
    </motion.div>
  );
};

export {ModesTabs};
