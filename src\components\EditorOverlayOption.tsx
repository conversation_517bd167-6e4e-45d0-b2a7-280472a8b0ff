import { FC, CSSProperties } from "react";
import { Img } from "./Img";

interface IconProps {
  src: string;
  className?: string;
  onClick?: () => void;
}

const EditorOverlayOption: FC<IconProps> = (props) => {
  return (
    <div onClick={props.onClick} className={"flex justify-center items-center  flex-col cursor-pointer  " + props.className}>
      <Img
        src={props.src}
        className="rounded-full h-unit-3xl w-unit-3xl object-cover outline-2 hover:outline outline-white transition-all duration-100 ease-in"
      ></Img>
    </div>
  );
};

export { EditorOverlayOption };
