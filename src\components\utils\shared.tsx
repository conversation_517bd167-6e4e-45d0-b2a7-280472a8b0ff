import toast from "@minieditor/react-hot-toast";
import {
  <PERSON><PERSON>,
  <PERSON>overTrigger,
  Popover<PERSON>ontent,
  Switch,
  Tooltip,
  TooltipProps,
  Modal,
  ModalContent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>dalB<PERSON>,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  <PERSON><PERSON>,
} from "@nextui-org/react";
import { useEffect, useState } from "react";
import Close from "../icons/Close";
import LockIcon from "../icons/Lock";
import PlusSolid from "../icons/PlusSolid";
import ED_SETTING from "../../types";
import { premiumUserLimits } from "./limits";
import { PresetGroupTab } from "../EditorTabs";
import { canUplaodPresetOrMaterial } from "./functions";
import HelpIcon from "../icons/HelpIcon";
import { Button } from "../Button";
import { useProject } from "../provider/ProjectProvider";
import { useUi } from "../provider/UiProvider";

export const HideMaterialOrPresetItemWrapper = (props: {
  asset: any;
  isSelected: boolean;
  selectedPresetTab: string;
  onConfirm: (asset: any, assetType: string, isSelected: boolean) => Promise<void>;
  selectedHideAsset: string | null;
  setSelectedHideAsset: (path?: any) => void;
  children: React.ReactNode;
}) => {
  const { asset, onConfirm, selectedHideAsset, setSelectedHideAsset, isSelected, selectedPresetTab, children } = props;
  const [isLoading, setIsLoading] = useState(false);

  if (asset.name == "none") return null;
  if (!asset.isCustom) {
    return children;
  }

  return (
    <Popover key={asset.path} isOpen={asset.path === selectedHideAsset} placement="bottom" offset={20} showArrow>
      <PopoverTrigger className="relative">
        <span>
          <span
            id="hide-asset-button"
            onClick={() => {
              if (isSelected) {
                setSelectedHideAsset(null);
                toast.error("Before deleting this asset, please either deselect it or replace it with another option.", {
                  id: "asset-unselect-error",
                });
              } else setSelectedHideAsset(asset.path);
            }}
            className="z-10 absolute top-0 right-1 translate-x-1  w-fit h-fit p-unit-xs bg-[#D7D7D7] rounded-full"
          >
            <Close width={7} height={7} stroke="#898989" />
          </span>
          {children}
        </span>
      </PopoverTrigger>
      <PopoverContent className="rounded-small">
        <div className="flex flex-col gap-unit-xl py-unit-md text-sm">
          <p>Are you sure you want to delete it?</p>
          <div className="flex gap-unit-lg w-full justify-center items-center">
            <Button onClick={() => setSelectedHideAsset(null)} fullWidth={false} color="default" size="sm" className="py-unit-8 px-unit-2xl">
              Cancel
            </Button>
            <Button
              onClick={async () => {
                setIsLoading(true);
                await onConfirm(asset, selectedPresetTab, isSelected);
                setIsLoading(false);
              }}
              fullWidth={false}
              isLoading={isLoading}
              isDisabled={isLoading}
              color="danger"
              size="sm"
              className="py-unit-8 px-unit-2xl"
            >
              Delete
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export const UploadMaterialOrPresetButton = (props: {
  handleAddCustomAsset: (data: { file: File; assetType: string }) => Promise<void>;
  assetType: Exclude<PresetGroupTab, "ModelStage"> | "Material";
  isLogin?: boolean;
  rect?: boolean;
  isBusiness?: boolean;
  isPremium?: boolean;
  setShowSubscribeModal?: (...arg: any[]) => void;
  customAssetsStore: Record<string, ED_SETTING.CUSTOM_ASSET_ITEM[]> | null;
  fullEditor?: boolean;
}) => {
  const { handleAddCustomAsset, assetType, rect, fullEditor } = props;
  const [isLoading, setIsLoading] = useState(false);

  if(fullEditor && assetType === 'VJSON') return null;

  const totalMaterialsAndPresets = Object.values(props.customAssetsStore ?? {}).flat().length;
  const premiumUserLmt = premiumUserLimits.customAsset.uploadPresetLimit;
  const isPremiumUserLimitReached = !props.isBusiness && props.isPremium && totalMaterialsAndPresets >= premiumUserLmt;

  const handleInputFile = (inputfile: File) => {
    if(!inputfile) return;
    setIsLoading(true);
    if (
      !canUplaodPresetOrMaterial({
        customAssetsStore: props.customAssetsStore,
        setShowSubscribeModal: props.setShowSubscribeModal,
        isLogin: props.isLogin,
        isBusiness: props.isBusiness,
        isPremium: props.isPremium,
      })
    ) {
      setIsLoading(false);
      return;
    }

    const file = inputfile;
    let isPassed = true,
      msg = "",
      tOptions = {};

    if (file.size > 5 * 1024 * 1024) {
      isPassed = false;
      msg = "File size is too large. Please upload a file less than 5MB.";
    } else if (file.size > 2 * 1024 * 1024) {
      isPassed = false;
      msg = "File size is over 2MB.";
      tOptions = {
        icon: "Warning:",
        className: "!bg-[#fdf8e3]",
      };
    }

    if (!isPassed) {
      toast.error(msg, {
        id: "asset-upload-error",
        duration: 5000,
        ...tOptions,
      });
      setIsLoading(false);
      return;
    }

    let id = toast.loading("Uploading Asset...", {
      id: "asset-upload-start",
    });
    handleAddCustomAsset?.({ file, assetType })
      .then(() => {
        toast.dismiss(id);
        toast.success("Asset uploaded successfully", {
          id: "asset-upload-success",
        });
      })
      .catch((err: any) => {
        toast.dismiss(id);
        toast.error(err.message, { id: "asset-upload-error" });
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  let bg = "bg-primary";
  let planHint = 'Premium'

  if (isPremiumUserLimitReached) {
    bg = "bg-[linear-gradient(229.19deg,_#FCC253_-33.33%,_#FFE0A4_18.24%,_#B5821F_69.81%,_#FFD788_121.38%)]";
    planHint = "Business";
  }

  let buttonWrapper = rect ? "w-full max-w-[70px] h-[70px] rounded-md my-auto " : "w-[45px] h-[45px] rounded-full my-auto ";

  let buttonClass = rect ? "w-full max-w-[66px] h-[66px] p-0 rounded-md " : "w-[42px] h-[42px] p-0 rounded-full ";

  if (props.isBusiness || (props.isPremium && !isPremiumUserLimitReached)) {
    buttonClass = buttonWrapper + " !bg-[#CCCCCC]";
  } else {
    buttonWrapper += ` relative ${bg}`;
    buttonClass += " absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 !bg-[#737373]";
  }

  const inputAccept = {
    Environment: ".hdr, .exr",
    GemEnvironment: ".hdr, .exr",
    VJSON: ".json, .vjson",
    Ground: ".json",
    Background: "image/*",
    Material: ".pmat, .dmat",
  };

  return (
    <>
      <input
        type="file"
        accept={inputAccept[assetType]}
        className="hidden"
        id="asset-upload-input"
        onChange={(e) => {
          e.preventDefault();
          if (!e.target.files || e.target.files?.length === 0) return;
          handleInputFile(e.target.files[0]);
        }}
      />
      <div className={buttonWrapper}>
        {!rect && ((!props.isBusiness && !props.isPremium) || isPremiumUserLimitReached) && (
          <span className={`rounded-full z-10 h-[17px] w-[17px] flex items-center justify-center absolute top-0 right-0 ${bg}`}>
            <LockIcon className="text-white" width="9px" height="11px" />
          </span>
        )}

        {rect && !props.isBusiness && (
          <span
            className={`z-10 absolute left-1/2 -mt-[7.5px] -translate-x-1/2 w-[57px] text-center px-2 py-1 font-bold text-[9px] leading-none rounded-full text-[#FFFFFF] ${bg}`}
          >
           {planHint}
          </span>
        )}
        
        <Button
          className={buttonClass}
          isDisabled={isLoading}
          onClick={() => {
            const canUpload = canUplaodPresetOrMaterial({
              customAssetsStore: props.customAssetsStore,
              setShowSubscribeModal: props.setShowSubscribeModal,
              isLogin: props.isLogin,
              isBusiness: props.isBusiness,
              isPremium: props.isPremium,
            });
            if (canUpload) document.getElementById("asset-upload-input")?.click();
          }}
          startContent={
            <span>
              <PlusSolid className={`${"w-5 h-5"}`} fill="#FFFFFF" />
            </span>
          }
        />
      </div>
    </>
  );
};

export const Help = (props: TooltipProps) => {
  const { content, placement = "top", ...rest } = props;
  return (
    <Tooltip
      delay={300}
      placement={placement}
      content={content}
      color={"secondary"}
      closeDelay={500}
      className="p-2 max-w-[150px] text-tiny"
      {...(rest ?? {})}
    >
      <span className="inline-block">
        <HelpIcon fill="#A8A8A8" />
      </span>
    </Tooltip>
  );
};

export const SwitchController = (props: {
  value?: boolean;
  title: string;
  description?: string;
  onChange: (field: string, value: boolean) => void;
  isDisabled?: boolean;
  field: string;
  helpMessage?: string;
  helpProps?: Partial<TooltipProps>;
}) => {
  const { isDisabled = false, value, title, description, field, helpMessage, onChange } = props;

  return (
    <div key={field} className="flex justify-between  gap-unit-md items-center py-3">
      <span
        className={`flex flex-col items-baseline gap-unit-sm text-[11px] font-normal  ${isDisabled ? "text-[#87878a]" : "cursor-pointer"}`}
        onClick={() => {
          if (!isDisabled) onChange(field, !value);
        }}
      >
        <div>
          <span className="mr-1 leading-4">{title}</span> {helpMessage && <Help content={helpMessage} {...(props.helpProps ?? {})} />}
        </div>
        {description && <div className="text-[#87878A] text-[10px] leading-4 ">{description}</div>}
      </span>
      <Switch
        id={field}
        isSelected={value}
        isDisabled={isDisabled}
        onValueChange={(value) => {
          onChange(field, value);
        }}
        aria-label="Responsive"
        classNames={{
          wrapper: "h-[18px] w-[30px] m-0 bg-[#AFAFAF]",
          thumb: "h-3 w-3 group-data-[selected=true]:ml-2.5 group-data-[pressed=true]:w-3 group-data-[selected]:group-data-[pressed]:ml-2",
        }}
      />
    </div>
  );
};

export const DeleteLogoModal = (props: {
  isOpen: boolean;
  onOpenChange: (check: boolean) => void;
  logoUseCount: number | null;
  handleLogoRemove: () => void;
  canUnassign: boolean;
}) => {
  const { isOpen, onOpenChange, handleLogoRemove, canUnassign, logoUseCount } = props;

  return (
    <Modal
      key="delete-logo-modal"
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      backdrop="blur"
      radius="md"
      size="xl"
      className="py-4 px-5"
      classNames={{
        closeButton: "hidden",
      }}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex gap-4 items-center p-0">
              <span className="text-xl leading-none font-bold text-[#373737]">Action required</span>
              <div className="flex-1" />
              <span className="p-2 cursor-pointer" onClick={onClose}>
                <Close width={14} height={14} fill="#898989" stroke="#898989" />
              </span>
            </ModalHeader>
            <ModalBody className="p-0 px-2.5 mt-1">
              <ul className="list-inside list-disc leading-6 text-sm font-medium text-[#373737]">
                <li>To delete the logo, you must first unassign it from any associated projects.</li>
                <li>
                  Currently, this logo is assigned to <strong>{logoUseCount ?? 0} project(s)</strong>.
                </li>
                {canUnassign && (
                  <li>
                    Click the button below to <strong>unassign the logo from this project</strong>.
                  </li>
                )}
              </ul>
            </ModalBody>
            {canUnassign && (
              <ModalFooter className="p-0 mt-1">
                <div className="flex-1 flex justify-end">
                  <div className="flex gap-unit-lg">
                    <Button
                      color="default"
                      varient="solid"
                      onPress={() => onOpenChange(false)}
                      className="min-w-[72px] h-[30px] w-fit px-3.5 rounded-full gap-unit-lg text-sm font-semibold"
                    >
                      <span className="text-sm font-semibold">Cancel</span>
                    </Button>
                    <Button color="danger" onPress={handleLogoRemove} className="min-w-[136px] h-[30px] w-fit px-3.5 rounded-full gap-unit-lg">
                      <span className="text-sm font-semibold">Unassign</span>
                    </Button>
                  </div>
                </div>
              </ModalFooter>
            )}
          </>
        )}
      </ModalContent>
    </Modal>
  );
};

export interface ScreenLoadingLogoProps {
  isDisabled?: boolean;
  getLogoList?: ED_SETTING.EMBED["getlogoList"];
  handleUploadLogo?: ED_SETTING.EMBED["handleUploadLogo"];
  handleProjectLogoUpdate?: ED_SETTING.EMBED["handleProjectLogoUpdate"];
  getProjectCountByLogoUrl?: ED_SETTING.EMBED["getProjectCountByLogoUrl"];
  currentLogoUrl?: string | null;
  maxLogoUploadLimit?: number;
  logoUrlField: string;
  disableDelete?: boolean;
  disableUpload?: boolean;
  heading?: string;
}
export const ScreenLoadingLogo = (props: ScreenLoadingLogoProps) => {
  const {
    isDisabled,
    currentLogoUrl: initialLogoUrl,
    handleUploadLogo,
    handleProjectLogoUpdate,
    getProjectCountByLogoUrl,
    maxLogoUploadLimit = 10,
    logoUrlField,
    disableDelete = false,
    disableUpload = false,
    heading,
  } = props;

  const { setProject } = useProject();
  const { embed } = useUi();
  const logoList = embed.logoList || [];
  const [currentLogoUrl, setCurrentLogoUrl] = useState<string | null | undefined>(initialLogoUrl);
  const [isLoading, setIsLoading] = useState(false);

  const fetchLogoList = () =>
    props
      .getLogoList?.()
      .then((data) => {
        embed.setLogoList(data);
      })
      .catch((err) => {
        toast.error(err.message);
      });

  const deleteInit = {
    item: null,
    isPopupOpen: false,
    isSeconaryModalOpen: false,
    logoUseCount: null,
    isFetchingCount: true,
  };
  const [deleteStore, setDeleteStore] = useState<{
    item: any | null;
    isPopupOpen: boolean;
    isSeconaryModalOpen: boolean;
    logoUseCount: null | number;
    isFetchingCount: boolean;
  }>(deleteInit);

  useEffect(() => {
    if (deleteStore.isPopupOpen == true && deleteStore.item) {
      setIsLoading(true);
      getProjectCountByLogoUrl?.({ field: logoUrlField, logoUrl: deleteStore.item.path })
        .then((count) => {
          setDeleteStore({
            ...deleteStore,
            logoUseCount: count,
            isFetchingCount: false,
          });
        })
        .catch((err) => {
          toast.error(err.message);
          setDeleteStore({
            ...deleteStore,
            isFetchingCount: false,
          });
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [deleteStore.isPopupOpen]);

  const isDeleteOpen = deleteStore.isPopupOpen;
  const isModalOpen = deleteStore.isSeconaryModalOpen;

  const handleLogoChange = (item: any) => () => {
    if (item?.path === currentLogoUrl) return;
    if (isDisabled) {
      toast.error("Please upgrade to use custom logo");
      return;
    }
    const id = toast.loading("Updating logo...", {
      id: "logo-change-start",
    });
    setIsLoading(true);
    handleProjectLogoUpdate?.({ item })
      .then(() => {
        setCurrentLogoUrl(item.path);
        setProject((prev) => ({ ...prev, [logoUrlField]: item.path }));

        toast.dismiss(id);
        toast.success("Logo changed successfully", {
          id: "logo-change-success",
        });
      })
      .catch((err) => {
        toast.dismiss(id);
        toast.error(err.message, { id: "logo-change-error" });
      })
      .finally(() => {
        setIsLoading(false);
      });
  };
  const handleLogoRemove = () => {
    if (isDisabled) {
      toast.error("Please upgrade to use custom logo");
      return;
    }
    const item = deleteStore.item;
    const count = deleteStore.logoUseCount;
    const isModalOpen = deleteStore.isSeconaryModalOpen;
    if (!item || count === null) return;

    if (count > 0 && !isModalOpen) {
      handleModalChange(true);
      return;
    }
    const isRemoveAsset = count === 0 ? true : false;
    const id = toast.loading(`${isRemoveAsset ? "Deleting" : "Unassigning"} Logo...`, {
      id: "logo-change-start",
    });
    setIsLoading(true);
    handleProjectLogoUpdate?.({ isRemove: true, isRemoveAsset, item })
      .then(() => {
        if (item.path === currentLogoUrl) {
          setCurrentLogoUrl(null);
          setProject((prev) => ({ ...prev, [logoUrlField]: null }));
        }
        if (isRemoveAsset) fetchLogoList();
        setDeleteStore(deleteInit);

        toast.dismiss(id);
        toast.success(`Logo ${isRemoveAsset ? "deleted" : "unassigned"} successfully`, {
          id: "logo-change-success",
        });
      })
      .catch((err) => {
        toast.dismiss(id);
        toast.error(err.message, { id: "logo-change-error" });
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleFileChange = (event: any) => {
    event.preventDefault();
    if (isDisabled) {
      toast.error("Please upgrade to upload logo");
      return;
    }
    const file = event.target.files[0];
    let id = toast.loading("Uploading logo...", {
      id: "logo-upload-start",
    });
    setIsLoading(true);
    handleUploadLogo?.({ file })
      .then((data) => {
        fetchLogoList();

        if (logoList.length === 0) {
          handleLogoChange(data)();
        }

        toast.dismiss(id);
        toast.success("Logo uploaded successfully", {
          id: "logo-upload-success",
        });
      })
      .catch((err) => {
        toast.dismiss(id);
        toast.error(err.message, { id: "logo-upload-error" });
      })
      .finally(() => {
        setIsLoading(false);
      });
    event.target.value = null;
  };

  const handleModalChange = (isOpen: boolean) => {
    if (isOpen) {
      setDeleteStore((p) => ({ ...p, isPopupOpen: false, isSeconaryModalOpen: true }));
    } else {
      setDeleteStore(deleteInit);
    }
  };
  const handlePopupChange = (item?: any) => (isOpen: boolean) => {
    if (isOpen) {
      setDeleteStore((p) => ({ ...p, item, isPopupOpen: true }));
    } else {
      setDeleteStore(deleteInit);
    }
  };

  if (Array.isArray(embed.logoList) && embed.logoList.length === 0 && disableUpload) return null;

  return (
    <>
      <span className={`text-xs font-normal ${isDisabled ? "text-[#87878a]" : ""}`}>
        {heading ? heading : "Loading screen icon"}
        <span className="block text-[#87878a]">(180 * 48 px upto 720*192 px)</span>
      </span>
      <div className="flex flex-wrap gap-2.5 items-center pt-3.5">
        {logoList
          .map((item) => (
            <Popover key={item.path} isOpen={item.path === deleteStore?.item?.path && isDeleteOpen} placement="bottom" offset={20} showArrow>
              <PopoverTrigger className="relative">
                <span>
                  <Avatar
                    key={item.name}
                    className="hover:cursor-pointer w-11 h-11 overflow-hidden hover:scale-110 transition-all"
                    isBordered
                    color={currentLogoUrl == item.path ? "primary" : "default"}
                    onClick={handleLogoChange(item)}
                    radius="full"
                    fallback
                    src={item.path}
                  />
                  {!disableDelete && (
                    <span
                      onClick={() => handlePopupChange(item)(true)}
                      className="absolute top-0 right-0 translate-x-1  w-fit h-fit p-unit-xs bg-[#D7D7D7] rounded-full"
                    >
                      <Close width={8} height={8} stroke="#898989" />
                    </span>
                  )}
                </span>
              </PopoverTrigger>
              <PopoverContent className="rounded-small">
                <div className="flex flex-col gap-unit-xl py-unit-md text-sm">
                  <p>Are you sure you want to delete it?</p>
                  <div className="flex gap-unit-lg w-full justify-center items-center">
                    <Button
                      onClick={() => handlePopupChange(null)(false)}
                      fullWidth={false}
                      color="default"
                      size="sm"
                      className="py-unit-8 px-unit-2xl"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleLogoRemove}
                      fullWidth={false}
                      isLoading={deleteStore.isFetchingCount}
                      isDisabled={deleteStore.isFetchingCount}
                      color="danger"
                      size="sm"
                      className="py-unit-8 px-unit-2xl"
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          ))
          .concat(
            <span key="add-logo">
              <input
                type="file"
                accept="image/*"
                className="hidden"
                id="logo-upload"
                onChange={handleFileChange}
                disabled={isLoading || isDisabled}
                style={{ display: "none" }}
              />
              <Button className="w-11 h-11" radius="full" isDisabled={isDisabled || isLoading}>
                <Avatar
                  classNames={{
                    base: "cursor-pointer block min-w-11 min-h-11 bg-[#CCCCCC]",
                  }}
                  as="label"
                  htmlFor="logo-upload"
                  fallback={<PlusSolid className="w-5 h-5 text-default-500" fill="#FFFFFF" />}
                  showFallback
                />
              </Button>
            </span>
          )
          .slice(0, disableUpload ? logoList.length : maxLogoUploadLimit)}
      </div>
      <DeleteLogoModal
        canUnassign={Boolean(deleteStore.item) && currentLogoUrl == deleteStore.item.path}
        logoUseCount={deleteStore.logoUseCount}
        isOpen={isModalOpen}
        onOpenChange={handleModalChange}
        handleLogoRemove={handleLogoRemove}
      />
    </>
  );
};
