import { FC } from "react";
import { ModalContent, Modal, type ModalProps } from "@nextui-org/react";

interface CustomModalProps {
  children: any;
  isOpen: boolean;
  onOpenChange: any;
  size?: "sm" | "md" | "lg" | "xl" | "2xl" | "full" | "xs" | "3xl" | "4xl" | "5xl";
  backdrop?: "blur" | "transparent" | "opaque";
  placement?: "top-center" | "bottom-center" | "center" | "auto" | "top" | "bottom" | undefined;
  hideCloseButton?: boolean;
  radius?: ModalProps["radius"];
  className?: ModalProps["className"];
  classNames?: ModalProps["classNames"];
}

const defaultProps = {
  classNames: { closeButton: "z-[1010] right-unit-lg top-unit-lg", base: "!border-0" },
};
const MyModal: FC<CustomModalProps> = (props) => {
  return (
    <Modal
      isOpen={props.isOpen}
      size={props.size || "4xl"}
      onOpenChange={props.onOpenChange}
      placement={props.placement ?? "bottom-center"}
      backdrop={props.backdrop ?? "transparent"}
      shadow={"sm"}
      hideCloseButton={props.hideCloseButton}
      radius={props.radius}
      className={props.className}
      classNames={{ ...defaultProps.classNames, ...(props.classNames ?? {}) }}
    >
      <ModalContent className="bg-transparent overflow-hidden border-[3px] border-white flex flex-row">{(onClose) => props.children(onClose)}</ModalContent>
    </Modal>
  );
};

export { MyModal as Modal };
