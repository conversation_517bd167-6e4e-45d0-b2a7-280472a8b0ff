import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={28}
    height={28}
    viewBox="0 0 28 28"
    fill="none"
    {...props}
  >
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="m13.938 4.19 1.046 1.476C11.938 6.9 9.77 9.91 9.777 13.406c.007 2.97 1.588 5.582 3.937 7.045a.884.884 0 0 0 1.022-1.397 6.572 6.572 0 0 1-3.223-5.657c-.008-3.645 2.922-6.589 6.523-6.6a6.532 6.532 0 0 1 5.842 3.59.884.884 0 0 0 1.457-.943A8.356 8.356 0 0 0 21.06 5.64l1.065-1.515c.175-.266.221-.625.04-.89 0-.046-.85-1.954-.85-1.954a.876.876 0 0 0-.801-.531l-4.986.014a.878.878 0 0 0-.799.537l-.795 1.959s-.298.351.003.932Zm12.366 9.781c-.246 3.459-2.629 6.349-5.811 7.347l.307-.123a.878.878 0 0 0 .064-.025l-.064.025a.883.883 0 0 1-.847-1.521 6.576 6.576 0 0 0 4.619-5.81.884.884 0 0 1 1.732.107Zm-6.32-11.443.447 1.02-1.142 1.614a8.139 8.139 0 0 0-2.463-.002l-1.111-1.599.442-1.022 3.827-.011Z"
      clipRule="evenodd"
    />
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M7.758 11.957a6.766 6.766 0 1 0 6.07.929.902.902 0 0 1 1.052-1.467 8.57 8.57 0 1 1-7.688-1.175.902.902 0 1 1 .566 1.713Z"
      clipRule="evenodd"
    />
  </svg>
)
export default SvgComponent
