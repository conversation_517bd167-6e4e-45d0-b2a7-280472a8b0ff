import { nextui } from "@nextui-org/react";

const config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@nextui-org/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        inter: ["Inter-regular", "sans-serif"],
        mainSemiBold: ["Inter-semibold", "sans-serif"],
      },
      screens: {
        sm: "640px",
        // => @media (min-width: 640px) { ... }

        md: "768px",
        // => @media (min-width: 768px) { ... }

        lg: "1024px",
        // => @media (min-width: 1024px) { ... }

        xl: "1280px",
        // => @media (min-width: 1280px) { ... }

        "2xl": "1536px",
        "3xl": "1800px",
        // => @media (min-width: 1536px) { ... }
      },
      // colors: {
      //   bg: "#f0f0f0",
      // },
    },
  },
  darkMode: "class",
  plugins: [
    nextui({
      prefix: "mini-editor",
      themes: {
        light: {
          extend: "light",
          colors: {
            primary: {
              DEFAULT: "#6E73F2",
              foreground: "#ffffff",
            },
            default: {
              DEFAULT: "#e9e9e9",
              foreground: "#2e2e2e",
            },
            secondary: {
              DEFAULT: "#2E2E2E",
              foreground: "#fff",
            },
            danger: {
              DEFAULT: "#ef4444",
              foreground: "#fff",
            },
          },
          layout: {
            radius: {
              small: "10px",
              medium: "16px",
              large: "30px",
            },
            boxShadow: {
              small: "0px 0px 5px 0px rgb(0 0 0 / 0.01), 0px 2px 10px 0px rgb(0 0 0 / 0.00)",
              medium: " 0px 0px 15px 0px rgb(0 0 0 / 0.02), 0px 2px 30px 0px rgb(0 0 0 / 0.08)",
              large: " 0px 0px 30px 0px rgb(0 0 0 / 0.03), 0px 30px 60px 0px rgb(0 0 0 / 0.12)",
            },
            spacingUnit: 2,
            fontSize: {
              tiny: "11px",
              small: "16px",
              medium: "24px",
              large: "72px",
            },
            lineHeight: {
              tiny: "1rem", // text-tiny
              small: "1.4rem", // text-small
              medium: "1.5rem", // text-medium
              large: "3.75rem", // text-large
            },
            borderWidth: {
              small: "1px",
              medium: "2px",
              large: "3px",
            },
          },
        },
      },
    }),
  ],
};
export default config;
