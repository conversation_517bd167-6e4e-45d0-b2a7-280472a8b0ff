import { FC, useState, useCallback } from "react";
import {
  AssetExporterPlugin,
  CanvasSnipperPlugin,
  downloadBlob,
  GLTFDracoExportPlugin,
  timeout,
  ViewerApp,
} from "webgi";
import { Input } from "./Input";
import { Button } from "./Button";
import { Checkbox } from "@nextui-org/react";
import toast from "@minieditor/react-hot-toast";
import { useUi } from "./provider/UiProvider";
import { useProject } from "./provider/ProjectProvider";

interface GLbExportSettingsProps {
  viewer?: ViewerApp;
}

const GLbExportSettings: FC<GLbExportSettingsProps> = ({ viewer }) => {
  const [compress, setCompress] = useState(true);
  const [loading, setLoading] = useState(false);
  const { freeze, setFreeze , limits } = useUi();
  const { project } = useProject();

  const handleExport = useCallback(async () => {
    if (!viewer) {
      toast.error("Viewer not found");
      return;
    }
    setLoading(true);
    setFreeze(true);
    toast.loading("Exporting 3D file", { id: "exporting-3d-file" });
    const exporter = await viewer.getOrAddPlugin(AssetExporterPlugin);
    if (compress) {
      await viewer.getOrAddPlugin(GLTFDracoExportPlugin);
    }
    const blob = await exporter!.exportScene({
      name: project?.name ?? "scene",
      compress: compress,
      viewerConfig: true,
    });
    if (!blob) {
      toast.error("Failed to export 3D file");
      setLoading(false);
      setFreeze(false);
      return;
    }
    downloadBlob(blob, `${project?.name || "scene"}.glb`);
    setLoading(false);
    setFreeze(false);
    toast.success("Exported successfully", {
      id: "exporting-3d-file",
    });
  }, [viewer, compress, project]);

  return (
    <div className="flex flex-col gap-unit-lg w-full">
      <p className="text-primary font-mainSemiBold text-[13px] text-center">
        3D file
      </p>
      <Checkbox
        isDisabled={freeze}
        title="Compress"
        color="primary"
        size="sm"
        className=" !text-tiny font-main " 
        classNames={{label : "text-tiny text-default-600"}}
        isSelected={compress}
        onValueChange={setCompress}
        key={"compress"}
      >
        Compress
      </Checkbox>
      <Button
        disabled={loading || freeze}
        onClick={handleExport}
        name={"Download ." + "glb"}
        size="lg"
        color="primary"
        className="!text-tiny rounded-full !font-mainSemiBold"
      />
    </div>
  );
};

export { GLbExportSettings };
