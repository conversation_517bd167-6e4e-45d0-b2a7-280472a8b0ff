import { FC, useState, useCallback } from "react";
import { CanvasSnipperPlugin, downloadBlob, RendererUiPlugin, timeout, ViewerApp } from "webgi";
import { Input } from "./Input";
import Arrow from "./icons/Arrow";
import { Button } from "./Button";
import { Select, SelectItem } from "@nextui-org/react";
import toast from "@minieditor/react-hot-toast";
import { useUi } from "./provider/UiProvider";

interface SnapshotSettingsProps {
  viewer?: ViewerApp;
}

const SnapshotSettings: FC<SnapshotSettingsProps> = ({ viewer }) => {
  const [width, setWidth] = useState(1920);
  const [height, setHeight] = useState(1080);
  const [isWidthValid, setIsWidthValid] = useState(true);
  const [isHeightValid, setIsHeightValid] = useState(true);
  const [mimeType, setMimeType] = useState("png");
  const [loading, setLoading] = useState(false);
  const { freeze, setFreeze , limits } = useUi();

  const handleSnapshot = useCallback(async () => {
    if (!viewer) {
      toast.error("Viewer not found");
      return;
    }
    setLoading(true);
    setFreeze(true);
    toast.loading("Exporting snapshot", { id: "exporting-snapshot" });
    const snapper = await viewer.getOrAddPlugin(CanvasSnipperPlugin)!;

    const p = viewer.getPlugin(RendererUiPlugin);
    if(p) p.enabled = false;
    await viewer.doOnce('postFrame')
    viewer.setSize({ width, height });

    viewer.setDirty();

    await timeout(500);
    await viewer.doOnce('postFrame')
    const file = await snapper.getFile(`snapshot.${mimeType}`, { mimeType: "image/" + mimeType, waitForProgressive: true });
    if (file) downloadBlob(file, `snapshot.${mimeType}`);

    viewer.setSize();
    viewer.setDirty();

    if(p) p.enabled = true;
    setLoading(false);
    setFreeze(false);
    toast.success("Snapshot exported successfully", {
      id: "exporting-snapshot",
    });
  }, [viewer, width, height, mimeType]);

  return (
    <div className="flex flex-col gap-unit-lg w-full">
      <p className="text-primary font-mainSemiBold text-[13px]  text-center">
        Snapshot
      </p>
      <p className="font-inherit !text-tiny font-normal">Size</p>
      <div className="flex gap-unit-md">
        <Input
          isDisabled={freeze}
          onChange={setWidth as any}
          value={width.toString()}
          setIsValid={setIsWidthValid}
          max={limits?.snapshot?.maxResolution?.width}
          placeholder="1920"
          className="w-full"
          type="number"
          startContent={
            <div className="w-unit-xs -translate-x-0.5">
              <Arrow className="scale-[1.2]" />
            </div>
          }
        />
        <Input
          isDisabled={freeze}
          onChange={setHeight as any}
          value={height.toString()}
          setIsValid={setIsHeightValid}
          max={limits?.snapshot?.maxResolution?.height}
          placeholder="1080"
          className="w-full "
          type="number"
          startContent={
            <div className="w-unit-xs -translate-x-0.5">
              <Arrow className="scale-[1.2] rotate-90" />
            </div>
          }
        />
      </div>
      <Select
        isDisabled={freeze}
        disallowEmptySelection
        aria-label="mimeType"
        defaultSelectedKeys={["png"]}
        size="sm"
        onSelectionChange={(v: any) => setMimeType([...v][0])}
        classNames={{
          mainWrapper: "rounded-md",
          value: "text-tiny font-inter text-tiny  ",
          trigger:
            "bg-default-50 !h-[20px] !rounded-md border-default-300 border-1 ",
          popoverContent: "rounded-md !text-tiny border-1 border-default-300 px-0 ",
          
          listbox: "p-0 rounded-none [&>*>*]:rounded-none [&>*>*]:px-3 [&>*>*>*]:text-tiny [&>*>*>*]:font-main",
          listboxWrapper: "rounded-md rounded-none",
        }}
      >
        {(limits?.snapshot?.formats ?? ["png", "jpg", "webp"]).map((item) => (
          <SelectItem key={item}>{item}</SelectItem>
        ))}
      </Select>
      <Button
        disabled={!isWidthValid || !isHeightValid || loading || freeze}
        onClick={handleSnapshot}
        name={"Download ." + mimeType}
        size="lg"
        color="primary"
        className="!text-tiny rounded-full !font-mainSemiBold"
      />
    </div>
  );
};

export { SnapshotSettings };
