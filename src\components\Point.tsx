import { FC, CSSProperties } from "react";

interface HeroTextProps {
  className?: string;
  children?: any;
}

const Point: FC<HeroTextProps> = (props) => {
  return (
    <div className={`font-inter text-medium font-light  flex justify-start items-start text-left 	${props.className}`} style={{}}>
      <div className=" h-full mr-unit-lg md:mr-0">{<span className="top-0 md:hidden inline">•</span>}</div>
      <div>{props.children}</div>
      {/* <div className="w-[5px] h-[5px] bg-default-foreground rounded-full mr-unit-lg mt-[4px]"></div>• {props.children} */}
    </div>
  );
};

export { Point };
