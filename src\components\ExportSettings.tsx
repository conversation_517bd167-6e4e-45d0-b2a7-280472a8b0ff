import { FC } from "react";
import { AssetManagerPlugin, ViewerApp, downloadBlob, downloadFile } from "webgi";
import { SnapshotSettings } from "./SnapshotSettings";
import { VideoSettings } from "./VideoSettings";
import { Divider } from "@nextui-org/react";
import { GLbExportSettings } from "./GLbExportSettings";
import { IjewelStudioBanner } from "./IjewelStudioBanner";
import SceneSettings from "./SceneSettings";

interface ExportSettingsProps {
  viewer?: ViewerApp;
}

const ExportSettings: FC<ExportSettingsProps> = ({ viewer }) => {
  return (
    <div className="flex flex-col h-fit gap-unit-lg w-full">
      {/*<IjewelStudioBanner/>*/}
      <SnapshotSettings viewer={viewer} />
      <Divider className="my-unit-md" />
      <GLbExportSettings viewer={viewer} />
      <Divider className="my-unit-md" />
      <SceneSettings viewer={viewer} />    
      <Divider className="my-unit-md" />
      <VideoSettings viewer={viewer} />
    </div>
  );
};

export { ExportSettings };
