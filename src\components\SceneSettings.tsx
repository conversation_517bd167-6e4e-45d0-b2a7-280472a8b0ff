import toast from "@minieditor/react-hot-toast";
import React from "react";
import { AssetManagerPlugin, ViewerApp, downloadBlob } from "webgi";
import { Button } from "./Button";

interface SceneSettingProps {
  viewer?: ViewerApp;
}

export default function SceneSettings(props: SceneSettingProps) {
  const { viewer } = props;
  const [loading, setLoading] = React.useState(false);

  return (
    <div className="flex flex-col gap-unit-lg w-full">
      <div>
        <p className="text-primary font-mainSemiBold text-[13px] text-center">Scene Settings</p>
        <p className="text-secondary text-xs text-s">
          Export your setup, including all scene settings — HDRs, background, ground, and configurators.
        </p>
      </div>
      <Button
        disabled={loading}
        name={"Download ." + "vjson"}
        size="lg"
        color="primary"
        className="!text-tiny rounded-full !font-mainSemiBold"
        onClick={async () => {
          if (!viewer) return;
          setLoading(true);
          toast.loading("Exporting...", { id: "exporting-scene-config" });

          const json = viewer.getPlugin(AssetManagerPlugin)?.exportViewerConfig(false);
          if (json) downloadBlob(new Blob([JSON.stringify(json, null, 2)], { type: "application/json" }), "scene-config.vjson");
          else toast.error("Could not export config");

          toast.dismiss("exporting-scene-config");
          setLoading(false);
        }}
      />
    </div>
  );
}
