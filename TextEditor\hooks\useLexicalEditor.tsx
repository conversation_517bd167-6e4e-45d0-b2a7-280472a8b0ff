import { useState } from "react";
import { createEditor } from "lexical";
import { $generateHtmlFromNodes } from "@lexical/html"; // Assuming you have this package
import { type SerializedEditorState } from "lexical";
import { textEditorConfig } from "../TextEditor";

export interface LexicalEditorHooks {
  useLexicalEditorHtml: (initialJSON: SerializedEditorState) => {
    htmlContent: string;
    getHtmlFromEditor: () => void;
    setEditorState: (initialJSON: SerializedEditorState) => void;
  };
}

// Custom Hook
export function useLexicalEditorHtml() {
  const [htmlContent, setHtmlContent] = useState("");

  // Function to retrieve HTML from Lexical editor state
  const setEditorState = (json: SerializedEditorState) => {
    const editorTemp = createEditor({
      ...textEditorConfig,
      onError: console.error,
    });
    const state = editorTemp.parseEditorState(json);
    editorTemp.registerUpdateListener(({ editorState }) => {
      editorTemp.update(() => {
        const htmlOutput = $generateHtmlFromNodes(editorTemp, null);
        setHtmlContent(htmlOutput);
      });
    });
    editorTemp.setEditorState(state);
  };
  return { htmlContent, setEditorState };
}
