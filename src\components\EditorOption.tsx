import { FC, CSSProperties, useState, useEffect, useRef, useCallback } from "react";
import { Img } from "./Img";
import { Dropdown, DropdownItem, DropdownMenu, DropdownTrigger, Input, Spinner, Tooltip } from "@nextui-org/react";
import { PresetGroup } from "webgi";
import { motion } from "framer-motion";
import LockIcon from "./icons/Lock";
import toast from "@minieditor/react-hot-toast";
import OtherIcon from "./icons/Other";
import { useProject } from "./provider/ProjectProvider";
import ResetIcon from "./icons/Reset";
import DiamondIcon from "./icons/Diamond";
import EditIcon from "./icons/Edit";

interface IconProps {
  src?: string;
  name?: string;
  className?: string;
  group?: PresetGroup;
  onClick?: any;
  selected?: boolean;
  loading?: boolean;
  rect?: boolean;
  small?: boolean;
  index?: number;
  isLocked?: boolean;
  isLogin?: boolean;
  setShowSubscribeModal?: any;
  onRemove?: any;
  isPremium?: boolean;
  isBusiness?: boolean;
  editable?:boolean
  handleLabelChanged?: any;
  title?: string;
  isCustomAsset?: boolean;
}

const EditorOption: FC<IconProps> = (props) => {
  const ref = useRef<HTMLDivElement>(null);
  const [contextMenuOpen, setContextMenuOpen] = useState(false);

  const [title, setTitle] = useState(props.title || toPresentableName(props.name || "", props.isCustomAsset));
  const [isEditing, setIsEditing] = useState(false);
  const [toolTipOpen , setToolTipOpen] = useState(false)
  const handleStartEditing = () => {
    if(!props.editable) return;
    setIsEditing(true);
  };

  const handleChange = (e : any) => {
    setTitle(e.target.value);
  };

  const handleFinishEditing = () => {
    setIsEditing(false);
    props.handleLabelChanged && props.handleLabelChanged(title);
  };

  const handleKeyDown = (e : any) => {
    // For example, if user presses Enter, finish editing
    if (e.key === "Enter") {
      handleFinishEditing();
    }
  };

  useEffect(()=>{
    if(!props.editable)return;
    if(!toolTipOpen){
      setIsEditing(false)
    }
  },[toolTipOpen])

  function toPresentableName(filename: string, isCustomAsset?: boolean): string {
    // 1. Strip out any file extensions and content after semicolon
    const cleanName = filename.split("/").pop()!.split(".")[0].split(";")[0];

    let titleCased = cleanName
    // Delete everything after the last underscore
    let lastUnderscoreIndex = titleCased.lastIndexOf('_');
    // lastUnderscoreIndex-- // Remove the underscore itself
    if (lastUnderscoreIndex > 0) {
      titleCased = titleCased.substring(0, lastUnderscoreIndex);
    }

    // 2. Convert snake_case or kebab-case to Title Case
    titleCased = titleCased
      .split(/[-_]/)
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");

    // 3. Remove unnecessary common prefixes or suffixes
    const replacements: [RegExp, string][] = [
      [/^Gradient\s?Radial\s?/, ""],
      [/^Gradient\s?Linear\s?/, ""],
      [/^Env\s?Metal\s?/, "Metal "],
      [/^Env\s?Gem\s?/, "Gem "],
      [/^Env\s?/, ""],
      [/^\d+k?$/, ""], // Removes standalone numbers
      [/^Background\s?\d+$/, "Bg"],
      [/^G\s?\d+$/, "Ground"],
      [/^Views\s?\d+$/, "View"],
      [/^Metal\s?Diamond$/, "Diamond Metal"],
      [/^Gold\s?Ceramic$/, "Ceramic Gold"],
      [/([a-zA-Z])gold/, "$1 Gold"],
      // [/^Metal\s?/, ""],
      [/Hammeredlinear/, "Hammer"],
      //remove numbers at the beginning
      [/^\d+/, ""],
    ];

 

    let presentableName = titleCased;
    for (let [pattern, replacement] of replacements) {
      presentableName = presentableName.replace(pattern, replacement);
    }
    if(isCustomAsset) presentableName += " (Custom)";
    return presentableName.trim();
  }

  const handleClicked = useCallback(() => {
    if (props.isLocked && !props.isPremium && !props.isBusiness) {
      toast(
      <div className="flex items-center bg-white rounded-small">
        <LockIcon className="mr-unit-xl w-unit-xl text-warning" />
        <span>Please login to unlock this {props.isPremium ? "premium" : "free"} asset</span>
      </div>
      );
      return;
    }
    
    if (props.isLocked) {
      let msg = "Please subscribe to unlock this asset";
      if (!props.isLogin) msg = "Please log in and subscribe to unlock this asset";

      toast.error(msg, { id: "subscribe" });
      if (props.isLogin) props.setShowSubscribeModal?.(true);
      return;
    }
    props.onClick();
  }, [props.onClick, props.isLocked]);

  const handleTabRightClick = (event: any) => {
    event.preventDefault();
    setContextMenuOpen(true);
  };
  // const isOriginalAsset = Object.values(project.sceneConfig || {}).find((asset : string) => props.name?.includes(asset|| "")) ? true : false;

  return (
    <div
      // initial={{ opacity: 0 }}
      // animate={{
      //   opacity: 1,
      //   transition: { delay: props.index ? props.index * 0.005 : 0 },
      // }}
      // exit={{ opacity: 0 }}
      id={props.selected ? "selectedOption" : ""}
      ref={ref}
      onClick={handleClicked}
      onContextMenu={(event) => handleTabRightClick(event)}
      className={
        "flex justify-center h-fit items-center flex-col cursor-pointer relative transition-none " +
        props.className +
        (props.rect ? "w-full shrink-0 max-w-[78px] " : " w-[50px] ")
      }
    >
      <Tooltip
        color={"secondary"}
        closeDelay={isEditing? 3000 : 500}
        shouldCloseOnBlur={props.editable? !isEditing : undefined}
        onClick={handleStartEditing} 
        key={props.name}
        // onClose={()=> setIsEditing(false)}
        onBlur={()=> {setToolTipOpen(false)}}
        isOpen={toolTipOpen}
        onOpenChange={(v) => setToolTipOpen(isEditing || v)}
        content={
          (isEditing && props.editable) ? (
            <Input
              autoFocus
              value={title}
              onChange={handleChange}
              onBlur={handleFinishEditing}
              onKeyDown={handleKeyDown}
              variant="underlined"
              size="sm"
              color="secondary"
              className="h-unit-2xl"
              classNames={{
                label: "hidden",
                input:"h-unit-2xl font-normal  placeholder:text-tiny placeholder:font-normal text-tiny  ",
                innerWrapper: [" font-bold"],
                inputWrapper: "py-0 px-0 rounded-md h-unit-2xl"
              }}
              // You can style or size the Input as needed
            />
          ) : (
            <div className="flex justify-center items-center gap-1">
              {title} {props.editable && <EditIcon className="h-3 cursor-pointer"/>}
            </div>
          )
        }
        delay={500}
        className="text-tiny"
      >
        <div
          
        className={
            "relative flex h-fit flex-col pointer-events-auto transition-none " +
            (props.rect ? "w-full shrink-0 max-w-[100px] " : " max-w-[50px] ")
          }
        >
          <div className="relative h-fit  ">
            {props.isLocked && (
              <div
                className={
                  "absolute top-0  z-50 left-0 w-full h-full bg-black/40 flex justify-center items-center border-2 flex-col gap-unit-xs   " +
                  (props.rect
                    ? " rounded-small w-full  shrink-0 aspect-square "
                    : " rounded-full ")
                }
              >
                <LockIcon className="text-white" />
                {(props.isPremium || props.isBusiness) && (
                  !props.rect ? <DiamondIcon className="absolute bottom-0 right-0 z-10" fill={props.isPremium ? "premium" : "business"}/>
                  : <p className={`py-[2px] px-[5px] text-[8px] font-bold font 
                    ${props.isPremium ? "bg-primary" : "bg-gradient-to-r from-[#FFE0A4] from-33% to-[#B5821F]"}
                     leading-3 text-white rounded-medium z-50`}>{props.isPremium ? "PREMIUM" : "BUSINESS"}</p>
                )}
              </div>
            )}
            {props.src ? props.src === "none" ? 
              (
                <span
                  className={`m-[2px] flex justify-center items-center cursor-pointer bg-[#CCCCCC!important] border-2 transition-all duration-300 
                    ${props.rect ? " w-full max-w-[70px] h-[70px] rounded-md " : " w-[46px] h-[46px] rounded-full"} 
                    ${props.selected ? " border border-primary hover:border-primary " : " border-transparent "}
                    `}
                >
                  <ResetIcon className={props.rect ? "w-7 h-7" : "w-6 h-6"} fill="#FFFFFF" />
                </span>
              )
            : <Img
              // key={props.src + props.name }
              src={props.src}
              className={
                "  object-fill border-2 transition-none hover:border-default-300 transition-all duration-300  opacity-100 aspect-square " +
                (props.selected
                  ? " border border-primary hover:border-primary"
                  : " border-transparent") +
                (props.loading ? " opacity-50" : "") +
                (props.rect
                  ? " rounded-small w-full  shrink-0 aspect-square "
                  : " rounded-full ") +
                (props.small || props.rect ? " " : " w-full min-w-unit-3xl min-h-unit-3xl  ")
              }
            ></Img>: <OtherIcon className={`w-full min-w-unit-3xl min-h-unit-3xl p-unit-sm ${props.selected ? "text-primary" : "text-default-400"}` } />}
            {props.loading && (
              <Spinner
                className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
                color="secondary"
              />
            )}
          </div>
          {/* {!props.rect && props.name && (
            <p
              aria-label={toPresentableName(props.name)}
              className="text-tiny font-normal text-default-foreground mt-unit-lg  text-center"
            >
              {toPresentableName(props.name)}
            </p>
          )} */}
        </div>
      </Tooltip>
      {props.onRemove && (
        <Dropdown
          placement="top"
          offset={50}
          isOpen={contextMenuOpen}
          onOpenChange={setContextMenuOpen}
          closeOnSelect
          triggerRef={ref as any}
        >
          <DropdownTrigger>
            <div></div>
          </DropdownTrigger>
          <DropdownMenu
            aria-label="Tab actions"
            onAction={(key) => {
              if (key === "delete") {
                props.onRemove();
              }
              setContextMenuOpen(false);
            }}
          >
            <DropdownItem key="delete" className="text-danger" color="danger">
              Delete
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      )}
    </div>
    // </Tooltip>
  );
};

export { EditorOption };
