import { useState, useEffect, useCallback, useRef } from "react";
import { DiamondMaterial, DiamondPlugin, DropzonePlugin, getUrlQueryParam, LoadingScreenPlugin, PresetLibraryPlugin, ViewerApp } from "webgi";
import { ConfiguratorEditor } from "./components/ConfiguratorEditor";
import { EditorTabs } from "./components/EditorTabs";
import { clearViewer, disposeViewer, setupViewer } from "./components/viewer/app";
import toast, { Toaster, useToasterStore } from "@minieditor/react-hot-toast";
import logo from "./assets/ijewel_logo.svg?inline";
import Webgi from "./components/viewer/Webgi";
import { useProject } from "./components/provider/ProjectProvider";
import { IjewelEditorPlugin } from "./components/viewer/IjewelEditorPlugin";
import { EditorSettings, Project } from "./main";
import ProjectSettings from "./components/ProjectSettings";
import { usePrompt } from "./components/Prompt";
import { motion } from "framer-motion";
import { MenuButton } from "./components/MenuButton";
import { Header } from "./components/Header";
import {ModesTabs} from './components/ModesTabs.tsx'
import { useUi } from "./components/provider/UiProvider.tsx";
import { freeUserLimits, getBrandingDefaultSettings, getEmbedDefaultSettings, premiumUserLimits } from "./components/utils/limits.ts";
import { ConfirmToast } from "./components/ConfirmToast.tsx";
import { UiButtons } from "./components/UiButtons.tsx";
import { DropZoneBackground } from "./components/DropZoneBackground.tsx";
import { DefaultEnvMapUrl } from "./components/utils/urls.ts";
import { RhinoClient } from "./components/RhinoClient.tsx";

interface EditorProps {
  project: Project;
  editorSettings?: EditorSettings;
}

export default function Editor(props: EditorProps) {
  const [viewer, setViewer] = useState<ViewerApp>();
  const [hasEdited, setHasEdited] = useState(false);
  const [ijewelEditorPlugin, setIjewelEditorPlugin] = useState<IjewelEditorPlugin>();
  const { project, setProject } = useProject();
  const { embed, branding, setLimits, showDefaultMaterialsPrompt, showCacheKeysWarnnin, showDefaultMaterialConfigurator } = useUi();
  const { showPrompt, PromptComponent } = usePrompt();
  const {toasts}  = useToasterStore()
  const [showDropzone, setShowDropzone] = useState(false);
  const [rhinoClinetActive, setRhinoClinetActive] = useState(false);
  const modelLoaded = useRef(false);

  useEffect(() => {
    setProject(props.project);
  }, [props.project]);

  useEffect(() => {
    if (!props.editorSettings || !viewer) return;

    //set limits
    if(props.editorSettings.limits){
      setLimits(props.editorSettings.limits);
    }else if (props.editorSettings.isPremium){
      setLimits(premiumUserLimits);
    }else{
      setLimits(freeUserLimits);
    }

    const editorPlugin = viewer.getPluginByType<IjewelEditorPlugin>("IjewelEditorPlugin")!;
    if(!editorPlugin) return;
    const prev = editorPlugin!.editorSettings;

    //apply new asset files if changed
    //todo: this looks hacky and should be refactored
    if (
      (prev?.assetFiles !== props.editorSettings.assetFiles && props.editorSettings.assetFiles && props.editorSettings.assetFiles.length && project.basePath)
    ) {
      editorPlugin.init(project.basePath, props.editorSettings)
      if(props.editorSettings?.useDefaultConfig){
        const loader = viewer.getPluginByType<LoadingScreenPlugin>("LoadingScreenPlugin")!;
        if(!editorPlugin.isMaterialMapping() && editorPlugin.isModelLoaded()){
          loader.show()
          // loader.loadingTextHeader = "Loading default configuration";
          editorPlugin.applyDefaultConfig().then(() => {
            loader.hide()
          });
        }
      }
    }
  }, [props.editorSettings , viewer]);

  useEffect(() => {
    if (viewer) {
      return;
    }
    const useRhino = getUrlQueryParam("rhino");
    if (useRhino !== null) {
      setRhinoClinetActive(true);
    }
    setupViewer(props.project, props.editorSettings).then(async (viewer) => {
      window.dispatchEvent(new CustomEvent("webgi-viewer-ready", { detail: { viewer } }));
      let loader = viewer.getPlugin("LoadingScreenPlugin") as LoadingScreenPlugin;
      loader.logoImage = props.project.logo || logo;

      //show user confirmation if they want to clear all scene settings
      viewer.scene.addEventListener("sceneUpdate", (e) => {
        if(!e.hierarchyChanged) return;
        if(viewer.scene.modelRoot.children.length !== 0 || !modelLoaded.current) return;
        ConfirmToast("Do you want to clear all scene settings?", "clear-scene", () => {
          clearViewer(viewer);
          toast.dismiss("clear-scene");
          props.editorSettings?.onSceneCleared?.();
        });
      });

      if (props.project.modelUrl) {
        //load model
        await viewer.load(props.project.modelUrl, {
          autoCenter: true,
          autoScale: true,
        });
        modelLoaded.current = viewer.scene.modelRoot.children.length !== 0;


        viewer.renderEnabled = true;
        let editorPlugin = await viewer.getPluginByType<IjewelEditorPlugin>("IjewelEditorPlugin")!;
        setIjewelEditorPlugin(editorPlugin);
        editorPlugin.fromJSON(props.project);
        await editorPlugin.applyConfig();

        if(!editorPlugin.isMaterialConfigurator()){
          setTimeout(() => {
            showDefaultMaterialConfigurator(viewer);
          }, 3000);
        }
        
        setProject({ ...props.project });
        embed.setSettings({ ...getEmbedDefaultSettings(), ...(props.project.embedSettings ?? {}) });
        branding.setSettings({ ...getBrandingDefaultSettings(), ...(props.project.brandingSettings ?? {}) });
        embed.setEmbedPosterUrl(props.project.embedPosterUrl ?? null)
      } else {
        await viewer.getPluginByType<IjewelEditorPlugin>("IjewelEditorPlugin")!.fromJSON(props.project);
        setProject({ ...props.project });
      }


      showCacheKeysWarnnin(viewer);
      setViewer(viewer);
    });
    return () => {
      disposeViewer(viewer!);
    };

  }, []);

  useEffect(() => {
    if (!viewer) return;

    //drop
    viewer.getPluginByType<DropzonePlugin>("Dropzone")!.addEventListener("drop", async (e) => {
      if (!viewer.scene.environment) {
        await viewer.setEnvironmentMap(DefaultEnvMapUrl); // from asset packs v5
      }
      const plugin = viewer.getPluginByType<IjewelEditorPlugin>("IjewelEditorPlugin")!;
      modelLoaded.current = plugin.isModelLoaded()
      if(project.sceneConfig){
        viewer.getPluginByType<IjewelEditorPlugin>("IjewelEditorPlugin")!.applyConfig();
      } else if(!plugin.isMaterialMapping() && plugin.isModelLoaded()){
        setTimeout(() => {
          showDefaultMaterialsPrompt(viewer);
        }, 2000);
      }
      setHasEdited(true);
      viewer.renderEnabled = true;
    });

    viewer.scene.addEventListener("materialChanged", (e) => {
      //check if we have diamond material and no diamond env map
      let flag = false;
      const diamoondMap = viewer.getPlugin(DiamondPlugin)?.envMap;
      if (diamoondMap) return;
      viewer.traverseSceneObjects((obj) => {
        if (obj.material && obj.material instanceof DiamondMaterial ) {
          flag = true;
        }
      });

      if (flag) {
        ConfirmToast(`Do you want to use a separate environment map for Gems and Metals in the scene to enhance the lighting?`, "diamond-env-map", () => {
          const plugin = viewer.getPlugin(IjewelEditorPlugin);
          const sceneConfig = {
            GemEnvironment : plugin?.defaults.sceneConfig.GemEnvironment,
            Environment : plugin?.defaults.sceneConfig.Environment,
            type : "PresetLibraryPlugin"
           };
          viewer.getPlugin(PresetLibraryPlugin)?.fromJSON(sceneConfig);
        });
      }

      

    });
  }, [viewer]);

  const [firstLoad, setFirstLoad] = useState(true);
  useEffect(() => {
    if (!viewer) return;
    const editor = viewer.getPluginByType<IjewelEditorPlugin>("IjewelEditorPlugin")!;
    editor.fromJSON(project);

    if (firstLoad && !props.editorSettings?.fullEditor) {
      setFirstLoad(false);
    } else {
      setHasEdited(true);
    }
  }, [viewer , project]);

  useEffect(() => {
    if (!viewer) return;
    const editor = viewer.getPluginByType<IjewelEditorPlugin>("IjewelEditorPlugin")!;
    editor.hasEdited = hasEdited;
    const handleBeforeUnload = (event: any) => {
      const editor = viewer.getPluginByType<IjewelEditorPlugin>("IjewelEditorPlugin")!;
      if (editor.hasEdited) {
        event.preventDefault();
        event.returnValue = "You have unsaved changes! Are you sure you want to leave?";
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [hasEdited, viewer]);

  useEffect(() => {
    (window as any).prompt = showPrompt;
    // window.prompt("test");
  }, []);

  const showProjectSettings = useCallback(() => {
    document.getElementById("webgi-mini-editor-canvas-container")?.classList.remove("ml-2");
    document.getElementById("project-settings")?.classList.remove("w-0");
    document.getElementById("project-settings")?.classList.add("w-auto");
    document.getElementById("project-settings")?.classList.remove("hidden");
    document.getElementById("menu-button")?.classList.add("-left-unit-9xl");
    document.getElementById("menu-button")?.classList.remove("left-4");
  }, []);

  const reverseLayout = props.editorSettings?.fullEditor;

  return (
    <div
      id="mini-editor-wrapper"
      className={`w-screen h-full !font-inter !font-normal text-small flex flex-col items-center gap-4 py-4 overflow-hidden bg-[#f0f0f0]`}
    >
      <PromptComponent />
      <Toaster toasterId={"mini-editor"} reverseOrder containerStyle={{top : 80}} toastOptions={{toasterId: "mini-editor"}}/>
      {rhinoClinetActive && <RhinoClient viewer={viewer}/>}
      {props.editorSettings?.showHeader && <Header viewer={viewer} editorSettings={props.editorSettings} hasEdited={hasEdited} setHasEdited={setHasEdited} />}
      <div
          className={`flex relative w-full h-full items-start overflow-hidden ${reverseLayout ? "flex-row-reverse" : ""} 
            ${!props.editorSettings?.fullEditor && !props.editorSettings?.showProjectSettings ? "pl-unit-xl" : ""}`}>
        {!props.editorSettings?.fullEditor && props.editorSettings?.showProjectSettings && <MenuButton showProjectSettings={showProjectSettings} viewer={viewer}/>}
        {!props.editorSettings?.fullEditor && props.editorSettings?.showProjectSettings && <div id="project-settings"
                                 className="mx-4 z-10 rounded-small h-full flex flex-col transition-width duration-1000">
          <ProjectSettings
              project={project}
              viewer={viewer}
              editorSettings={props.editorSettings}
              setHasEdited={setHasEdited}
              // setProject={setProject}
              hasEdited={hasEdited}
          />
        </div>}
        {props.editorSettings?.fullEditor && <ModesTabs
            editorSettings={props.editorSettings}
            viewer={viewer}
            reverseLayout={reverseLayout}
            // materialConfig={project.materialConfig}
            // sceneConfig={project.sceneConfig}
        />}
        <motion.div id="canvas-wrapper" className="flex-1 h-full overflow-hidden rounded-small relative">
          {props.editorSettings?.showDropzone && <DropZoneBackground viewer={viewer} editorSettings={props.editorSettings} isModelLoadedRef={modelLoaded} />}
          <Webgi/>
          <ConfiguratorEditor
              setHasEdited={setHasEdited}
              viewer={viewer}
              isPremium={props.editorSettings?.isPremium}
              labels={props.editorSettings?.labels}
              setShowSubscribeModal={props.editorSettings?.onSubscribe}
              isLogin={props.editorSettings?.isLogin}
              fullEditor={props.editorSettings?.fullEditor}
          />
          {!props.editorSettings?.fullEditor && <UiButtons
            viewer={viewer}
            cameraConfig={project.cameraConfig}
            enableZoom={false}
            hideQuality={false}
            showShareButton={false}
            // className={"md:flex absolute"}
          />}
        </motion.div>

        <EditorTabs
            editorSettings={props.editorSettings}
            viewer={viewer}
            setHasEdited={setHasEdited}
            setShowSubscribeModal={props.editorSettings?.onSubscribe}
            reverseLayout={reverseLayout}
            // materialConfig={project.materialConfig}
            // sceneConfig={project.sceneConfig}
        />
      </div>
    </div>
  );
}