import React, { useCallback, useEffect, useState } from "react";
import { EditorOption } from "./EditorOption";
import { MaterialPresetPlugin } from "webgi";
import toast from "@minieditor/react-hot-toast";
import ED_SETTING from "../types";
import { HideMaterialOrPresetItemWrapper, UploadMaterialOrPresetButton } from "./utils/shared";
import { moveCustomInFront } from "./utils/functions";

interface MaterialOption {
  path: string;
  icon: string;
}

interface MaterialOptionsProps {
  selectedMesh: any;
  setHasEdited: any;
  viewer: any;
  selectedMaterialTab: string;
  isPremium?: boolean;
  isBusiness?: boolean;
  setShowSubscribeModal?: any;
  isLogin?: boolean;
  customAssets?: ED_SETTING.CUSTOM_ASSET;
  customAssetsStore: Record<string, ED_SETTING.CUSTOM_ASSET_ITEM[]> | null;
  setCustomAssetsStore: React.Dispatch<React.SetStateAction<Record<string, ED_SETTING.CUSTOM_ASSET_ITEM[]> | null>>;
}

const MaterialOptions: React.FC<MaterialOptionsProps> = ({
  selectedMesh,
  setHasEdited,
  viewer,
  selectedMaterialTab,
  isPremium,
  isBusiness,
  setShowSubscribeModal,
  isLogin,
  customAssets,
  customAssetsStore,
  setCustomAssetsStore,
}) => {
  const [lastClicked, setLastClicked] = useState<string | null>(null);
  const [materialPresetPlugin, setMaterialPresetPlugin] = useState<MaterialPresetPlugin>();
  const [materialPresets, setMaterialPresets] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedHideAsset, setSelectedHideAsset] = useState(null);

  useEffect(() => {
    if (!viewer) return;
    setMaterialPresetPlugin(viewer.getPlugin(MaterialPresetPlugin));
  }, []);

  const handleAddCustomAsset = async ({ file, assetType }: { file: File; assetType: string }) => {
    if (!materialPresetPlugin) return;

    const newMaterial = await customAssets?.handleUploadAsset?.({
      assetFile: file,
      assetType,
      materialCategory: selectedMaterialTab,
    });
    if (!newMaterial || !newMaterial.path) {
      throw new Error("Failed to upload asset");
    }

    const allMaterialMap = materialPresetPlugin.presets;
    allMaterialMap[selectedMaterialTab].push(newMaterial);

    // setMaterialPresets((p) => [...p, newMaterial]);
    setCustomAssetsStore((p) => ({ ...p, [assetType]: [...(p?.[assetType] ?? []), newMaterial] }));
  };
  const handleHideAsset = async (asset: any, assetType: string, isSelected: boolean) => {
    if (asset.isCustom !== true) return;
    if (!materialPresetPlugin) return;

    if (isSelected) {
      setSelectedHideAsset(null);
      toast.error("Before deleting this asset, please either deselect it or replace it with another option.", { id: "asset-unselect-error" });
      return;
    }

    let id = toast.loading("Deleting Asset...", {
      id: "asset-delete-start",
    });
    return customAssets
      ?.handleArchiveAsset?.({ asset, isArchive: true })
      .then(() => {
        const allMaterialMap = materialPresetPlugin.presets;
        const materialCategory = asset.meta?.asset_data?.materialCategory ?? selectedMaterialTab
        allMaterialMap[materialCategory] = allMaterialMap[materialCategory].filter((item: any) => item.path !== asset.path);
        setMaterialPresets(allMaterialMap[materialCategory]);
        setCustomAssetsStore((p) => {
          let prev = p ?? {};
          return {
            ...prev,
            [assetType]: (prev[assetType] ?? [])?.filter((item: any) => item.path !== asset.path),
          };
        });

        setSelectedHideAsset(null);
        toast.dismiss(id);
        toast.success("Asset deleted successfully", {
          id: "asset-delete-success",
        });
      })
      .catch((err: any) => {
        toast.dismiss(id);
        toast.error(err.message, { id: "asset-delete-error" });
      });
  };

  useEffect(() => {
    if (!viewer || !materialPresetPlugin || selectedMaterialTab == "hidden") return;
    // setPresets([]);
    // setGroup(undefined);
    let pms = materialPresetPlugin.presets;
    if (pms && pms[selectedMaterialTab]) setMaterialPresets(pms[selectedMaterialTab]);
  }, [selectedMaterialTab, materialPresetPlugin]);

  const handleMaterialPresetClick = useCallback(
    (path: string) => {
      path += "?localhost";
      setHasEdited(true);
      if (!selectedMesh || !materialPresetPlugin) return;
      //for stl files, the materials don't have a name, so we need to add a name
      if (!selectedMesh.material?.name) {
        selectedMesh.material.name = selectedMesh.name || (selectedMaterialTab + "_" + Math.random() * 1000)
      }
      setLoading(true);
      materialPresetPlugin.addMapping(selectedMesh?.material?.name!, path , true)
      setLoading(false);

    },
    [selectedMesh, materialPresetPlugin]
  );

  const isSelected = useCallback(
    (option: MaterialOption) => {
      if (!selectedMesh || !materialPresetPlugin) return false;
      return (
        materialPresetPlugin.mapping
          .find((m) => m.name === selectedMesh?.material?.name)
          ?.path?.includes?.(option.path) || false
      );
    },
    [selectedMesh, materialPresetPlugin]
  );

  const isLoading = useCallback(
    (option: MaterialOption) => {
      if (!selectedMesh || !materialPresetPlugin) return false;
      return lastClicked
        ? lastClicked === option.path
        : materialPresetPlugin.mapping
            .find((m) => m.name === selectedMesh?.material?.name)
            ?.path?.includes?.(option.path);
    },
    [selectedMesh, materialPresetPlugin, loading]
  );

  return (
    <>
      <UploadMaterialOrPresetButton
        handleAddCustomAsset={handleAddCustomAsset}
        assetType={"Material"}
        isLogin={isLogin}
        isBusiness={isBusiness}
        isPremium={isPremium}
        setShowSubscribeModal={setShowSubscribeModal}
        customAssetsStore={customAssetsStore}
      />
      {moveCustomInFront(materialPresets).map((option, i) => (
        <HideMaterialOrPresetItemWrapper
          key={option.path}
          asset={option}
          isSelected={isSelected(option)}
          selectedPresetTab={"Material"}
          onConfirm={handleHideAsset}
          selectedHideAsset={selectedHideAsset}
          setSelectedHideAsset={setSelectedHideAsset}
        >
          <EditorOption
            onClick={() => {
              handleMaterialPresetClick(option.path);
              setLastClicked(option.path);
            }}
            selected={isSelected(option)}
            key={option.path + "materials" + i + (option.isPremium ? "premium" : "")}
            src={option.icon}
            loading={isLoading(option) && loading}
            name={option.path}
            // className="w-unit-4xl h-unit-5xl"
            isLocked={(option.isPremium && !isPremium) || (!isLogin && i > 3)}
            isPremium={option.isPremium}
            isLogin={isLogin}
            index={i}
            setShowSubscribeModal={setShowSubscribeModal}
          />
        </HideMaterialOrPresetItemWrapper>
      ))}
    </>
  );
};

export default MaterialOptions;

export const addCustomMaterialPresetsPlgn = (viewer: any, customAssetsStore: Record<string, ED_SETTING.CUSTOM_ASSET_ITEM[]>) => {
  const materialPresetPluginInit = viewer.getPlugin(MaterialPresetPlugin);
  const allMaterialMap = materialPresetPluginInit.presets;

  // create material preset group of custom asset.
  const customMaterialPresets = customAssetsStore["Material"]?.reduce((acc: any, item: any) => {
    const category = item.meta?.asset_data?.materialCategory || "other";
    if (!acc[category]) {
      acc[category] = [];
    } 
    // to not added twice we check existing presets group. not effective boz of iterate, for now it was suggeested.
    if(!allMaterialMap[category] ?.find((preset: any) => preset.path === item.path)) acc[category].push(item);

    return acc;
  }, {});
  
  // add custom preset group to preset plgn preset reference 
  Object.keys(customMaterialPresets).forEach((key) => {
    if (!allMaterialMap[key]) {
      allMaterialMap[key] = [];
    }
    allMaterialMap[key].push(...customMaterialPresets[key]);
  });

  return allMaterialMap;
};
