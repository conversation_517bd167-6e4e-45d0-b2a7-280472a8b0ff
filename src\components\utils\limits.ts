export interface ExportLimits {
    snapshot: {
      maxResolution: {
        width: number;
        height: number;
      };
      formats: string[];
      dailyExportLimit: number;
    };
    fileExport: {
      compressOptionAvailable: boolean;
      dailyExportLimit: number;
    };
    video: {
      maxResolution: {
        width: number;
        height: number;
      };
      maxFPS: number;
      maxDurationInSeconds: number;
      dailyExportLimit: number;
    };
    customAsset: {
      uploadPresetLimit: number;
    };
  }
  
export const freeUserLimits: ExportLimits = {
  snapshot: {
    maxResolution: {
      width: 1920,
      height: 1080,
    },
    formats: ["png"],
    dailyExportLimit: 5,
  },
  fileExport: {
    compressOptionAvailable: true,
    dailyExportLimit: 3,
  },
  video: {
    maxResolution: {
      width: 1280,
      height: 720,
    },
    maxFPS: 30,
    maxDurationInSeconds: 10,
    dailyExportLimit: 2,
  },
  customAsset: {
    uploadPresetLimit: 0
  }
};

export const premiumUserLimits: ExportLimits = {
  snapshot: {
    maxResolution: {
      width: 3840,
      height: 2160,
    },
    formats: ["png", "jpeg", "webp"],
    dailyExportLimit: Infinity,
  },
  fileExport: {
    compressOptionAvailable: true,
    dailyExportLimit: Infinity,
  },
  video: {
    maxResolution: {
      width: 3840,
      height: 2160,
    },
    maxFPS: 60,
    maxDurationInSeconds: 60,
    dailyExportLimit: Infinity,
  },
  customAsset: {
    uploadPresetLimit: 5  },
};

export const businessUserLimits: ExportLimits = {
  snapshot: {
    maxResolution: {
      width: 3840,
      height: 2160,
    },
    formats: ["png", "jpeg", "webp"],
    dailyExportLimit: Infinity,
  },
  fileExport: {
    compressOptionAvailable: true,
    dailyExportLimit: Infinity,
  },
  video: {
    maxResolution: {
      width: 3840,
      height: 2160,
    },
    maxFPS: 60,
    maxDurationInSeconds: 60,
    dailyExportLimit: Infinity,
  },
  customAsset: {
    uploadPresetLimit: 100,
  },
};

export const getEmbedDefaultSettings = (override = {}) => {
  return {
    isTitle: true,
    isResponsive: true,
    width: "1280",
    height: "720",
    posterWidth: "1280",
    posterHeight: "720",
    isRemoveHologram: false,
    isRemoveLogo: false,
    isRemoveLogoLink: false,
    isRemoveLoadingBgImg: true,
    isShowPrompt: true,
    isTurntableAnimation: false,
    isAutoplay: false,
    isTransparentBackground: false,
    isConfigurator: true,
    isEnabledZoom: true,
    isShare: true,
    isShowModelInfo: false,
    isQuality: true,
    isResetView: true,
    isRotateCamera: true,
    isPlayCameraViews: true,
    isPlayAnimations: true,
    isFitObject: true,
    isFullScreen: true,
    ...override,
  };
};  

export const getBrandingDefaultSettings = (override = {}) => {
  return {
    enable: false,
    positionX: 0.5,
    positionY: 0,
    maxWidth: 720,
    maxHeight: 192,
    objectFit: "contain" as const,
    showLoadingScreenLogo: true,
    ...override,
  };
};