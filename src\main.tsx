// import React from "react";
// import React from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import Editor from "./Editor.tsx";
import {NextUIProvider} from "@nextui-org/react";
import {ProjectProvider} from "./components/provider/ProjectProvider.ts";
import {EulerOrder, getUrlQueryParam, TPreset} from "webgi";
import {UiProvider} from "./components/provider/UiProvider.tsx";
import "./assets/Inter-Regular.woff2"
import "./assets/Inter-SemiBold.woff2"
import {ExportLimits} from "./components/utils/limits.ts";
import config from "../test-config.json"
import v4Assets from "../fullEditor/assets.json";
import {PresetGroupTab, PresetMatTab} from "./components/EditorTabs.tsx";
import type EDSETTING from "./types.ts"

export interface Project {
  modelUrl?: string;
  posterUrl?: string;
  // deprecated
  model_url?: string;
  // deprecated
  poster_url?: string;
  logo?: string | null;
  name?: string;
  description?: string;
  textEditorState?: any;
  basePath?: string;
  sceneConfig?: { [key: string]: any };
  cameraConfig?: any;
  materialConfig?: any;
  configuratorConfig?: any;
  plugins?: any;
  // sceneSettings?: string; // vjson url
  tags?: string[];
  category?: string;
  version?: string;
  slug?:string;
  shouldApplyModelStageConfig?: boolean;
  currentPose?: { position: number[]; rotation: [number, number, number, EulerOrder] } | null;
  embedSettings?: EDSETTING.EMBED_SETTING;
  brandingSettings?: EDSETTING.BRANDING_SETTING;
  embedPosterUrl?: string;
}
export interface EditorSettings {
  allow3dExport?: boolean;
  beforeExportCallback?: any;
  labels?: {
    metal: string[];
    gem: string[];
    ceramic: string[];
    matt: string[];
  };
  hideLogoInput?: boolean;
  hidePoster?: boolean;
  hideSaveButton?: boolean;
  previewLink?: string;
  autoGroundTonemap?: boolean;
  saveCallback?: (project: Project, saveView?: boolean, export3d?: boolean) => void;
  diamondKey?: string;
  showCategory?: boolean;
  showTags?: boolean;
  showExport?: boolean;
  showAdvanced?: boolean;
  presetLib?: any;
  materialLib?: any;
  assetLib?: any;
  premiumPresetLib?: any;
  premiumMaterialLib?: any;
  isPremium?: boolean;
  isBusiness?: boolean;
  showHeader?: boolean;
  tutorialLink?: string;
  logo?: string;
  categories?: string[];
  fullEditor?: boolean;
  assetFiles?: DriveFile[];
  isLogin?: boolean;
  token?: string;
  useDefaultConfig?: boolean;
  onSubscribe?: (isOpen?: boolean, type?: 'premium' | 'business') => void;
  limits?: ExportLimits,
  onPosterUpdate?: (dataUrl : string , cameraConfig:any) => Promise<any>;
  showDropzone?: boolean;
  showEmbedding?:boolean
  embed?: EDSETTING.EMBED;
  customAssets?: EDSETTING.CUSTOM_ASSET;
  baseName?: string; // base name of new drive instances to load assets from there
  showProjectSettings?: boolean;
  showWelcome?: boolean;
  showSampleModels ?: boolean;
  onSceneCleared?: () => void;
  maxLogoUploadLimit?: number;
  showBrandingSetting?: boolean;
  tryonPath?: string;
}

export type Preset = {
  basePath: string;
  assets: TPreset[];
};

export interface AssetsLibrary {
  presets: Record<PresetGroupTab, Preset>;
  materials: Record<PresetMatTab, Preset>;
  version?: string;
  [key: string]: any;
}

export interface DriveFile { id: string; name: string; path: string; file: string; thumb: string; tags: string , deleted: boolean; }

class MiniEditor {
  editorSettings: EditorSettings = {}
  root: ReactDOM.Root;

  constructor(container: HTMLElement, project: Project, editorSettings?: EditorSettings) {
    // this.editorSettings = editorSettings || {};
    this.root = ReactDOM.createRoot(container);

    if(project.model_url !== undefined){
      project.modelUrl = project.model_url;
      console.warn("model_url is deprecated, please use modelUrl instead");
    }
    if(project.poster_url !== undefined){
      project.posterUrl = project.poster_url;
      console.warn("poster_url is deprecated, please use posterUrl instead");
    }

    if((project as any).presetLib !== undefined || (project as any).materialLib !== undefined){
      this.editorSettings.presetLib = (project as any).presetLib;
      this.editorSettings.materialLib = (project as any).materialLib;
      console.warn(
        "presetLib and materialLib have been moved to editorSettings, please use editorSettings.presetLib and editorSettings.materialLib instead"
      );
    }
    this.render(project , editorSettings);
  }

  render(project: Project , editorSettings?: EditorSettings) {
    if(editorSettings){
      const assetFiles = editorSettings.assetFiles?.length ? editorSettings.assetFiles : this.editorSettings.assetFiles;
      //reload assets if token changes
      if(editorSettings.token !== this.editorSettings.token && editorSettings.token){
        loadAssets({
          baseName: editorSettings.baseName || "packs",
          project: project,
          editorOptions: { 
            token: editorSettings.token, 
            assetFiles: assetFiles
          }
        }).then((files) => {
          if (!files) return;
          this.render(project, { assetFiles: files });
        });
      }
      this.editorSettings = {...this.editorSettings, ...editorSettings , assetFiles}
    }
    this.root.render(
      <NextUIProvider >
        <ProjectProvider initialValue={project}>
          <UiProvider>
          <Editor
            project={project}
            editorSettings={{
              ...this.editorSettings,
              hideSaveButton: this.editorSettings?.hideSaveButton || this.editorSettings?.fullEditor,
              hidePoster: this.editorSettings?.hidePoster ?? true,
              autoGroundTonemap: this.editorSettings?.autoGroundTonemap ?? true,
              showCategory: this.editorSettings?.showCategory ?? true,
              showTags: this.editorSettings?.showTags ?? true,
              showExport: this.editorSettings?.showExport ?? true,
              showAdvanced: this.editorSettings?.showAdvanced ?? true,
              showHeader: this.editorSettings?.showHeader ?? true,
              isLogin: this.editorSettings?.isLogin ?? true,
              useDefaultConfig: this.editorSettings?.useDefaultConfig ?? true,
              showEmbedding: this.editorSettings.showEmbedding ?? false,
              showProjectSettings: this.editorSettings.fullEditor ? false : (this.editorSettings?.showProjectSettings ?? true),
              showWelcome: this.editorSettings?.showWelcome ?? true,
              showSampleModels: this.editorSettings?.showSampleModels ?? true,
            }}
          />
          </UiProvider>
        </ProjectProvider>
      </NextUIProvider>
    );
  }
}

export interface IjewelDriveConfig {
  model?: string;
  basePath: string;
  ijewelEditorOptions?: EditorSettings;
  useMetaConfig?: boolean;
  useFolderConfig?: boolean;
  useDefaultConfig?: boolean;
}

async function setupIjewelDriveEditor(config: IjewelDriveConfig, target?: HTMLElement) {
  // Set default values for the new parameters
  const useMetaConfig = config.useMetaConfig !== undefined ? config.useMetaConfig : true;
  const useFolderConfig = config.useFolderConfig !== undefined ? config.useFolderConfig : false;
  const useDefaultConfig = config.useDefaultConfig !== undefined ? config.useDefaultConfig : true;

  //for cors
  // let f = window.fetch;
  // window.fetch = async (request: any) => {
  //   if (typeof request === "string") request = new Request(request);

  //   if (request.url && request.url.includes(config.basePath)) {
  //     let url = new URL(request.url);
  //     url.searchParams.append("origin", window.location.origin); // for caching
  //     request = new Request(url, {
  //       headers: request.headers,
  //       method: request.method,
  //       body: request.body,
  //     });
  //   }
  //   const response = await f(request);

  //   return response;
  // };

  if (!config.model) {
    config.model = atob(getUrlQueryParam("path") || "");
    // config.model = config.model.replace(/#/g, '%23');
  }
  if(!config.ijewelEditorOptions){
    config.ijewelEditorOptions = {};
  }
  config.ijewelEditorOptions.isPremium = true;
  config.ijewelEditorOptions.isLogin = true;
  //replace all # with %23
  config.model = encodeURI(config.model).replace(/#/g, '%23');
  const directory = config.basePath + config.model.split("/").slice(0, -1).join("/");
  const filename = config.model.split("/").pop();
  const metaFilePath = directory + "/.meta/" + filename + ".json";
  const folderConfigPath = directory + "/default.json";
  const defaultConfigPath = config.basePath + "/ijewel/default.json";

  async function loadConfig(filePath: string) {
    const response = await fetch(filePath + "?t=" + Date.now()).catch(() => ({ status: 500 }));
    if (response.status === 200) {
      const blob = await (response as Response).blob();
      const c = JSON.parse(await blob.text());
      c.modelUrl = config.basePath + config.model;
      c.basePath = config.basePath + "/ijewel/";
      delete c["model_url"]; // deprecated
      return c;
    }
    return null;
  }

  let configLoaded = false;

  if (!useMetaConfig && !useFolderConfig && !useDefaultConfig) {
    new MiniEditor(target || document.getElementById("root")!, config, config.ijewelEditorOptions);
  }

  if (useMetaConfig) {
    const metaConfig = await loadConfig(metaFilePath);
    if (metaConfig) {
      new MiniEditor(
        target || document.getElementById("root")!,
        metaConfig,
        config.ijewelEditorOptions
      );
      configLoaded = true;
    }
  }

  if (!configLoaded && useFolderConfig) {
    const folderConfig = await loadConfig(folderConfigPath);
    if (folderConfig) {
      new MiniEditor(
        target || document.getElementById("root")!,
        folderConfig,
        config.ijewelEditorOptions
      );
      configLoaded = true;
    }
  }

  if (!configLoaded && useDefaultConfig) {
    const defaultConfig = await loadConfig(defaultConfigPath);
    if (defaultConfig) {
      new MiniEditor(
        target || document.getElementById("root")!,
        defaultConfig,
        config.ijewelEditorOptions
      );
    }
  }
}



interface IjewelDriveConfig2 {
  baseName: string;
  target?: HTMLElement;
  project?: Project;
  editorOptions?: EditorSettings;
  assetsPath?: string;
}

const setupIjewelDriveEditor2 = (config: IjewelDriveConfig2) => {
  let filesApi =  `https://${config.baseName}.ijewel3d.com/files/`;

  const editor = new MiniEditor(config.target || document.getElementById("root")!, {
    basePath: filesApi,
    ...(config.project || {}),
  }, {
    allow3dExport: true,
    fullEditor: false,
    hidePoster: false,
    hideLogoInput: true,
    baseName: config.baseName,
    ...config.editorOptions,
  });

  // if(!config.editorOptions?.token) return editor;

  // loadAssets({...config}).then((files) => {
  //   if(!files) return;
  //   editor.render({
  //     basePath: filesApi,
  //     ...config.project,
  //   }, {
  //     assetFiles: files,
  //   })
  // });




  return editor;
}

const loadAssets = async (config: IjewelDriveConfig2): Promise<any> => {
  const baseApiUrl: string = `https://${config.baseName}.ijewel3d.com/api/raw/v1/`;

  let files = config.editorOptions?.assetFiles;
  try {
    let assetsPath = null
    if(config.assetsPath){

      assetsPath = config.assetsPath;
      
    }else if(files && files.length > 0){
      //get version from the files array
      const parent = files[0].path.split('/')[1]
      if(parent){
        assetsPath = parent;
      }
    }else if(config.project?.version){
        //get version from project
        const version = config.project.version;
        const queryParams = new URLSearchParams({ select: "id,name,path", where: `name='${version}' & path='/'` });
        const response = await fetch(baseApiUrl + "files/select?" + queryParams.toString(), {
          headers: {
            Authorization: `${config.editorOptions?.token}`,
          },
        });
        const versionData = await response.json();
        if (!versionData || versionData.length === 0) {
          console.warn("Version not found, using default assets path");
          assetsPath = null;
        } else {
          const versionId = versionData[0].id;
          assetsPath = versionId;
        }
    } 
    
    if(!assetsPath){
      //get version set in drive config
      const configQueryParams = new URLSearchParams({ select: "id,val" });
      const driveConfigResponse = await fetch(baseApiUrl + "drive_config/select?" + configQueryParams.toString());
      const driveConfig = await driveConfigResponse.json();
      if (!driveConfig) throw new Error("Failed to fetch drive config");
      assetsPath = driveConfig?.find((c: any) => c.id === "assets-path")?.val;
    }

    if(!assetsPath) throw new Error('assets-path not found in drive config');

    const queryParams = new URLSearchParams({ select: "id,name,path,thumb,file,tags,deleted", where: `path~'%${assetsPath}%'` });

    const response = await fetch(baseApiUrl + "files/select?" + queryParams.toString(), {
      headers: {
        Authorization: `${config.editorOptions?.token}`,
      },
    });
    files = await response.json();
    if(files && files.length > 0){
      //sort by name
      files = files.filter((f: DriveFile) => !f.deleted);
      files.sort((a: DriveFile, b: DriveFile) => a.name.localeCompare(b.name));
    }

  } catch (error) {
    console.error("Failed to load assets", error);
  }
  return files;
};


// @ts-ignore
if (process.env.NODE_ENV === "development") {
  //new drive
  let e = setupIjewelDriveEditor2({
    baseName: "packs",
    project: {
      modelUrl: "https://cors-proxy.r2cache.com/https://assets.demodrive.ijewel3d.com/client1/2rings.glb",
      ...(config as any),
      version: "v6",
      // "basePath":"https://packs.ijewel3d.com/files/",
      // "basePath":"https://assets.ijewel.design/v1/",

      // Branding logo - settings
      logo:'https://cdn1.ijewel.design/95ccca39fb87509e3e752896be93c0ecfb34feb4/682b1e00-4b4e/custom-asset/logo/H004_1747656192444.png',
      brandingSettings: {
        enable: false,
        objectFit: "fill",
        maxWidth: 180,
        minHeight: 48,
        positionX: 0.5,
        positionY: 0.5,
        showLoadingScreenLogo: true
      },
    },
    editorOptions: {
      isLogin: true,
      fullEditor: true,
      isBusiness: true,
      isPremium: true,
      showDropzone: true,
      showProjectSettings: false,
      showWelcome: false,
      diamondKey: "JRB4Y43278FDX3AQ2KY4CPH3GY564ZRR-JCTNR2ZXAQ",
      assetFiles: v4Assets as any[],
      logo: "https://playground.ijewel3d.com/playground-logo.svg",
      showEmbedding: true,
      // customAssets:{
      //   initialAssets:{},
      //   async handleUploadAsset({ assetFile, assetType }) {
      //     return new Promise((resolve, reject) => {
      //       resolve({ assetFile, assetType })
      //     })
      //   },
      //   async handleArchiveAsset(data) {
      //     console.log(data);
      //   }
      // },
      embed : {
        // baseurl: baseEmbed ,
        // previewBaseurl: baseEmbed + "/preview",
        // handleUploadLogo: handleUploadLogo,
        // handleProjectLogoUpdate: handleProjectLogoUpdate,
        // getProjectCountByLogoUrl: getProjectCountByLogoUrl,
        // maxLogoUpload: 10,
        // logoList: logoList,
        enabled: false,
        warnningMessage : "This project is private and cannot be embedded. Consider making it Public or Unlisted"
      },
      showBrandingSetting:true,
    },
  });





  // const proxyUrl = "https://cors-proxy.r2cache.com/";
  // new MiniEditor(document.getElementById("root")!, {
  //   model_url: proxyUrl + "https://assets.demodrive.ijewel3d.com/client1/2rings.glb",
  //   basePath: proxyUrl + "https://assets.demodrive.ijewel3d.com/ijewel/",
  // } , {
  //   hideLogoInput: true,
  //   hidePoster: false,
  //   previewLink: "google.com",
  //   allow3dExport:true,
  //   fullEditor: true,
  //   // isPremium: true,
  // });

  // const basePath = "https://assets.veynou.ijewel3d.com";

  // setupIjewelDriveEditor(
  //   {
  //     basePath: proxyUrl + basePath,
  //     // model: "/main-website/engagementring-configurator.glb",
  //     useMetaConfig: true,
  //     useFolderConfig: true,
  //     useDefaultConfig: true,
  //     ijewelEditorOptions: {
  //       hidePoster: false,
  //       hideLogoInput: true,
  //       autoGroundTonemap: true,
  //     },
  //   },
  //   document.getElementById("root")!
  // );
}

export { MiniEditor, setupIjewelDriveEditor , setupIjewelDriveEditor2 };
export type { EDSETTING }
