import { But<PERSON> } from "@nextui-org/react";
import { FC } from "react";
// import {  } from "./Icon";
import MenuIcon from "./icons/Menu";
import { ViewerApp } from "webgi";
interface MenuButtonProps {
  showProjectSettings: any;
  viewer?: ViewerApp;
}

const MenuButton: FC<MenuButtonProps> = (props) => {

  return (
    <Button
      id="menu-button"
      variant="solid"
      aria-label="Menu"
      size="lg"
      color="default"
      className="absolute z-10 -left-unit-9xl top-unit-xl h-unit-3xl transition-all"
      startContent={
        <div className="w-unit-2xl overflow-hidden">
          {/* fdsfsdf */}
          <MenuIcon className="w-full " />
          {/* <img src={menuIcon} alt="menu" className="w-full h-full text-white !mix-blend-difference" /> */}
        </div>
      }
      onClick={props.showProjectSettings}
    ></Button>
  );
};

export { MenuButton };
