import { AViewerPlugin, serialize, TonemapPlugin, GroundPlugin, ViewerApp, UiObjectConfig, SRGBColorSpace, Color, SSBevelPlugin } from "webgi";

export class SimpleBackgroundEnvUiPlugin2 extends AViewerPlugin<""> {
  public static readonly PluginType = "SimpleBackgroundEnvUiPlugin2";
  enabled = true;
  private _sceneEnvironmentRotation: number | null = null;
  private _tonemapGround = true;
  private _tonemapBackground = true;
  private _ssbevelEnabled = false;
  private _ssbevelIntensity: number | null = null;
  public _cache = true;
  autoGroundTonemap;

  constructor(autoGroundTonemap: boolean) {
    super();
    this.autoGroundTonemap = autoGroundTonemap;

    this.uiConfig = {
      label: "Background / Environment",
      type: "folder",
      expanded: false,
      limitedUi: true,
      children: [
        // {
        //   label: "Color",
        //   type: "color",
        //   inlinePicker: true,
        //   property: [this, "sceneBackgroundColor"],
        //   limitedUi: true,
        // },
        {
          type: "slider",
          label: "Env Rotation",
          property: [this, "sceneEnvironmentRotation"],
          bounds: [0, Math.PI * 2],
          stepSize: Math.PI / 180,
          limitedUi: true,
          onChange: (data) => {
            console.log(data, "rotation");
          },
        },
        {
          type: "slider",
          label: "Env Intensity",
          property: [this, "sceneEnvironmentIntensity"],
          bounds: [0, 4],
          stepSize: 0.01,
          limitedUi: true,
        },
        {
          type: "checkbox",
          label: "Fixed Env Direction",
          property: [this, "sceneEnvironmentFixedDirection"],
        },
        {
          type: "checkbox",
          label: "Tonemap Background",
          property: [this, "tonemapBackground"],
        }, 
        {
          type: "checkbox",
          label: "Enable Cache",
          property: [this, "cache"],
          onChange: this.changeCache,
        },
        {
          label: "Plugin",
          type: "folder",
          expanded: false,
          limitedUi: true,
          children: [
            {
              type: "text",
              label: "Plugins",
              className: "text-sm font-semibold text-center mt-unit-2xl",
            },
            {
              type: "checkbox",
              label: "SSBevel",
              property: [this, "ssbevelEnabled"],
            },
            {
              type: "text",
              label: "Enabling SSBevel may slow down the performance.",
              hidden: () => !this.ssbevelEnabled,
              className: "mt-2 bg-[#fdf8e3] text-default-foreground border-[#f8eab1] border-[1px] p-2 rounded-small text-xs w-full",
            },
            {
              type: "slider",
              label: "SSBevel Intensity",
              hidden: () => !this.ssbevelEnabled,
              property: [this, "ssbevelIntensity"],
              bounds: [0, 8],
              stepSize: 0.01,
              limitedUi: true,
              onChange: (data) => {
                // console.log(data, "ssbavl");
              },
            },
          ],
        },
      ],
    };
  }

  private _savedBg = false;
  // @serialize()
  // get sceneBackgroundColor() {
  //   const bg = this._viewer?.scene.backgroundColor?.getHex(SRGBColorSpace);
  //   if (bg !== undefined) this._savedBg = true;
  //   return bg || 0;
  // }
  // set sceneBackgroundColor(v) {
  //   if (!this._viewer) return;
  //   if (!this._savedBg) return;
  //   if (!this._viewer.scene.backgroundColor) this._viewer.scene.backgroundColor = new Color();
  //   this._viewer.scene.backgroundColor?.setHex(v);
  //   this._viewer.scene.setDirty();
  // }
  @serialize()
  get sceneEnvironmentRotation() {
    return this.viewer?.scene.environment?.rotation ?? 0;
  }
  set sceneEnvironmentRotation(v) {
    if (this._sceneEnvironmentRotation === undefined && v === 0) return; //skip initial value
    this._sceneEnvironmentRotation = v;
    // console.log("rotation", v ,this.viewer?.scene.environment?.rotation);
    const env = this._viewer?.scene.environment;
    if (env) {
      env.rotation = v;
      this._viewer?.scene.setDirty();
    }
    this.uiConfig.uiRefresh?.("postFrame", true);
  }
  @serialize()
  get sceneEnvironmentIntensity() {
    return this._viewer?.scene.envMapIntensity ?? 1;
  }
  set sceneEnvironmentIntensity(v) {
    this._viewer?.scene && (this._viewer.scene.envMapIntensity = v);
    this.uiConfig.uiRefresh?.("postFrame", true);
  }

  @serialize()
  get sceneEnvironmentFixedDirection() {
    return this._viewer?.scene.fixedEnvMapDirection ?? false;
  }
  set sceneEnvironmentFixedDirection(v) {
    this._viewer?.scene && (this._viewer.scene.fixedEnvMapDirection = v);
    this.uiConfig.uiRefresh?.("postFrame", true);
  }

  @serialize()
  get tonemapBackground() {
    return (this._viewer!.getPlugin(TonemapPlugin)?.config?.tonemapBackground as boolean) || false;
  }
  set tonemapBackground(v) {
    this._tonemapBackground = v;
    this._viewer?.getPlugin(TonemapPlugin) && (this._viewer!.getPlugin(TonemapPlugin)!.config!.tonemapBackground = v);
    if (this.autoGroundTonemap) {
      this.tonemapGround = v;
    }
    this.uiConfig.uiRefresh?.("postFrame", true);
  }

  @serialize()
  get tonemapGround() {
    return (this._viewer!.getPlugin(GroundPlugin)?.tonemapGround as boolean) || false;
  }
  set tonemapGround(v) {
    this._tonemapGround = this.autoGroundTonemap ? this.tonemapBackground : v;
    this._viewer?.getPlugin(GroundPlugin) && (this._viewer!.getPlugin(GroundPlugin)!.tonemapGround = this._tonemapGround);
    this.uiConfig.uiRefresh?.("postFrame", true);
  }

  async onAdded(viewer: ViewerApp) {
    await super.onAdded(viewer);
    // this.bgUIConfig.value = '#' + (viewer.scene.backgroundColor?.getHexString(SRGBColorSpace) || '000000')
    // viewer.scene.addEventListener("backgroundChanged", () => {
    //   this._viewer?.getPlugin(TonemapPlugin) &&
    //     (this._viewer!.getPlugin(TonemapPlugin)!.config!.tonemapBackground = this.tonemapBackground);

    //   this.uiConfig.uiRefresh?.("postFrame", true);
    // });
    viewer.scene.addEventListener("environmentChanged", () => {
      this.uiConfig.uiRefresh?.("postFrame", true);
      const env = this._viewer?.scene.environment;
      if (env) {
        // this.sceneEnvironmentRotation = env.rotation;
        // this._viewer?.scene.setDirty();
      }
    });

    // auto ground tonemap
    const ground = viewer.getPlugin(GroundPlugin);
    ground?.addEventListener("deserialize", () => {
      const ground = viewer.getPlugin(GroundPlugin)!;
      ground!.tonemapGround = viewer.getPlugin(TonemapPlugin)?.config?.tonemapBackground ?? false;
      this.uiConfig.uiRefresh?.("postFrame", true);
      // console.log("tonemapGround", ground!.tonemapGround);
    });
  }

  get cache() {
    return this._cache;
  }
  set cache(v) {
    this._cache = v;
    this.uiConfig.uiRefresh?.("postFrame", true);
  }
  changeCache(checked: boolean) {
    if (checked) {
      let url = new URL(window.location.href);
      url.searchParams.delete("cache");
      window.location.href = url.toString();
    } else {
      const url = new URL(window.location.href);
      url.searchParams.set("cache", "false");
      window.location.href = url.toString();
    }
  }

  @serialize()
  get ssbevelEnabled() {
    return this._ssbevelEnabled;
  }
  set ssbevelEnabled(v) {
    this._ssbevelEnabled = v;
    const ssbevel = this._viewer?.getPlugin(SSBevelPlugin);
    if (ssbevel && ssbevel.pass) {
      ssbevel.enabled = v;
      ssbevel.pass.passObject.sceneBevel = v;
      ssbevel.pass.passObject.setDirty();
      ssbevel.setDirty();
    }
    this.uiConfig.uiRefresh?.("postFrame", true);
  }

  @serialize()
  get ssbevelIntensity() {
    return this._ssbevelIntensity;
  }
  set ssbevelIntensity(v) {
    this._ssbevelIntensity = v;
    const ssbevel = this._viewer?.getPlugin(SSBevelPlugin);

    if (ssbevel && ssbevel.pass) {
      ssbevel.pass.passObject.sceneBevelRadius = v ?? 0;
      ssbevel.pass.passObject.setDirty();
      ssbevel.setDirty();
    }
    this.uiConfig.uiRefresh?.("postFrame", true);
  }

  uiConfig: UiObjectConfig = {};
}
