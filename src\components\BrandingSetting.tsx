import { FC, useState, useCallback, useEffect, useRef } from "react";
import { Input } from "./Input";
import Arrow from "./icons/Arrow";
import { useUi } from "./provider/UiProvider";
import { EditorSettings, Project } from "../main";
import { Divider, Select, SelectItem } from "@nextui-org/react";
import { useProject } from "./provider/ProjectProvider";
import { Help, ScreenLoadingLogo, SwitchController } from "./utils/shared";
import { getBrandingDefaultSettings } from "./utils/limits";
import { UpgradeToBusinessButton } from "./UpgradeButton";
import toast from "@minieditor/react-hot-toast";
import ED_SETTING from "../types";

interface BrandingSettingProps {
  editorSettings?: EditorSettings;
  project?: Project;
}

const defaultSetting = getBrandingDefaultSettings();
const padding = 10;
const BrandingSetting: FC<BrandingSettingProps> = (props: BrandingSettingProps) => {
  const { editorSettings, project } = props;
  const { onSubscribe: setShowSubscribeModal, isBusiness } = editorSettings ?? {};
  const { branding, embed } = useUi();
  const [isPreview, setIsPreview] = useState(false);
  const { setProject } = useProject();
  const brandingImageRef = useRef<NonNullable<HTMLImageElement>>();
  const canvasContainerRef = useRef<NonNullable<HTMLElement>>();
  const isDisabled = !isBusiness;

  const removeAllElements = useCallback(() => {
    // const backdrop = document.getElementById("branding-preview-backdrop");
    // if (backdrop) {
    //   backdrop.remove();
    // }
    const brandingImg = document.getElementById("branding-preview");
    if (brandingImg) {
      brandingImg.remove();
    }
  }, [document]);

  useEffect(() => {
    if (editorSettings?.showBrandingSetting && !embed.logoList && editorSettings.isLogin) {
      editorSettings?.embed?.getlogoList?.().then((data) => {
        embed.setLogoList(data);
      });
    }
  }, [editorSettings?.showBrandingSetting, embed.logoList, editorSettings?.isLogin]);

  const handleUpdateStore = (field: string, value: any) => {
    branding.setSettings((p) => ({ ...p, [field]: value }));
  };
  useEffect(() => {
    setProject((prev) => ({ ...prev, brandingSettings: branding.settings }));
  }, [branding.settings]);

  const getPosition = useCallback(
    ({ settings: { positionX, positionY } }: ED_SETTING.EMBED_SETTING) => {
      if (!brandingImageRef.current || !canvasContainerRef.current) return { left: 0, top: 0 };

      const brandingImg = brandingImageRef.current;
      const canvasContainer = canvasContainerRef.current;

      const brandingWidth = brandingImg.width;
      const brandingHeight = brandingImg.height;
      const canvasWidth = canvasContainer.clientWidth;
      const canvasHeight = canvasContainer.clientHeight;
      const offSetX = positionX * canvasContainer.clientWidth;
      const offSetY = positionY * canvasContainer.clientHeight;

      // Keep logo within container bounds
      const left = Math.max(padding, Math.min(canvasWidth - padding - brandingWidth * positionX, offSetX - brandingWidth * positionX));
      const top = Math.max(padding, Math.min(canvasHeight - padding - brandingHeight * positionY, offSetY - brandingHeight * positionY));
      return { left, top };
    },
    [branding.settings.positionX, branding.settings.positionY, brandingImageRef, canvasContainerRef]
  );

  useEffect(() => {
    if (isPreview) {
      const canvasContainer = document.getElementById("canvas-wrapper");
      if (!canvasContainer) return;

      canvasContainerRef.current = canvasContainer;
      // let backdrop = document.getElementById("branding-preview-backdrop");
      // if (!backdrop) {
      //   backdrop = document.createElement("div");
      //   canvasContainer.appendChild(backdrop);
      // }
      // backdrop.style.position = "absolute";
      // backdrop.style.left = "0";
      // backdrop.style.top = "0";
      // backdrop.style.width = "100%";
      // backdrop.style.height = "100%";
      // backdrop.style.zIndex = "5";
      // backdrop.id = "branding-preview-backdrop";

      let brandingImg = document.createElement("img");
      brandingImg.src = project?.logo || "";
      brandingImg.id = "branding-preview";
      brandingImg.style.position = "absolute";
      brandingImg.style.pointerEvents = "none";
      brandingImg.style.objectPosition = "center";
      brandingImg.style.minWidth = `180px`;
      brandingImg.style.minHeight = `48px`;
      brandingImg.style.objectFit = branding.settings.objectFit ?? "contain";
      brandingImg.style.maxWidth = branding.settings.maxWidth ? `${branding.settings.maxWidth}px` : "720px";
      brandingImg.style.maxHeight = branding.settings.maxHeight ? `${branding.settings.maxHeight}px` : "192px";

      const { left, top } = getPosition(branding);
      brandingImg.style.left = `${left}px}`;
      brandingImg.style.top = `${top}px`;
      brandingImg.style.zIndex = "10";
      canvasContainer.appendChild(brandingImg); //append to canvas wrapper
      brandingImageRef.current = brandingImg as HTMLImageElement;
    } else {
      removeAllElements();
    }

    return () => {
      removeAllElements();
    };
  }, [isPreview, project?.logo]);

  useEffect(() => {
    if (isPreview && brandingImageRef.current && canvasContainerRef.current) {
      const brandingImg = brandingImageRef.current;

      const { left, top } = getPosition(branding);
      brandingImg.style.left = `${left}px`;
      brandingImg.style.top = `${top}px`;
      brandingImg.style.maxWidth = `${branding.settings.maxWidth ?? defaultSetting.maxWidth}px`;
      brandingImg.style.maxHeight = `${branding.settings.maxHeight ?? defaultSetting.maxHeight}px`;
      brandingImg.style.objectFit = branding.settings.objectFit ?? "contain";
    }
  }, [branding, isPreview, brandingImageRef, canvasContainerRef, project?.logo]);
  return (
    <div className={`px-2 pb-8 flex flex-col w-full select-none`}>
      {!editorSettings?.isLogin && (
        <div className="bg-[#fdf8e3] text-default-foreground border-[#f8eab1] border-[1px] p-2 rounded-small mb-2 text-xs w-full">
          Please login to access this feature
        </div>
      )}

      {editorSettings?.isLogin && !isBusiness && (
        <div className="pb-4 flex justify-center">
          <UpgradeToBusinessButton
            onClick={() => {
              if (!editorSettings?.isLogin) {
                toast.error("Please login to upgrade to business plan.");
                return;
              }
              setShowSubscribeModal?.(true, "business");
            }}
          />
        </div>
      )}

      <div className={`${isBusiness ? "" : "opacity-40 pointer-events-none"}`}>
        <ScreenLoadingLogo
          isDisabled={isDisabled}
          getLogoList={editorSettings?.embed?.getlogoList}
          handleUploadLogo={editorSettings?.embed?.handleUploadLogo}
          handleProjectLogoUpdate={editorSettings?.embed?.handleProjectLogoUpdate}
          getProjectCountByLogoUrl={editorSettings?.embed?.getProjectCountByLogoUrl}
          currentLogoUrl={project?.logo}
          maxLogoUploadLimit={editorSettings?.maxLogoUploadLimit}
          logoUrlField="logo"
          heading="Branding Logo"
        />

        <SwitchController
          isDisabled={isDisabled}
          field="enable"
          value={branding.settings.enable}
          onChange={handleUpdateStore}
          title="Show logo"
          helpMessage="If enabled, the branding logo will be displayed in the iJewel Viewer."
        />
        <SwitchController
          isDisabled={isDisabled}
          field="showLoadingScreenLogo"
          value={branding.settings.showLoadingScreenLogo}
          onChange={handleUpdateStore}
          title="Loading screen logo"
          helpMessage="If enabled, the branding logo will be displayed on the loading screen."
        />

        <Divider className="mt-3 mb-4" />
        <p className="text-primary font-mainSemiBold text-[13px]  text-center">Logo Layout</p>

        <SwitchController
          isDisabled={isDisabled}
          field="isPreview"
          value={isPreview}
          onChange={(_, value) => setIsPreview(value)}
          title="Preview"
          helpMessage="Enabling this will show a preview and allow changes to the settings below."
        />

        <div className={`py-3 space-y-2 ${isDisabled ? "text-[#87878a]" : "cursor-pointer"}`}>
          <p className="inline-flex items-center gap-unit-md font-inherit !text-tiny font-normal">
            Fit <Help content={"Adjusts how the branding logo fits within its container."} />
          </p>
          <Select
            isDisabled={!isPreview || isDisabled}
            disallowEmptySelection
            aria-label="fit"
            defaultSelectedKeys={[defaultSetting.objectFit]}
            size="sm"
            name="fit"
            selectedKeys={[branding.settings.objectFit ?? defaultSetting.objectFit]}
            onChange={(e) => {
              handleUpdateStore("objectFit", e.target.value);
            }}
            classNames={{
              mainWrapper: "rounded-md",
              value: "text-tiny font-inter text-tiny  ",
              trigger: "bg-default-50 !h-[20px] !rounded-md border-default-300 border-1 ",
              popoverContent: "rounded-md !text-tiny border-1 border-default-300 px-0 ",
              listbox: "p-0 rounded-none [&>*>*]:rounded-none [&>*>*]:px-3 [&>*>*>*]:text-tiny [&>*>*>*]:font-main",
              listboxWrapper: "rounded-md rounded-none",
            }}
          >
            {objectFitItems.map((item) => (
              <SelectItem key={item.key} value={item.value}>
                {item.label}
              </SelectItem>
            ))}
          </Select>
        </div>

        <div className={`py-3 space-y-2 ${isDisabled ? "text-[#87878a]" : "cursor-pointer"}`}>
          <p className="inline-flex items-center gap-unit-md  font-inherit !text-tiny font-normal">
            Size <Help content={"Adjusts the size of the branding logo."} />
          </p>
          <div className="flex gap-unit-md">
            <Input
              isDisabled={!isPreview || isDisabled}
              onChange={(value) => {
                const newValue = Math.max(180, Math.min(Number(value), 720));
                handleUpdateStore("maxWidth", newValue);
              }}
              min={180}
              max={720}
              value={(branding.settings.maxWidth ?? defaultSetting.maxWidth).toString()}
              placeholder={defaultSetting.maxWidth.toString()}
              className="w-full"
              type="number"
              startContent={
                <div className="w-unit-xs -translate-x-0.5">
                  <Arrow className="scale-[1.2]" />
                </div>
              }
              endContent={<span className="text-xs font-normal">px</span>}
            />
            <Input
              isDisabled={!isPreview}
              onChange={(value) => {
                const newValue = Math.max(48, Math.min(Number(value), 192));
                handleUpdateStore("maxHeight", newValue);
              }}
              min={48}
              max={192}
              value={(branding.settings.maxHeight ?? defaultSetting.maxHeight).toString()}
              placeholder={defaultSetting.maxHeight.toString()}
              className="w-full"
              type="number"
              startContent={
                <div className="w-unit-xs -translate-x-0.5">
                  <Arrow className="scale-[1.2] rotate-90" />
                </div>
              }
              endContent={<span className="text-xs font-normal">px</span>}
            />
          </div>
        </div>

        <div className={`py-3 space-y-2 ${isDisabled ? "text-[#87878a]" : "cursor-pointer"}`}>
          <p className="inline-flex items-center gap-unit-md  font-inherit !text-tiny font-normal">
            Position <Help content={"Adjusts the position of the branding logo within its container."} />
          </p>
          <div className="flex gap-unit-md">
            <Input
              isDisabled={!isPreview || isDisabled}
              onChange={(value) => {
                handleUpdateStore("positionX", Math.max(0, Math.min(value, 100)) / 100);
              }}
              min={0}
              max={100}
              value={Math.round((branding.settings.positionX ?? defaultSetting.positionX) * 100).toString()}
              placeholder={(defaultSetting.positionX * 100).toString()}
              className="w-full"
              type="number"
              startContent={
                <div className="w-unit-xs -translate-x-0.5">
                  <Arrow className="scale-[1.2]" />
                </div>
              }
              endContent={<span className="text-sm font-normal">%</span>}
            />
            <Input
              isDisabled={!isPreview || isDisabled}
              onChange={(value) => {
                handleUpdateStore("positionY", Math.max(0, Math.min(value, 100)) / 100);
              }}
              min={0}
              max={100}
              value={Math.round((branding.settings.positionY ?? defaultSetting.positionY) * 100).toString()}
              placeholder={(defaultSetting.positionY * 100).toString()}
              className="w-full "
              type="number"
              startContent={
                <div className="w-unit-xs -translate-x-0.5">
                  <Arrow className="scale-[1.2] rotate-90" />
                </div>
              }
              endContent={<span className="text-sm font-normal">%</span>}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const objectFitItems = [
  {
    key: "contain",
    value: "contain",
    label: "Contain",
  },
  {
    key: "cover",
    value: "cover",
    label: "Cover",
  },
  {
    key: "fill",
    value: "fill",
    label: "Fill",
  },
  {
    key: "scale-down",
    value: "scale-down",
    label: "Scale Down",
  },
];
export { BrandingSetting };
