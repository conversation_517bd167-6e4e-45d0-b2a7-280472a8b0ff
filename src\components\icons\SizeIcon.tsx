import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="15"
    height="12"
    viewBox="0 0 15 12"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    stroke="#2E2E2E"
    {...props}
  >
    <path
      d="M11.8919 0.990234H3.10811C1.94383 0.990234 1 1.85678 1 2.92572V9.05475C1 10.1237 1.94383 10.9902 3.10811 10.9902H11.8919C13.0562 10.9902 14 10.1237 14 9.05475V2.92572C14 1.85678 13.0562 0.990234 11.8919 0.990234Z"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.46387 6.1569V3.49023H6.09452M11.3693 5.82357V8.49023H8.73867"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default SvgComponent;
