import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={19}
    fill="none"
    {...props}
  >
    <path
      fill="currentColor"
      d="m6.77 16.365-1.054-1.053 1.942-1.973c-2.037-.271-3.74-.805-5.107-1.601C1.184 10.94.5 10.027.5 9c0-1.23.918-2.287 2.753-3.172C5.088 4.943 7.337 4.5 10 4.5c2.663 0 4.912.443 6.747 1.328S19.5 7.77 19.5 9c0 .88-.505 1.686-1.514 2.42-1.009.733-2.337 1.286-3.986 1.66v-1.53c1.283-.333 2.27-.746 2.962-1.238C17.654 9.822 18 9.383 18 9c0-.553-.712-1.19-2.137-1.915C14.438 6.362 12.483 6 10 6c-2.483 0-4.438.362-5.862 1.085C2.713 7.81 2 8.447 2 9c0 .438.49.945 1.467 1.52.979.574 2.283.993 3.914 1.257l-1.666-1.665L6.77 9.057l3.654 3.653-3.654 3.654Z"
    />
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M11 0H9.5v10.714L11 12.43V0Zm0 13.429-1.5 1.285V19H11v-5.571Z"
      clipRule="evenodd"
    />
  </svg>
)
export default SvgComponent
