import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlSpace="preserve"
    id="Icons"
    viewBox="0 0 32 32"
    fill="currentColor"
    {...props}
  >
    <style>
      {
        ".st0{fill:none;stroke:currentColor;stroke-width:2;stroke-linecap:round;strokeLinejoin:round;stroke-miterlimit:10}"
      }
    </style>
    <circle cx={16} cy={16} r={14} className="st0" />
    <ellipse cx={16} cy={16} className="st0" rx={6} ry={14} />
    <ellipse cx={16} cy={16} className="st0" rx={14} ry={6} />
  </svg>
)
export default SvgComponent
