import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={800}
    height={800}
    fill="none"
    viewBox="0 0 24 24"
    {...props}
  >
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M20.387 7.157 12 12 3.61 7.15M12 12v9"
    />
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M11 2.577a2 2 0 0 1 2 0l6.66 3.846a2 2 0 0 1 1 1.732v7.69a2 2 0 0 1-1 1.732L13 21.423a2 2 0 0 1-2 0l-6.66-3.846a2 2 0 0 1-1-1.732v-7.69a2 2 0 0 1 1-1.732L11 2.577Z"
    />
  </svg>
)
export default SvgComponent
