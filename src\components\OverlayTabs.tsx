import React, {
  FC,
  useState,
  useEffect,
  useRef,
  use<PERSON><PERSON>back,
  WheelEvent,
  MouseEvent,
} from "react";
import {
  Tabs,
  Tab,
  ScrollShadow,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
  Button,
  Tooltip,
} from "@nextui-org/react";
import { EditorOption } from "./EditorOption";
import PlusIcon from "./icons/Plus";
import { MaterialConfiguratorPlugin, Mesh, PickingPlugin, ViewerApp } from "webgi";
export interface Configurator {
  name: string;
  title?: string;
  type:string
  getOptions: () => ConfigOption[];
  isSelected: (option: ConfigOption) => boolean;
  applyOption: (option: ConfigOption) => Promise<void>;
  handleDelete?: (option: ConfigOption) => void;
  addCurrentMaterial?: () => void;
  handleOptionLabelChanged?: (option: ConfigOption, newName: string) => void;
}

export interface ConfigOption {
  name: string;
  icon?: string;
  [key: string]: any; 
}

export interface OverlayTabsProps {
  selectedTab: string;
  setSelectedTab: (tab: string) => void;
  isConfig?: boolean;
  configurators: Configurator[];
  setHasEdited?: (hasEdited: boolean) => void;
  selectedMesh?: Mesh;
  viewer?: ViewerApp;
  onEdit?: () => void;
  enableEdit?: boolean;
}


const OverlayTabs: FC<OverlayTabsProps> = ({
  selectedTab,
  setSelectedTab,
  isConfig,
  configurators,
  setHasEdited,
  selectedMesh,
  onEdit,
  viewer,
  enableEdit
}) => {
  const [loadingStates, setLoadingStates] = useState<{ [key: string]: {loading:boolean, option?: ConfigOption } | undefined}>({});
  const [contextMenuOpen, setContextMenuOpen] = useState(false);
  const [rightClickedTab, setRightClickedTab] = useState<Configurator | null>(null);
  const [rightClickedElement, setRightClickTarget] = useState<HTMLElement | null>();
  const optionsContainer = useRef<HTMLDivElement>(null);
  const handleOptionClick = async (configurator: Configurator, option: ConfigOption) => {
    setLoadingStates((prev) => ({ ...prev, [configurator.name]: {loading:true, option: option} }));
    await configurator.applyOption(option);
    setLoadingStates((prev) => ({ ...prev, [configurator.name]: {loading : false , option:option} }));
    setHasEdited?.(true);
  };

  //selectedTab is in the format of "configuratorName-type" so we need to find the configurator with the same name and type
  const selectedConfigurator = configurators.find((c) => c.name === selectedTab.split("-").slice(0,-1).join("-")
   && c.type === selectedTab.split("-").pop());

  const handleWheel = (e: WheelEvent) => {
    if (Math.abs(e.deltaY) < 10) return;
    optionsContainer.current?.scrollBy({ left: e.deltaY > 0 ? 100 : -100 });
  };

  const handleTabRightClick = (configurator: Configurator, event: MouseEvent) => {
    event.preventDefault();
    setRightClickedTab(configurator);
    setRightClickTarget(event.target as HTMLElement || undefined);
    setContextMenuOpen(true);
  };

  const handleEditClick = () => {
    if(!viewer) return;
    const picking = viewer.getPlugin(PickingPlugin)
    if(!picking) return;
    let mesh;
    viewer.traverseSceneObjects((obj : any) => {
      if(obj?.material?.name === rightClickedTab?.name && obj.visible){
        mesh = obj
      }
    })

    if(mesh){
      picking.setSelectedObject(mesh)
      onEdit?.()
    }
  }

  return (
    <div className="flex flex-col absolute overflow-hidden bottom-0 left-1/2 -translate-x-1/2 pointer-events-auto justify-center items-center">
      {selectedConfigurator && (
        <div className="flex max-w-[400px] overflow-hidden relative gap-unit-lg bg-white/50 rounded-large justify-around">
          <ScrollShadow
            onWheel={handleWheel}
            ref={optionsContainer}
            orientation="horizontal"
            hideScrollBar
            className="max-w-2xl p-unit-lg py-unit-sm h-full flex gap-unit-sm justify-start items-center"
          >
            {selectedConfigurator.getOptions().map((option, i) => (
              <EditorOption
                key={`${option.name}-${i}`+selectedConfigurator.name}
                selected={selectedConfigurator.isSelected(option)}
                loading={loadingStates[selectedConfigurator.name]?.loading &&
                   (loadingStates[selectedConfigurator.name]?.option?.name === option.name)}
                onClick={() => handleOptionClick(selectedConfigurator, option)}
                src={option.icon}
                className="h-unit-3xl w-unit-3xl shrink-0"
                name={option.label ||option.path?.split("/")?.pop() || option.name}
                onRemove={option.onRemove}
                editable
                handleLabelChanged={(newName : string) => selectedConfigurator.handleOptionLabelChanged?.(option, newName)}
                title={option.label}
              />
            ))}
            {selectedConfigurator.addCurrentMaterial && (selectedMesh?.material as any) &&
              <Tooltip content="Add current material" placement="top" offset={10}>
                <Button
                  key={"add-current-material" + selectedConfigurator.type}
                  onClick={selectedConfigurator.addCurrentMaterial}
                  className="h-8 w-8 aspect-square shrink-0 p-0 border-0"
                  disableRipple
                  variant="ghost"
                  color="primary"
                  isIconOnly
                  startContent={<PlusIcon className=""/>}
                />
              </Tooltip>
              }
          </ScrollShadow>
        </div>
      )}

      <Tabs
        isDisabled={isConfig}
        selectedKey={selectedTab}
        onSelectionChange={setSelectedTab as any}
        color="default"
        variant="solid"
        fullWidth
        className="mt-unit-lg"
        classNames={{
          tabList: [
            "!px-unit-lg py-unit-md rounded-large text-tiny bg-transparent w-full flex justify-center bg-white/60 w-fit",
          ],
          cursor: ["rounded-large"],
          tab: ["text-tiny"],
          base: ["justify-center"],
        }}
      >
        {configurators.map((configurator) => (
          <Tab
            key={configurator.name + "-" + configurator.type}
            title={configurator.title || configurator.name}
            onContextMenu={(event) => handleTabRightClick(configurator, event)}
            className="w-fit"
          />
        ))}
      </Tabs>

      {/* Context Menu for Right-Click on Tabs */}
      {rightClickedTab && rightClickedTab.handleDelete && enableEdit && (
        <Dropdown
          placement="top"
          offset={50}
          isOpen={contextMenuOpen}
          onOpenChange={setContextMenuOpen}
          closeOnSelect
          radius="md"
          triggerRef={rightClickedElement as any}
        >
          <DropdownTrigger>
            <div></div>
          </DropdownTrigger>
          <DropdownMenu
            aria-label="Tab actions"
            onAction={(key) => {
              if (key === "delete") {
                rightClickedTab.handleDelete?.(rightClickedTab);
              }else if(key === "edit"){
                handleEditClick()
              }
              setContextMenuOpen(false);
            }}
          >
            <DropdownItem key="delete" className="text-danger" color="danger">
              Delete
            </DropdownItem>
            <DropdownItem key="edit"  >
              Edit
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      )}
    </div>
  );
};

export { OverlayTabs };
