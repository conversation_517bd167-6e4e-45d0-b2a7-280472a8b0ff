import { FC, useState, useEffect} from "react";
import { SimpleBackgroundEnvUiPlugin2 } from "./viewer/SimpleBackgroundEnvUiPlugin2 ";
import { ViewerApp } from "webgi";
import { Checkbox } from "./Checkbox";
import useReactivePlugin from "./viewer/useReactivePlugin";
import { Slider } from "@nextui-org/react";

interface AdvancedSettingsProps {
  className?: string;
  viewer?: ViewerApp;
}

const AdvancedSettings: FC<AdvancedSettingsProps> = (props) => {
  const [plugin, setPlugin] = useState<SimpleBackgroundEnvUiPlugin2>();
  const reactivePluginInstance = useReactivePlugin(plugin) as SimpleBackgroundEnvUiPlugin2;

  useEffect(() => {
    if (!reactivePluginInstance) return;
    //check if dymanic query param is enabled
    const c = new URLSearchParams(window.location.search).get("cache");
    if (c === "false") {
      reactivePluginInstance.cache = false;
    } else {
      reactivePluginInstance.cache = true;
    }
  }, [reactivePluginInstance]);

  useEffect(() => {
    if (!props.viewer) return;
    let plugin = props.viewer.getPluginByType(SimpleBackgroundEnvUiPlugin2.PluginType) as SimpleBackgroundEnvUiPlugin2;
    setPlugin(plugin);
  }, [props.viewer]);

  const createUIOfFolder = (uiConfig: any, pluginRef: any, collection: any[] = []): any => {
    let elements = collection;

    const folderChildrens = uiConfig.children;

    for (const uiElement of folderChildrens) {
      if (typeof uiElement.hidden === "function" ? uiElement.hidden() : uiElement.hidden) continue;

      switch (uiElement.type) {
        // case 'color':
        //   return <ColorPicker value={pluginRef[uiElement.property[1]]} onChange={(value) => pluginRef[uiElement.property[1]] = value} />;
        case "folder":
          createUIOfFolder(uiElement, pluginRef, elements);
          break;
        case "text":
          elements.push(
            <p key={uiElement.label} className={uiElement.className ?? "mt-unit-8"}>
              {uiElement.label}
            </p>
          );
          break;
        case "slider":
          const slider = (
            <Slider
              aria-label={uiElement.label}
              className={uiElement.className ?? "mt-unit-8"}
              key={uiElement.label}
              value={pluginRef[uiElement.property[1]]}
              label={uiElement.label}
              minValue={uiElement.bounds[0]}
              maxValue={uiElement.bounds[1]}
              onChange={(value) => (pluginRef[uiElement.property[1]] = value)}
              step={uiElement.stepSize}
              classNames={{ label: "text-tiny", value: "text-tiny" }}
              size="sm"
            />
          );
          elements.push(slider);
          break;
        case "checkbox":
          const checkbox = (
            <Checkbox
              className={uiElement.className ?? "m-[0px] mt-2 pl-0" }
              key={uiElement.label}
              label={uiElement.label}
              isSelected={pluginRef[uiElement.property[1]]}
              onValueChange={uiElement.onChange ?? ((checked: boolean) => (pluginRef[uiElement.property[1]] = checked))}
            />
          );
          elements.push(checkbox);
          break;
        // Handle other types and potentially nested folders
        default:
          break; // or a placeholder component
      }
    }

    return elements;
  };

  return <div className="flex flex-col w-full font-inter !font-normal">{reactivePluginInstance && createUIOfFolder(reactivePluginInstance?.uiConfig, reactivePluginInstance)}</div>;
};
export { AdvancedSettings };
