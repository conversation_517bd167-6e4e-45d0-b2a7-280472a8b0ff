import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { But<PERSON> } from "./Button";
import { Img } from "./Img";
import { Chip, Select, SelectItem, Spinner } from "@nextui-org/react";
import { Button as NextButton } from "@nextui-org/react";
import toast from "@minieditor/react-hot-toast";
import noimageIcon from "../assets/no_image.jpg?inline";
import Snap from "./icons/Snap";
import { ViewerApp, CanvasSnipperPlugin, PickingPlugin } from "webgi";
import { processImage } from "./utils/processImage";
import { EditorSettings, Project } from "../main";
import { IjewelEditorPlugin } from "./viewer/IjewelEditorPlugin";
import { ListItemNode, ListNode } from "@lexical/list";
import { Input as NextInput } from "@nextui-org/react";
import { useProject } from "./provider/ProjectProvider";
import CloseIcon from "./icons/Close";
import ArrowIcon from "./icons/ArrowRight";
import { getRandomPlaceholder } from "./utils/functions";
import { TextEditor } from "../../TextEditor";
import { type EditorState } from "lexical/LexicalEditorState";
import { useLexicalEditorHtml } from "../../TextEditor";

interface ProjectSettingsProps {
  project: Project;
  viewer: ViewerApp | undefined;
  editorSettings?: EditorSettings;
  setHasEdited: (value: boolean) => void;
  hasEdited: boolean;
}

const ProjectSettings: React.FC<ProjectSettingsProps> = ({
  project,
  viewer,
  editorSettings,
  setHasEdited,
}) => {
  const [saving, setSaving] = useState(false);
  const [poster, setPoster] = useState<string>();
  const [loadingPoster, setLoadingPoster] = useState(false);
  const { setProject } = useProject();
  const [ijewelEditorPlugin, setIjewelEditorPlugin] =
    useState<IjewelEditorPlugin>();

const initPoster = project.posterUrl ?? project.poster_url
  useEffect(() => {
    setPoster(initPoster);
  }, [initPoster]);

  useEffect(() => {
    if (!viewer) return;

    // viewer.canvas.addEventListener("click", () => {
    //   hideProjectSettings();
    // });

    (async () => {
      const editorPlugin = await viewer.getPluginByType<IjewelEditorPlugin>(
        "IjewelEditorPlugin"
      )!;
      setIjewelEditorPlugin(editorPlugin);
    })();
  }, [viewer]);

  const snapPoster = async () => {
    if (!viewer || !project) return;

    setSaving(true);
    setHasEdited(true);
    setLoadingPoster(true);

    const pickingPlugin = viewer.getPluginByType("Picking") as PickingPlugin;
    const currentMesh = pickingPlugin.getSelectedObject();

    if (currentMesh) pickingPlugin.setSelectedObject(undefined);

    viewer.doOnce("postFrame", async () => {
      const snipper = (await viewer.getPluginByType("CanvasSnipper")) as CanvasSnipperPlugin;
      const dataurl = await processImage((await snipper.getFile())!, 512, 360);
      const cameraConfig = viewer.scene.activeCamera.toJSON();

      const updateProject:any = { cameraConfig }
      if (editorSettings?.onPosterUpdate) {
        await editorSettings.onPosterUpdate(dataurl, cameraConfig).then(data => {
          if (data?.poster_url) {
            updateProject.posterUrl = data.poster_url;
          }
          if (data?.project_data?.cameraConfig) {
            updateProject.cameraConfig = data.project_data.cameraConfig
          }
        })
      } else{
        updateProject.posterUrl = dataurl
      }

      setPoster(dataurl);
      setLoadingPoster(false);
      setSaving(false);

      setProject({ ...project, ...updateProject });

      if (currentMesh) pickingPlugin.setSelectedObject(currentMesh) as any;

    });
  };

  const hideProjectSettings = useCallback(() => {
    document
      .getElementById("project-settings")
      ?.classList.add("w-0", "overflow-hidden");
    document
      .getElementById("webgi-mini-editor-canvas-container")
      ?.classList.add("ml-2");
    document.getElementById("project-settings")?.classList.remove("w-auto");
    document.getElementById("project-settings")?.classList.add("hidden");
    document.getElementById("menu-button")?.classList.remove("-left-unit-9xl");
    document.getElementById("menu-button")?.classList.add("left-4");
  }, []);



  return (
    <div className="w-unit-8xl h-full overflow-y-auto scrollbar-hide flex flex-col gap-3">
      <div className="flex items-center gap-unit-sm h-unit-2xl -translate-x-2">
        <NextButton
          variant="light"
          className="!w-unit-md px-0"
          size="sm"
          color="default"
          // className="translate-x-4"
          onClick={hideProjectSettings}
          // isIconOnly
          startContent={
            <div>
              <ArrowIcon className="scale-110" />
            </div>
          }
        />
        <p className="text-sm font-mainSemiBold text-default-foreground">
          Project Settings
        </p>
        <div className="flex-1" />
      </div>
      {!editorSettings?.hidePoster && (
        <div className="flex gap-3 flex-col">
          <div className="relative">
            {loadingPoster && (
              <Spinner className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2" />
            )}
            <Img
              src={poster || noimageIcon}
              className={`rounded-small h-unit-6xl w-full object-cover ${
                loadingPoster ? "opacity-50" : ""
              }`}
            ></Img>
          </div>
          <Button
            disabled={loadingPoster || saving}
            endContent={<Snap />}
            color="primary"
            size="lg"
            className="text-sm font-inter font-semibold text-center rounded-full gap-2.5 py-4"
            fullWidth
            onClick={snapPoster}
          >Update Poster</Button>
        </div>
      )}

      <TextEditor
        initialState={
          project.textEditorState ?? {
            name: project.name,
            description: project.description,
          }
        }
        onChange={(editorNewState, projectData) => {
          const { name, description } = projectData;
          setProject((prev) => ({
            ...prev,
            name,
            description,
            textEditorState: name ? editorNewState.toJSON() : undefined,
          }));
          setHasEdited(true);
        }}
      />
      {!editorSettings?.hideLogoInput && (
        <div className="flex flex-col gap-unit-sm">
          <p className="text-sm font-normal text-default-500 pl-unit-md">
            Logo
          </p>
          <NextInput
            value={project.logo as any}
            onValueChange={(v: any) => {
              setProject((prev) => ({ ...prev, logo: v }));
            }}
            // label="Logo"
            className="placeholder:text-tiny"
            classNames={{
              // innerWrapper: "h-unit-2xl",
              input:
                "h-unit-2xl placeholder:text-sm pl-unit-md placeholder:text-default-400",
              innerWrapper: "h-unit-2xl",
              base: "shadow-none ",
              mainWrapper:
                "bg-[#f7f7f7]  !rounded-large  border-[#e7e9e9] border-1 shadow-none",
              inputWrapper: "bg-[#f7f7f7] shadow-none rounded-large h-unit-3xl",
            }}
            type="text"
            placeholder="Add a logo url"
          />
        </div>
      )}
      {editorSettings?.showCategory && (
        <Select
          // isDisabled={freeze}
          aria-label="category"
          defaultSelectedKeys={
            project.category ? [project.category] : undefined
          }
          // defaultSelectedKeys={["png"]}
          placeholder="Choose a category"
          size="sm"
          onSelectionChange={(v: any) =>
            setProject((prev) => ({ ...prev, category: [...v][0] }))
          }
          classNames={{
            mainWrapper: "rounded-large",
            popoverContent: "rounded-small !text-tiny ",
            value: "font-normal text-sm  text-default-400 ",
            trigger:
              "bg-[#f7f7f7] !h-unit-3xl !rounded-small  py-unit-xl border-[#e7e9e9] border-1 shadow-none",
            listbox: "p-0  [&>*]:px-unit-",
            listboxWrapper: "rounded-small",
          }}
        >
          {(editorSettings.categories || categories).map((item) => (
            <SelectItem key={item}>{item}</SelectItem>
          ))}
        </Select>
      )}
      {editorSettings?.showTags && (
        <Tags categories={editorSettings.categories || categories} />
      )}
      <div className="flex flex-col text-small ">
        {/* <p className="text-small font-normal text-default mt-unit-xl">
          Help
        </p>
        <Point className="text-tiny mt-unit-lg">
          Сlick on the object you want to change the material, then select
          material from the menu
        </Point> */}
      </div>
    </div>
  );
};

const Tags: React.FC<{ categories: string[] }> = ({ categories }) => {
  const { project, setProject } = useProject();
  const [tag, setTag] = useState<string>("");
  // project.tags = ["tag1", "tag2", "tag3"];

  const addTag = useCallback(
    (e: KeyboardEvent) => {
      if (e.key !== "Enter") return;

      // count tags not start with '_'
      const tagsCount = (project.tags ?? []).filter((tag) => !tag.startsWith("_")).length ?? 0;

      if (project.tags && tagsCount >= 5) {
        toast.error("You can add only 5 tags");
        return;
      }
      if (tag.trim() === "") return;
      if (tag.trim().length > 20) {
        toast.error("Tag length should be less than 40 characters");
        return;
      }
      if (tag.trim().length < 2) {
        toast.error("Tag length should be at least 2 characters");
        return;
      }
      if (tag.startsWith("_")) {
        toast.error("Tag should not start with _");
        return;
      }
      const tags = project.tags || [];
      tags.push(tag.trim());
      setProject({ ...project, tags });
      setTag("");
    },
    [project, setProject, tag]
  );

  const deleteTag = useCallback(
    (index: number) => {
      const tags = project.tags || [];
      tags.splice(index, 1);
      setProject({ ...project, tags });
    },
    [project, setProject]
  );

  return (
    <div className="flex flex-col gap-unit-lg">
      <NextInput
        value={tag}
        onKeyDown={addTag as any}
        onValueChange={(v) => setTag(v)}
        size="sm"
        // label="Title*"
        // className="placeholder:text-s,a"
        classNames={{
          // innerWrapper: "h-unit-2xl",
          input:
            "h-unit-2xl placeholder:text-md !placeholder:font-normal !text-sm placeholder:text-default-400",
          innerWrapper: "h-unit-2xl",
          base: "shadow-none ",
          mainWrapper:
            "bg-[#f7f7f7]  !rounded-small  border-[#e7e9e9] border-1 shadow-none",
          inputWrapper: "bg-[#f7f7f7] shadow-none rounded-small h-unit-3xl",
        }}
        type="text"
        placeholder="Add tags"
      />
      {project.tags && (
        <div className="w-full flex flex-wrap gap-unit-md px-unit-sm">
          {project.tags
            ?.filter(
              (tag) => tag && !categories.includes(tag) && !tag?.startsWith("_")
            )
            .map((tag, i) => (
              <Chip
                key={tag + i}
                color="primary"
                variant="faded"
                classNames={{
                  base: "bg-[#f7f7f7] rounded-small border-1",
                  content: "text-sm font-normal inline-block min-w-20",
                }}
                endContent={
                  <CloseIcon
                    onClick={() => deleteTag(i)}
                    className="mx-unit-xs cursor-pointer"
                  />
                }
              >
                {tag}
              </Chip>
            ))}
        </div>
      )}
    </div>
  );
};

const TextEditorThem = {
  code: "text-editor-code",
  heading: {
    h1: "text-editor-heading-h1",
    h2: "text-editor-heading-h2",
    h3: "text-editor-heading-h3",
    h4: "text-editor-heading-h4",
    h5: "text-editor-heading-h5",
  },
  image: "text-editor-image",
  link: "text-editor-link",
  list: {
    listitem: "text-editor-listitem",
    nested: {
      listitem: "text-editor-nested-listitem",
    },
    ol: "text-editor-list-ol",
    ul: "text-editor-list-ul",
  },
  ltr: "ltr",
  paragraph: "text-editor-paragraph",
  placeholder: "text-editor-placeholder text-editor-first-line",
  quote: "text-editor-quote",
  rtl: "rtl",
  text: {
    bold: "text-editor-text-bold",
    code: "text-editor-text-code",
    hashtag: "text-editor-text-hashtag",
    italic: "text-editor-text-italic",
    overflowed: "text-editor-text-overflowed",
    strikethrough: "text-editor-text-strikethrough",
    underline: "text-editor-text-underline",
    underlineStrikethrough: "text-editor-text-underlineStrikethrough",
  },
};

const textEditorConfig = {
  namespace: "React.js Demo",
  nodes: [ListNode, ListItemNode],
  // Handling of errors during update
  onError(error: Error) {
    throw error;
  },
  // The editor theme
  theme: TextEditorThem,
};

const categories = [
  "Rings",
  "Earrings",
  "Necklaces",
  "Bracelets",
  "Watches",
  "Other",
];

export default ProjectSettings;
