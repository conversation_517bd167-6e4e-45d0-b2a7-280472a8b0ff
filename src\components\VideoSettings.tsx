import { FC, useState, useCallback } from "react";
import {
  CanvasRecorderPlugin,
  downloadBlob,
  PopmotionPlugin,
  RendererUiPlugin,
  timeout,
  ViewerApp,
} from "webgi";
import { Input } from "./Input";
import Arrow from "./icons/Arrow";
import Fps from "./icons/Fps";
import Clock from "./icons/Clock";
import { Button } from "./Button";
import toast from "@minieditor/react-hot-toast";
import { useUi } from "./provider/UiProvider";

interface VideoSettingsProps {
  viewer?: ViewerApp;
}

const VideoSettings: FC<VideoSettingsProps> = ({ viewer }) => {
  const [videoWidth, setVideoWidth] = useState(1280);
  const [videoHeight, setVideoHeight] = useState(720);
  const [fps, setFps] = useState(30);
  const [duration, setDuration] = useState(5);
  const [loading, setLoading] = useState(false);
  const { freeze, setFreeze , limits } = useUi();
  const [isWidthValid, setIsWidthValid] = useState(true);
  const [isHeightValid, setIsHeightValid] = useState(true);
  const [isFpsValid, setIsFpsValid] = useState(true);
  const [isDurationValid, setIsDurationValid] = useState(true);

  const handleVideo = useCallback(async () => {
    if (!viewer) {
      toast.error("Viewer not found");
      return;
    }
    
    setFreeze(true);
    setLoading(true);
    toast.loading("Exporting video", { id: "exporting-video" });
    await viewer.getOrAddPlugin(PopmotionPlugin);
    const recorder = await viewer.getOrAddPlugin(CanvasRecorderPlugin)!;
    const p = viewer.getPlugin(RendererUiPlugin);
    if(p) p.enabled = false;
    await viewer.doOnce('postFrame')
    viewer.setSize({ width : videoWidth, height : videoHeight });
    recorder.videoFrameRate = fps;
    await viewer.doOnce("postFrame");
    const blob = await recorder.recordCamera360(duration);
    if (!blob) {
      toast.error("Failed to record video");
      setLoading(false);
      setFreeze(false);
      if(p) p.enabled = true;
      return;
    }
    downloadBlob(blob, `ijewel.mp4`);
    viewer.setSize();
    setLoading(false);
    setFreeze(false);
    if (p) p.enabled = true;
    toast.success("Video exported successfully", { id: "exporting-video" });
  }, [viewer, videoWidth, videoHeight, fps, duration]);

  return (
    <div className="flex flex-col gap-unit-lg w-full">
      <p className="text-primary font-mainSemiBold text-[13px] text-center whitespace-nowrap">
        Turntable video
      </p>
      <p className="font-inherit !text-tiny font-normal">Size</p>
      <div className="flex gap-unit-md">
        <Input
          isDisabled={freeze}
          onChange={setVideoWidth}
          value={videoWidth.toString()}
          max={limits?.video?.maxResolution?.width}
          setIsValid={setIsWidthValid}
          placeholder="1280"
          className="w-full"
          type="number"
          startContent={
            <div className="w-unit-xs -translate-x-0.5">
              <Arrow className="scale-[1.2]" />
            </div>
          }
        />
        <Input
          isDisabled={freeze}
          onChange={setVideoHeight}
          value={videoHeight.toString()}
          max={limits?.video?.maxResolution?.height}
          setIsValid={setIsHeightValid}
          placeholder="720"
          className="w-full"
          type="number"
          startContent={
            <div className="w-unit-xs -translate-x-0.5">
              <Arrow className="scale-[1.2] rotate-90" />
            </div>
          }
        />
      </div>
      <div className="gap-unit-md grid grid-cols-2">
        <div className="flex flex-col gap-unit-md">
          <p className="font-inherit !text-tiny font-normal">FPS</p>
          <Input
            isDisabled={freeze}
            onChange={setFps}
            value={fps.toString()}
            max={limits?.video?.maxFPS}
            setIsValid={setIsFpsValid}
            placeholder="30"
            className="w-full"
            type="number"
            startContent={
              <div className="w-unit-xl">
                <Fps className="scale-[1] " />
              </div>
            }
          />
        </div>
        <div className="flex flex-col gap-unit-md">
          <p className="font-inherit !text-tiny font-normal">Duration</p>
          <Input
            isDisabled={freeze}
            onChange={setDuration}
            value={duration.toString()}
            max={limits?.video?.maxDurationInSeconds}
            setIsValid={setIsDurationValid}
            placeholder="5"
            className="w-full"
            type="number"
            startContent={
              <div className="w-unit-md">
                <Clock className="w-full scale-150 h-fit" />
              </div>
            }
          />
        </div>
      </div>
      <Button
        disabled={!isDurationValid || !isFpsValid || !isWidthValid || !isHeightValid || loading || freeze}
        onClick={handleVideo}
        name={"Download .mp4"}
        size="lg"
        color="primary"
        className="!text-tiny rounded-full !font-mainSemiBold"
      />
    </div>
  );
};

export { VideoSettings };
