import { SVGProps } from "react";

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M8.36442 9.16665C8.53702 9.16665 8.69847 9.25174 8.79607 9.3941L9.67507 10.6767L11.1665 11.1163C11.3321 11.1651 11.4629 11.2924 11.5162 11.4565C11.5695 11.6207 11.5385 11.8005 11.4333 11.9373L10.4851 13.1696L10.5278 14.7239C10.5326 14.8964 10.4519 15.0602 10.3123 15.1616C10.1727 15.2631 9.9921 15.2892 9.82947 15.2313L8.36442 14.7104L6.89944 15.2313C6.73681 15.2892 6.55618 15.2631 6.41658 15.1616C6.2769 15.0602 6.19631 14.8964 6.20105 14.7239L6.2438 13.1696L5.29562 11.9373C5.19038 11.8005 5.15935 11.6207 5.21268 11.4565C5.26601 11.2924 5.39684 11.1651 5.56237 11.1163L7.05384 10.6767L7.93284 9.3941C8.03037 9.25174 8.19188 9.16665 8.36442 9.16665ZM8.36442 10.6155L7.81186 11.4218C7.74384 11.5211 7.64365 11.5938 7.52819 11.6279L6.59065 11.9043L7.18667 12.679C7.26007 12.7743 7.29837 12.8921 7.29502 13.0124L7.26816 13.9895L8.18909 13.662C8.30254 13.6217 8.42637 13.6217 8.53975 13.662L9.46075 13.9895L9.43389 13.0124C9.43054 12.8921 9.46884 12.7743 9.54224 12.679L10.1383 11.9043L9.20072 11.6279C9.08526 11.5938 8.98507 11.5211 8.91705 11.4218L8.36442 10.6155Z"
    />
    <path d="M19.5499 5C19.5499 7.03296 18.052 8.71622 16.0997 9.00607V11.3027C16.0996 11.4267 16.063 11.5468 15.9952 11.6484L15.917 11.7432L8.24321 19.417C8.03011 19.6301 7.70098 19.6571 7.45903 19.4971L7.36138 19.417L1.08306 13.1387C0.839664 12.8953 0.839664 12.5002 1.08306 12.2568L8.75689 4.58301L8.85161 4.50488C8.9532 4.43704 9.07338 4.40048 9.19732 4.40039H15.4766L15.6026 4.41309C15.8865 4.47129 16.0997 4.72233 16.0997 5.02344V7.8891C17.4414 7.61198 18.4502 6.42372 18.4502 5C18.4502 3.42159 17.2102 2.1326 15.6514 2.05371L15.5 2.0498C14.6872 2.04986 13.9512 2.37858 13.417 2.91113L13.3135 3.01953C13.135 3.21615 12.846 3.25429 12.626 3.12402L12.5362 3.05762C12.3115 2.85358 12.2954 2.50615 12.4991 2.28125L12.6417 2.13184C13.3735 1.40245 14.3842 0.950257 15.5 0.950195L15.7081 0.955078C17.8481 1.06344 19.5499 2.83307 19.5499 5ZM13.9992 7.54041C14.2608 7.69529 14.5482 7.81088 14.8536 7.87914V5.64648H9.4561L2.40435 12.6973L7.80181 18.0947L8.05962 17.8389L14.8536 11.0439V8.99843C14.3101 8.91121 13.8027 8.71592 13.3543 8.43545C13.2442 8.47716 13.1248 8.5 13 8.5C12.4478 8.5 12 8.05228 12 7.5C12 6.94772 12.4478 6.5 13 6.5C13.5523 6.5 14 6.94772 14 7.5C14 7.51353 13.9998 7.527 13.9992 7.54041Z" />
  </svg>
);
export default SvgComponent;
